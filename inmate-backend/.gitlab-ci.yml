# stages:
#   - pull_repo
#   - sync_functions

# variables:
#   REPO_PATH: "/home/<USER>/inmate-backend"
#   FUNCTION_SRC: "/home/<USER>/inmate-backend/supabase/functions"
#   FUNCTION_DEST: "/mnt/NFS_DEV_ENV/supabaseinmatefunction"
#   BRANCH: "dev"

# before_script:
#   - echo "Starting pipeline execution on $(hostname)"

# pull_repo:
#   stage: pull_repo
#   tags:
#     - uat_apps
#   variables:
#     TARGET_BRANCH: "dev"  # Ensure TARGET_BRANCH is properly set
#   script:
#     - echo "Starting pipeline execution on $(hostname)"
#     - whoami
#     - pwd
#     - echo "Listing contents of $REPO_PATH before execution:"
#     - ls -lah "$REPO_PATH" || echo "Directory does not exist"
#     - |
#       if [ -d "$REPO_PATH/.git" ]; then
#         echo "Git repository found. Updating repository..."
#         cd "$REPO_PATH"
#         git fetch origin "$TARGET_BRANCH"
#         git checkout "$TARGET_BRANCH" || git checkout -b "$TARGET_BRANCH"
#         git pull origin "$TARGET_BRANCH"
#       else
#         echo "No Git repository found. Cloning fresh..."
#         git clone --branch "$TARGET_BRANCH" https://gitlab.variiance.com/backend/inmate-backend.git "$REPO_PATH"
#       fi
#   only:
#     - dev

# sync_functions:
#   stage: sync_functions
#   tags:
#     - uat_apps
#   script:
#     - echo "Syncing Supabase functions..."
#     - echo "Deleting old files in $FUNCTION_DEST before copying..."
#     - find "$FUNCTION_SRC" -type f -exec rm -f "$FUNCTION_DEST/{}" \;
#     - sudo rsync -av "$FUNCTION_SRC/" "$FUNCTION_DEST/"
#     - chown -R $(whoami):$(whoami) "$FUNCTION_DEST/" || echo "Skipping chown due to permission issues"
#   only:
#     - dev
#   needs:
#     - pull_repo 


stages:
  - deploy

deploy_dev:
  stage: deploy
  tags:
    - inmateback
  script:
    - echo "Deploying on local server with runner 'inmateback'..."

    # Step 1: Pull latest code
    - cd /root/inmate-backend
    - git checkout dev
    - git pull

    # Step 2: Copy Supabase Edge Functions
    - echo "Copying Supabase functions..."
    - mkdir -p /root/supabase-project/volumes/functions/
    - cp -ru supabase/functions/* /root/supabase-project/volumes/functions/

    # Step 3: Copy env.dev to .env.inmate
    - echo "Updating .env.inmate..."
    - cp -f .gitlab/env.dev /root/supabase-project/.env.inmate

    # Step 4: Restart Supabase Edge Functions container
    - echo "Restarting Supabase Edge Functions container..."
    - cd /root/supabase-project/
    - docker compose down && docker compose up -d 
