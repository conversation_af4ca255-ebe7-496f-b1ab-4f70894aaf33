# "Test Meeting" Edge Function

This document provides instructions on how to use and test the `testMeeting` Supabase Edge Function.

## Overview

The `testMeeting` function is designed to create a temporary video conference room for testing purposes. It checks for room availability, creates a new meeting record in the database, and provisions a new room with LiveKit.

This is useful for allowing users to test their connection and device setup before a real, scheduled visit.

## Endpoint

- **Method:** `POST`
- **URL:** `/functions/v1/testMeeting`

## Authorization

This function does not use JWT-based authorization. Instead, it should be protected by a secret key when deployed. For local development, JWT verification can be disabled.

## Request

### Headers

| Header          | Value              |
| --------------- | ------------------ |
| `Content-Type`  | `application/json` |

### Body

The request body must be a JSON object containing the `roomId` for the test.

```json
{
  "roomId": "a1b2c3d4-e5f6-7890-1234-567890abcdef"
}
```

| Field    | Type     | Description                                  |
| -------- | -------- | -------------------------------------------- |
| `roomId` | `string` | **Required.** The UUID of the room to be used for the test meeting. |

## Responses

### Success Response (200 OK)

On success, the function returns the newly created test meeting object.

```json
{
  "data": {
    "id": "f1e2d3c4-b5a6-7890-1234-567890abcdef",
    "created_at": "2025-07-03T12:00:00Z",
    "visitor_id": "...",
    "inmate_id": "...",
    "datetime": "2025-07-03T12:00:00Z",
    "duration": 15,
    "status_id": 5,
    "archived": false,
    "room_id": "a1b2c3d4-e5f6-7890-1234-567890abcdef",
    "test": true,
    "meeting_url": "wss://your-livekit-host.com"
  },
  "message": "Test Meeting Got successfully"
}
```

### Error Responses

- **400 Bad Request:** Invalid request body (e.g., missing `roomId`).
- **405 Method Not Allowed:** If the request method is not `POST`.
- **471 Conflict:** The requested time slot conflicts with an existing scheduled visit.
- **472 Conflict:** The requested time slot conflicts with another active test meeting.
- **500 Internal Server Error:** A general server-side error occurred.

## Environment Variables

To run this function, you must have a `supabase/.env.local` file with the following variables:

| Variable                             | Description                                          | Example                               |
| ------------------------------------ | ---------------------------------------------------- | ------------------------------------- |
| `SUPABASE_URL`                       | Your Supabase project URL.                           | `http://127.0.0.1:54321`               |
| `SUPABASE_ANON_KEY`                  | Your Supabase anonymous key.                         | `your-anon-key`                       |
| `LIVEKIT_HTTP_HOST`                  | The host URL for the LiveKit server.                 | `http://localhost:7880`               |
| `LIVEKIT_API_KEY`                    | Your LiveKit API key.                                | `API...`                              |
| `LIVEKIT_API_SECRET`                 | Your LiveKit API secret.                             | `your-secret`                         |
| `VISIT_REQUEST_STATUS_APPROVED`      | The status ID for an "Approved" visit.               | `2`                                   |
| `VISIT_REQUEST_STATUS_RUNNIING`      | The status ID for a "Running" visit.                 | `5`                                   |
| `VISIT_REQUEST_STATUS_COMPLETED`     | The status ID for a "Completed" visit.               | `6`                                   |
| ... (and other status IDs)           | ...                                                  | ...                                   |

## Local Development

1.  **Ensure Docker is running.**
2.  **Start the Supabase services:**
    ```bash
    npx supabase start
    ```
3.  **Serve the function:**
    Run the following command from the root of your `inmate-backend` project to serve the function, load environment variables, and disable JWT verification for easy testing.
    ```bash
    npx supabase functions serve testMeeting --env-file ./supabase/.env.local --no-verify-jwt
    ```
4.  **Send a request:**
    Use a tool like `curl` or an API client to send a `POST` request to the local function URL displayed in the terminal.
    ```bash
    curl -X POST http://127.0.0.1:54321/functions/v1/testMeeting \
      -H "Content-Type: application/json" \
      -d '{"roomId": "your-room-uuid-here"}'
    ```
