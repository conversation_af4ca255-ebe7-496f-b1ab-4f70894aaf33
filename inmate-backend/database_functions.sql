-- Migration script for PostgreSQL functions and triggers
BEGIN;

-- Drop existing triggers first (if they exist)
DROP TRIGGER IF EXISTS trigger_set_banned_until ON public.users;
-- DROP TRIGGER IF EXISTS log_inmates_changes ON public.inmates;
-- DROP TRIGGER IF EXISTS log_visitors_changes ON public.visitors;
-- DROP TRIGGER IF EXISTS log_notifications_changes ON public.notifications;
-- DROP TRIGGER IF EXISTS log_visit_requests_changes ON public.visit_requests;
-- DROP TRIGGER IF EXISTS log_users_changes ON public.users;
-- DROP TRIGGER IF EXISTS log_roles_changes ON public.roles;
-- DROP TRIGGER IF EXISTS log_permissions_changes ON public.permissions;
-- DROP TRIGGER IF EXISTS log_role_permissions_changes ON public.role_permissions;
-- DROP TRIGGER IF EXISTS log_user_permissions_changes ON public.user_permissions;
-- DROP TRIGGER IF EXISTS log_sections_changes ON public.sections;
-- DROP TRIGGER IF EXISTS log_section_availability_settings_changes ON public.section_availability_settings;
-- DROP TRIGGER IF EXISTS log_availability_times_changes ON public.availability_times;
-- DROP TRIGGER IF EXISTS log_inmate_visitors_changes ON public.inmate_visitors;
-- DROP TRIGGER IF EXISTS log_inmate_visitor_documents_changes ON public.inmate_visitor_documents;
-- DROP TRIGGER IF EXISTS log_rooms_changes ON public.rooms;
-- DROP TRIGGER IF EXISTS log_room_status_changes ON public.room_status;
-- DROP TRIGGER IF EXISTS log_room_availability_settings_changes ON public.room_availability_settings;
-- DROP TRIGGER IF EXISTS log_inmate_availability_settings_changes ON public.inmate_availability_settings;
-- DROP TRIGGER IF EXISTS log_inmate_settings_changes ON public.inmate_settings;
-- DROP TRIGGER IF EXISTS log_meeting_records_changes ON public.meeting_records;
-- DROP TRIGGER IF EXISTS log_meeting_feedbacks_changes ON public.meeting_feedbacks;
-- DROP TRIGGER IF EXISTS log_room_slots_changes ON public.room_slots;
-- DROP TRIGGER IF EXISTS log_visit_requests_status_changes ON public.visit_requests_status;
-- DROP TRIGGER IF EXISTS log_inmates_status_changes ON public.inmates_status;
-- DROP TRIGGER IF EXISTS log_settings_changes ON public.settings;
DROP TRIGGER IF EXISTS trigger_disable_auth_user ON public.users;
-- DROP TRIGGER IF EXISTS trigger_visit_status_change ON public.visit_requests;
-- DROP TRIGGER IF EXISTS trigger_update_visit ON public.visit_requests;
-- DROP TRIGGER IF EXISTS trigger_update_from_to ON public.visit_requests;
-- DROP TRIGGER IF EXISTS trigger_update_first_response_time ON public.visit_requests;
-- Drop all user-defined triggers
-- DO $$
-- DECLARE
-- r RECORD;
-- BEGIN
-- FOR r IN
-- SELECT event_object_table AS table_name, trigger_name
-- FROM information_schema.triggers
-- WHERE trigger_schema NOT IN ('pg_catalog', 'information_schema')
--     LOOP
--         EXECUTE format('DROP TRIGGER IF EXISTS %I ON %I CASCADE;', r.trigger_name, r.table_name);
-- END LOOP;
-- END$$;
--
-- -- Drop specific known trigger if needed
DROP TRIGGER IF EXISTS handle_public_user ON auth.users;
-- DROP TRIGGER IF EXISTS trigger_set_banned_until ON auth.users;
-- -- Drop all user-defined functions, excluding system and extension ones
-- DO $$
-- DECLARE
-- r RECORD;
-- BEGIN
-- FOR r IN
-- SELECT p.nspname AS routine_schema, p.proname AS routine_name,
--        oidvectortypes(p.proargtypes) AS args
-- FROM pg_proc p
--          JOIN pg_namespace n ON n.oid = p.pronamespace
-- WHERE n.nspname NOT IN ('pg_catalog', 'information_schema')
--   AND p.proisagg = false
--   AND NOT EXISTS (
--     SELECT 1 FROM pg_depend d
--                       JOIN pg_extension e ON d.refobjid = e.oid
--     WHERE d.objid = p.oid
-- )
--     LOOP
--         EXECUTE format('DROP FUNCTION IF EXISTS %I.%I(%s) CASCADE;',
--                        r.routine_schema, r.routine_name, r.args);
-- END LOOP;
-- END$$;
-- --
-- DROP FUNCTION IF EXISTS public.disable_auth_user_on_delete();
-- DROP FUNCTION IF EXISTS public.set_banned_until();
-- DROP FUNCTION IF EXISTS public.sync_all_auth_users();
-- DROP FUNCTION IF EXISTS public.get_visitor_requests_with_relation(uuid);
-- DROP FUNCTION IF EXISTS public.assign_room_to_visit();
-- DROP FUNCTION IF EXISTS public.approve_visit_if_whitelisted();
-- DROP FUNCTION IF EXISTS public.notify_visit_status_change();
-- DROP FUNCTION IF EXISTS public.get_admin_requests_with_meeting_records();
-- DROP FUNCTION IF EXISTS public.get_admin_requests_with_meeting_records_not_archived();
-- DROP FUNCTION IF EXISTS public.remove_quotes_from_names();
-- DROP FUNCTION IF EXISTS public.delete_user_from_public();
-- DROP FUNCTION IF EXISTS public.update_from_to();
-- DROP FUNCTION IF EXISTS public.get_visitor_requests_with_meeting_records();
-- DROP FUNCTION IF EXISTS public.update_visit_title_and_status();
-- DROP FUNCTION IF EXISTS public.get_visitor_requests_with_meeting_records_not_archived();
-- DROP FUNCTION -- Drop existing triggers dynamically
DO $$
DECLARE
    trigger_record RECORD;
BEGIN
    FOR trigger_record IN
        SELECT trigger_name, event_object_table
        FROM information_schema.triggers
        WHERE trigger_schema = 'public'
    LOOP
        BEGIN
            EXECUTE format('DROP TRIGGER IF EXISTS %I ON public.%I;', 
                          trigger_record.trigger_name, 
                          trigger_record.event_object_table);
            RAISE NOTICE 'Successfully dropped trigger % on table %', 
                         trigger_record.trigger_name, 
                         trigger_record.event_object_table;
        EXCEPTION WHEN OTHERS THEN
            RAISE NOTICE 'Failed to drop trigger % on table %: %', 
                         trigger_record.trigger_name, 
                         trigger_record.event_object_table, 
                         SQLERRM;
            -- Continue with the next trigger
        END;
    END LOOP;
END;
$$;

-- Drop existing triggers first (if they exist)
-- DROP TRIGGER IF EXISTS trigger_set_banned_until ON public.users;
-- DROP TRIGGER IF EXISTS log_inmates_changes ON public.inmates;
-- DROP TRIGGER IF EXISTS log_visitors_changes ON public.visitors;
-- DROP TRIGGER IF EXISTS log_notifications_changes ON public.notifications;
-- DROP TRIGGER IF EXISTS log_visit_requests_changes ON public.visit_requests;
-- DROP TRIGGER IF EXISTS log_users_changes ON public.users;
-- DROP TRIGGER IF EXISTS log_roles_changes ON public.roles;
-- DROP TRIGGER IF EXISTS log_permissions_changes ON public.permissions;
-- DROP TRIGGER IF EXISTS log_role_permissions_changes ON public.role_permissions;
-- DROP TRIGGER IF EXISTS log_user_permissions_changes ON public.user_permissions;
-- DROP TRIGGER IF EXISTS log_sections_changes ON public.sections;
-- DROP TRIGGER IF EXISTS log_section_availability_settings_changes ON public.section_availability_settings;
-- DROP TRIGGER IF EXISTS log_availability_times_changes ON public.availability_times;supa
-- DROP TRIGGER IF EXISTS log_inmate_visitors_changes ON public.inmate_visitors;
-- DROP TRIGGER IF EXISTS log_inmate_visitor_documents_changes ON public.inmate_visitor_documents;
-- DROP TRIGGER IF EXISTS log_rooms_changes ON public.rooms;
-- DROP TRIGGER IF EXISTS log_room_status_changes ON public.room_status;
-- DROP TRIGGER IF EXISTS log_room_availability_settings_changes ON public.room_availability_settings;
-- DROP TRIGGER IF EXISTS log_inmate_availability_settings_changes ON public.inmate_availability_settings;
-- DROP TRIGGER IF EXISTS log_inmate_settings_changes ON public.inmate_settings;
-- DROP TRIGGER IF EXISTS log_meeting_records_changes ON public.meeting_records;
-- DROP TRIGGER IF EXISTS log_meeting_feedbacks_changes ON public.meeting_feedbacks;
-- DROP TRIGGER IF EXISTS log_room_slots_changes ON public.room_slots;
-- DROP TRIGGER IF EXISTS log_visit_requests_status_changes ON public.visit_requests_status;
-- DROP TRIGGER IF EXISTS log_inmates_status_changes ON public.inmates_status;
-- DROP TRIGGER IF EXISTS log_settings_changes ON public.settings;
DROP TRIGGER IF EXISTS trigger_disable_auth_user ON public.users;
-- DROP TRIGGER IF EXISTS trigger_visit_status_change ON public.visit_requests;
-- DROP TRIGGER IF EXISTS trigger_update_visit ON public.visit_requests;
-- DROP TRIGGER IF EXISTS trigger_update_from_to ON public.visit_requests;
-- DROP TRIGGER IF EXISTS trigger_update_first_response_time ON public.visit_requests;IF EXISTS public.log_activity(uuid, text, text, text, text, text, jsonb, jsonb, jsonb, text, text);
-- DROP FUNCTION IF EXISTS public.log_custom_activity(text, text, text, text, jsonb);
-- DROP FUNCTION IF EXISTS public.handle_public_user();
-- DROP FUNCTION IF EXISTS public.user_trigger_function();
-- DROP FUNCTION IF EXISTS public.set_first_response_time();
-- DROP FUNCTION IF EXISTS public.trigger_log_changes();
-- DROP FUNCTION IF EXISTS public.get_visitor_visit_requests_with_meeting_records_not_archived();

-- Create functions
CREATE OR REPLACE FUNCTION public.disable_auth_user_on_delete()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
BEGIN
  -- Update auth.users to set is_active = FALSE where the id matches the user_auth_id
UPDATE auth.users
SET is_active = FALSE
WHERE id = NEW.user_auth_id;

RETURN NEW;
END;
$function$;

CREATE OR REPLACE FUNCTION public.set_banned_until()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$BEGIN
    -- Update the auth.users table to set banned_until when deleted_at is set
UPDATE auth.users
SET banned_until = (NOW() + INTERVAL '100 year')::TIMESTAMPTZ
WHERE id = NEW.user_auth_id;

RETURN NEW;
END;$function$;

CREATE OR REPLACE FUNCTION public.sync_all_auth_users()
 RETURNS void
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
auth_user RECORD;
    default_role_id UUID;
BEGIN
    -- Get the default role ID
SELECT id INTO default_role_id
FROM public.roles
WHERE name = 'default_role'
    LIMIT 1;

IF default_role_id IS NULL THEN
        RAISE EXCEPTION 'Default role not found in public.roles';
END IF;

    -- Loop through all auth users
FOR auth_user IN
SELECT * FROM auth.users
                  LOOP
              -- Insert or update the public.users table
    INSERT INTO public.users (
    user_auth_id, email, role_id, first_name, last_name
)
VALUES (
    auth_user.id,
    auth_user.email,
    default_role_id,
    auth_user.raw_user_meta_data ->> 'first_name',
    auth_user.raw_user_meta_data ->> 'last_name'
    )
ON CONFLICT (user_auth_id) DO UPDATE SET
    email = EXCLUDED.email,
                                  role_id = EXCLUDED.role_id,
                                  first_name = EXCLUDED.first_name,
                                  last_name = EXCLUDED.last_name;
END LOOP;
END;
$function$;

CREATE OR REPLACE FUNCTION public.get_visitor_requests_with_relation(visitor_id_input uuid)
 RETURNS TABLE(request_id text, request_status text, request_number text, inamte_full_name text, inmate_id text, visitor_id text, request_date timestamp without time zone, relation text)
 LANGUAGE plpgsql
AS $function$BEGIN
    RETURN QUERY
SELECT vr.id,vr.status_id::TEXT ,vr.visit_number::TEXT , inm.full_name,vr.inmate_id, vr.visitor_id,vr.datetime::timestamp without time zone, iv.relation::TEXT
FROM public."visit_requests" vr
    JOIN public."inmate_visitors" iv ON vr.inmate_id = iv.inmate_id AND vr.visitor_id = iv.visitor_id
    JOIN public."inmates" inm
    ON inm.id = vr.inmate_id
WHERE vr.visitor_id = visitor_id_input::TEXT AND vr.status_id IN ('1','3','2','4') AND iv."deleted_at" IS null;
END;$function$;

CREATE OR REPLACE FUNCTION public.assign_room_to_visit()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
BEGIN
  -- Assign room_id based on criteria
SELECT r.id
INTO NEW.room_id
FROM public."Room" r
         JOIN public."Inmate" i ON i.sectionid = r.sectionid
WHERE r.active = true
  AND i.id = NEW.inmate_id
  AND NOT EXISTS (
    SELECT 1
    FROM public."Visit_Request" vr
    WHERE vr.room_id = r.id
      AND vr.datetime <= NEW.datetime + interval '1 minute' * NEW.duration
        AND vr.datetime + interval '1 minute' * vr.duration >= NEW.datetime
        AND (vr.status = 'APPROVED' OR vr.status = 'STARTS_SOON')
)
ORDER BY random() -- Randomly pick any available room
    LIMIT 1;

-- If no room is found, raise an error
IF NEW.room_id IS NULL THEN
    RAISE EXCEPTION 'No available room for the requested time slot.';
END IF;

RETURN NEW;
END;
$function$;

CREATE OR REPLACE FUNCTION public.approve_visit_if_whitelisted()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
BEGIN
    IF EXISTS (
        SELECT 1
        FROM "Inmate_Visitor"
        WHERE inmate_id = NEW.inmate_id
        AND visitor_id = NEW.visitor_id
        AND is_white_list = TRUE
    ) THEN
        NEW.status := 'APPROVED';
END IF;
RETURN NEW;
END;
$function$;

CREATE OR REPLACE FUNCTION public.notify_visit_status_change()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
DECLARE
status_name TEXT;
BEGIN
    -- Get the name of the status
SELECT name INTO status_name FROM visit_requests_status WHERE id = NEW.status_id;

-- Insert a notification only if the status_id has changed
IF OLD.status_id IS DISTINCT FROM NEW.status_id THEN
        INSERT INTO notifications (id, datetime, visitor_id, visit_id, message, seen)
        VALUES (
            gen_random_uuid(),
            NOW(),
            NEW.visitor_id,
            NEW.id,
            status_name,
            FALSE
        );
END IF;

RETURN NEW;
END;
$function$;

CREATE OR REPLACE FUNCTION public.get_admin_requests_with_meeting_records()
 RETURNS TABLE(request_id text, request_status text, request_number text, inmate_full_name text, inmate_id text, admin_id text, admin_name text, request_date timestamp without time zone, meeting_status text, meeting_date_time timestamp without time zone, record_id text, recording_url text, record_duration integer, archived boolean, record_size integer, feedback_rate integer, feedback_comment text)
 LANGUAGE plpgsql
AS $function$
BEGIN
RETURN QUERY
SELECT DISTINCT ON (vr.id)
    vr.id,
    vr.status::TEXT,
    vr.visit_number::TEXT,
    inm.full_name AS inmate_full_name,
    vr.inmate_id,
    admin.id as admin_id,
    admin.full_name AS admin_name,
    vr.datetime AS request_date,
    mtg.status::TEXT AS meeting_status,
    mtg.meeting_date_time,
    mrec.id,
    mrec.recording_url,
    mrec.duration AS record_duration,
    mrec.archived,
    mrec.record_size,
    feedback.rate,
    feedback.comment
FROM public."visit_requests" vr

    LEFT JOIN public."inmates" inm ON inm.id = vr.inmate_id
    JOIN public."users" admin ON admin.id = vr.user_id
    LEFT JOIN public."meetings" mtg ON mtg.id = vr.meeting_id
    JOIN public."meeting_records" mrec ON mrec.meeting_id = mtg.id
    LEFT JOIN public."meeting_feedbacks" feedback ON mrec.meeting_id = feedback.meeting_id
WHERE mrec."deleted_at" IS NULL AND mrec.archived = TRUE AND vr.visitor_id IS NULL;
END;
$function$;

CREATE OR REPLACE FUNCTION public.get_admin_requests_with_meeting_records_not_archived()
 RETURNS TABLE(request_id text, request_status text, request_number text, inmate_full_name text, inmate_id text, admin_id text, admin_name text, request_date timestamp without time zone, meeting_status text, meeting_date_time timestamp without time zone, record_id text, recording_url text, record_duration integer, archived boolean, record_size integer, feedback_rate integer, feedback_comment text)
 LANGUAGE plpgsql
AS $function$
BEGIN
RETURN QUERY
SELECT DISTINCT ON (vr.id)
    vr.id,
    vr.status::TEXT,
    vr.visit_number::TEXT,
    inm.full_name AS inmate_full_name,
    vr.inmate_id,
    admin.id as admin_id,
    admin.full_name AS admin_name,
    vr.datetime AS request_date,
    mtg.status::TEXT AS meeting_status,
    mtg.meeting_date_time,
    mrec.id,
    mrec.recording_url,
    mrec.duration AS record_duration,
    mrec.archived,
    mrec.record_size,
    feedback.rate,
    feedback.comment
FROM public."visit_requests" vr

    LEFT JOIN public."inmates" inm ON inm.id = vr.inmate_id
    JOIN public."users" admin ON admin.id = vr.user_id
    LEFT JOIN public."meetings" mtg ON mtg.id = vr.meeting_id
    JOIN public."meeting_records" mrec ON mrec.meeting_id = mtg.id
    LEFT JOIN public."meeting_feedbacks" feedback ON mrec.meeting_id = feedback.meeting_id
WHERE mrec."deleted_at" IS NULL AND mrec.archived = FALSE AND vr.visitor_id IS NULL;
END;
$function$;

CREATE OR REPLACE FUNCTION public.remove_quotes_from_names()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
BEGIN
NEW.first_name := REPLACE(NEW.first_name, '"', '');
NEW.last_name := REPLACE(NEW.last_name, '"', '');
RETURN NEW;
END;
$function$;

CREATE OR REPLACE FUNCTION public.delete_user_from_public()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
BEGIN
-- Delete the user from public.users where the email matches
DELETE FROM public."users" WHERE email = OLD.email;
RETURN OLD;
END;
$function$;

CREATE OR REPLACE FUNCTION public.update_from_to()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
BEGIN
        NEW."from" := NEW.datetime;
        NEW."to" := NEW.datetime + INTERVAL '1 minute' * NEW.duration;
RETURN NEW;
END;
    $function$;

CREATE OR REPLACE FUNCTION public.get_visitor_requests_with_meeting_records()
 RETURNS TABLE(request_id text, request_status text, request_number text, inmate_full_name text, inmate_id text, visitor_name text, request_date timestamp with time zone, "from" time with time zone, "to" time with time zone, relation text, meeting_status text, meeting_date_time timestamp with time zone, record_id text, recording_url text, record_duration integer, archived boolean, record_size integer, feedback_rate integer, feedback_comment text)
 LANGUAGE plpgsql
AS $function$BEGIN
RETURN QUERY
SELECT DISTINCT ON (mrec.id)
    vr.id,
    vr.status_id::TEXT,
    vr.visit_number::TEXT,
    inm.full_name AS inmate_full_name,
    vr.inmate_id,
    vis.full_name AS visitor_name,
    vr.datetime::timestamptz AS request_date,
    vr."from"::timetz AS "from", -- New field
    vr."to"::timetz AS "to", -- New field
    iv.relation::TEXT,
    vr.status_id::TEXT AS meeting_status, -- Previously from meetings table
    vr.datetime::timestamptz AS meeting_date_time, -- Previously from meetings table
    mrec.id,
    mrec.recording_url,
    mrec.duration AS record_duration,
    mrec.archived,
    mrec.record_size,
    feedback.rate,
    feedback.comment
FROM public.visit_requests vr
    LEFT JOIN public.inmate_visitors iv
ON vr.inmate_id = iv.inmate_id
    AND vr.visitor_id = iv.visitor_id
    LEFT JOIN public.inmates inm
    ON inm.id = vr.inmate_id
    JOIN public.visitors vis
    ON vis.id = vr.visitor_id
    JOIN public.meeting_records mrec
    ON mrec.visit_request_id = vr.id
    LEFT JOIN public.meeting_feedbacks feedback
    ON mrec.visit_request_id = feedback.visit_request_id
WHERE mrec.deleted_at IS NULL
  AND mrec.archived = TRUE;
END;$function$;

CREATE OR REPLACE FUNCTION public.update_visit_title_and_status()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$BEGIN
  -- Check if the status_id is being changed to 2
  IF NEW.status_id = 2 THEN
    -- Construct the title using a prompt and visit_number
    NEW.title := 'Meeting for visit request number' || NEW.visit_number;

END IF;

RETURN NEW;
END;$function$;

CREATE OR REPLACE FUNCTION public.get_visitor_requests_with_meeting_records_not_archived()
 RETURNS TABLE(request_id text, request_status text, request_number text, inmate_full_name text, inmate_id text, visitor_name text, request_date timestamp with time zone, "from" time with time zone, "to" time with time zone, relation text, meeting_status text, meeting_date_time timestamp with time zone, record_id text, recording_url text, record_duration integer, archived boolean, record_size integer, feedback_rate integer, feedback_comment text)
 LANGUAGE plpgsql
AS $function$BEGIN
RETURN QUERY
SELECT DISTINCT ON (mrec.id)
    vr.id,
    vr.status_id::TEXT,
    vr.visit_number::TEXT,
    inm.full_name AS inmate_full_name,
    vr.inmate_id,
    vis.full_name AS visitor_name,
    vr.datetime::timestamptz AS request_date,
    vr."from"::timetz AS "from", -- New field
    vr."to"::timetz AS "to", -- New field
    iv.relation::TEXT,
    vr.status_id::TEXT AS meeting_status, -- Previously from meetings table
    vr.datetime::timestamptz AS meeting_date_time, -- Previously from meetings table
    mrec.id,
    mrec.recording_url,
    mrec.duration AS record_duration,
    mrec.archived,
    mrec.record_size,
    feedback.rate,
    feedback.comment
FROM public.visit_requests vr
    LEFT JOIN public.inmate_visitors iv
ON vr.inmate_id = iv.inmate_id
    AND vr.visitor_id = iv.visitor_id
    LEFT JOIN public.inmates inm
    ON inm.id = vr.inmate_id
    JOIN public.visitors vis
    ON vis.id = vr.visitor_id
    JOIN public.meeting_records mrec
    ON mrec.visit_request_id = vr.id
    LEFT JOIN public.meeting_feedbacks feedback
    ON mrec.visit_request_id = feedback.visit_request_id
WHERE mrec.deleted_at IS NULL
  AND mrec.archived = FALSE;
END;$function$;

CREATE OR REPLACE FUNCTION public.log_activity(p_user_id uuid, p_user_email text, p_entity_type text, p_entity_id text, p_action text, p_action_group text DEFAULT NULL::text, p_old_data jsonb DEFAULT NULL::jsonb, p_new_data jsonb DEFAULT NULL::jsonb, p_metadata jsonb DEFAULT NULL::jsonb, p_ip_address text DEFAULT NULL::text, p_user_agent text DEFAULT NULL::text)
 RETURNS uuid
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
v_log_id UUID;
BEGIN
INSERT INTO activity_logs (
    user_id,
    user_email,
    entity_type,
    entity_id,
    action,
    action_group,
    old_data,
    new_data,
    metadata,
    ip_address,
    user_agent
) VALUES (
             p_user_id,
             p_user_email,
             p_entity_type,
             p_entity_id,
             p_action,
             p_action_group,
             p_old_data,
             p_new_data,
             p_metadata,
             p_ip_address,
             p_user_agent
         )
    RETURNING id INTO v_log_id;

RETURN v_log_id;
END;
$function$;

CREATE OR REPLACE FUNCTION public.log_custom_activity(p_entity_type text, p_entity_id text, p_action text, p_action_group text DEFAULT NULL::text, p_metadata jsonb DEFAULT NULL::jsonb)
 RETURNS uuid
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
v_user_id UUID := auth.uid();
  v_log_id UUID;
BEGIN
INSERT INTO activity_logs (
    user_id,
    entity_type,
    entity_id,
    action,
    action_group,
    metadata
) VALUES (
             v_user_id,
             p_entity_type,
             p_entity_id,
             p_action,
             p_action_group,
             p_metadata
         )
    RETURNING id INTO v_log_id;

RETURN v_log_id;
END;
$function$;



CREATE OR REPLACE FUNCTION public.user_trigger_function()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
DECLARE
default_role_id UUID;
    user_name TEXT;
BEGIN
    -- Log the start of the function
    RAISE NOTICE 'Trigger started for user ID: %, email: %', NEW.id, NEW.email;

    -- Fetch the default role ID
SELECT id INTO default_role_id
FROM public.roles
WHERE name = 'default_role'
    LIMIT 1;

IF default_role_id IS NULL THEN
        RAISE NOTICE 'No default role found. Exiting trigger.';
RETURN NEW;
END IF;

    -- Extract username from email
    user_name := split_part(NEW.email, '@', 1);

    -- Insert or update the user in public.users table
INSERT INTO public.users (user_auth_id, email, role_id, first_name, last_name)
VALUES (
           NEW.id,
           NEW.email,
           default_role_id,
           NEW.raw_user_meta_data->>'first_name',
           NEW.raw_user_meta_data->>'last_name'
       )
    ON CONFLICT (email)
    DO UPDATE SET
    user_auth_id = NEW.id,
               role_id = default_role_id,
               first_name = NEW.raw_user_meta_data->>'first_name',
               last_name = NEW.raw_user_meta_data->>'last_name';

-- Log successful insertion
RAISE NOTICE 'User successfully inserted/updated in public.users';

RETURN NEW;
EXCEPTION WHEN OTHERS THEN
    RAISE NOTICE 'Error in trigger: %', SQLERRM;
RETURN NEW;
END;
$function$;

CREATE OR REPLACE FUNCTION public.set_first_response_time()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
BEGIN
  -- Only update first_response_time if it's NULL
  IF NEW.first_response_time IS NULL THEN
    NEW.first_response_time := NOW();
END IF;
RETURN NEW;
END;
$function$;

CREATE OR REPLACE FUNCTION public.trigger_log_changes()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
DECLARE
v_user_id UUID := NULL; -- Will be set from JWT or app context
    v_user_email TEXT := NULL; -- Optional, can be fetched from auth.users
    v_old_data JSONB := NULL; -- For UPDATE/DELETE
    v_new_data JSONB := NULL; -- For INSERT/UPDATE
    v_action TEXT := TG_OP; -- INSERT, UPDATE, or DELETE
    v_entity_type TEXT := TG_TABLE_NAME; -- Name of the table
    v_entity_id TEXT; -- ID of the affected row
BEGIN
    -- Extract user ID from JWT (if available)
    v_user_id := NULLIF((current_setting('request.jwt.claims', TRUE)::jsonb)->>'sub', '')::UUID;

    -- Prepare old and new data
    IF TG_OP = 'UPDATE' THEN
        v_old_data := to_jsonb(OLD);
        v_new_data := to_jsonb(NEW);
        v_entity_id := NEW.id::TEXT;
    ELSIF TG_OP = 'INSERT' THEN
        v_new_data := to_jsonb(NEW);
        v_entity_id := NEW.id::TEXT;
    ELSIF TG_OP = 'DELETE' THEN
        v_old_data := to_jsonb(OLD);
        v_entity_id := OLD.id::TEXT;
END IF;

    -- Log the activity
INSERT INTO activity_logs (
    user_id,
    user_email,
    entity_type,
    entity_id,
    action,
    old_data,
    new_data
) VALUES (
             v_user_id,
             v_user_email,
             v_entity_type,
             v_entity_id,
             v_action,
             v_old_data,
             v_new_data
         );

RETURN NEW;
END;
$function$;

CREATE OR REPLACE FUNCTION public.get_visitor_visit_requests_with_meeting_records_not_archived()
 RETURNS TABLE(request_id text, request_status text, request_number text, visitor_full_name text, visitor_id text, inmate_name text, request_date timestamp with time zone, "from" time with time zone, "to" time with time zone, relation text, meeting_status text, meeting_date_time timestamp with time zone, record_id text, recording_url text, record_duration integer, archived boolean, record_size integer, feedback_rate integer, feedback_comment text)
 LANGUAGE plpgsql
AS $function$
BEGIN
RETURN QUERY
SELECT DISTINCT ON (mrec.id)
    vr.id,
    vr.status_id::TEXT,
    vr.visit_number::TEXT,
    vis.full_name AS visitor_full_name,
    vr.visitor_id,
    inm.full_name AS inmate_name,
    vr.datetime::timestamptz AS request_date,
    vr."from"::timetz AS "from",
    vr."to"::timetz AS "to",
    iv.relation::TEXT,
    vr.status_id::TEXT AS meeting_status,
    vr.datetime::timestamptz AS meeting_date_time,
    mrec.id,
    mrec.recording_url,
    mrec.duration AS record_duration,
    mrec.archived,
    mrec.record_size,
    feedback.rate,
    feedback.comment
FROM public.visit_requests vr

    LEFT JOIN public.inmate_visitors iv
ON vr.visitor_id = iv.visitor_id
    AND vr.inmate_id = iv.inmate_id

    LEFT JOIN public.visitors vis
    ON vis.id = vr.visitor_id

    JOIN public.inmates inm
    ON inm.id = vr.inmate_id

    JOIN public.meeting_records mrec
    ON mrec.visit_request_id = vr.id

    LEFT JOIN public.meeting_feedbacks feedback
    ON mrec.visit_request_id = feedback.visit_request_id

WHERE mrec.deleted_at IS NULL
  AND mrec.archived = FALSE;
END;
$function$;

-- Create triggers
CREATE TRIGGER trigger_set_banned_until
    AFTER UPDATE OF deleted_at ON public.users
    FOR EACH ROW
    WHEN (OLD.deleted_at IS NULL AND NEW.deleted_at IS NOT NULL)
    EXECUTE FUNCTION set_banned_until();

CREATE TRIGGER log_inmates_changes
    AFTER INSERT OR UPDATE OR DELETE ON public.inmates
    FOR EACH ROW
    EXECUTE FUNCTION trigger_log_changes();

CREATE TRIGGER log_visitors_changes
    AFTER INSERT OR UPDATE OR DELETE ON public.visitors
    FOR EACH ROW
    EXECUTE FUNCTION trigger_log_changes();

CREATE TRIGGER log_notifications_changes
    AFTER INSERT OR UPDATE OR DELETE ON public.notifications
    FOR EACH ROW
    EXECUTE FUNCTION trigger_log_changes();

CREATE TRIGGER log_visit_requests_changes
    AFTER INSERT OR UPDATE OR DELETE ON public.visit_requests
    FOR EACH ROW
    EXECUTE FUNCTION trigger_log_changes();

CREATE TRIGGER log_users_changes
    AFTER INSERT OR UPDATE OR DELETE ON public.users
    FOR EACH ROW
    EXECUTE FUNCTION trigger_log_changes();

CREATE TRIGGER log_roles_changes
    AFTER INSERT OR UPDATE OR DELETE ON public.roles
    FOR EACH ROW
    EXECUTE FUNCTION trigger_log_changes();

CREATE TRIGGER log_permissions_changes
    AFTER INSERT OR UPDATE OR DELETE ON public.permissions
    FOR EACH ROW
    EXECUTE FUNCTION trigger_log_changes();

CREATE TRIGGER log_role_permissions_changes
    AFTER INSERT OR UPDATE OR DELETE ON public.role_permissions
    FOR EACH ROW
    EXECUTE FUNCTION trigger_log_changes();

CREATE TRIGGER log_user_permissions_changes
    AFTER INSERT OR UPDATE OR DELETE ON public.user_permissions
    FOR EACH ROW
    EXECUTE FUNCTION trigger_log_changes();

CREATE TRIGGER log_sections_changes
    AFTER INSERT OR UPDATE OR DELETE ON public.sections
    FOR EACH ROW
    EXECUTE FUNCTION trigger_log_changes();

CREATE TRIGGER log_section_availability_settings_changes
    AFTER INSERT OR UPDATE OR DELETE ON public.section_availability_settings
    FOR EACH ROW
    EXECUTE FUNCTION trigger_log_changes();

CREATE TRIGGER log_availability_times_changes
    AFTER INSERT OR UPDATE OR DELETE ON public.availability_times
    FOR EACH ROW
    EXECUTE FUNCTION trigger_log_changes();

CREATE TRIGGER log_inmate_visitors_changes
    AFTER INSERT OR UPDATE OR DELETE ON public.inmate_visitors
    FOR EACH ROW
    EXECUTE FUNCTION trigger_log_changes();

CREATE TRIGGER log_inmate_visitor_documents_changes
    AFTER INSERT OR UPDATE OR DELETE ON public.inmate_visitor_documents
    FOR EACH ROW
    EXECUTE FUNCTION trigger_log_changes();

CREATE TRIGGER log_rooms_changes
    AFTER INSERT OR UPDATE OR DELETE ON public.rooms
    FOR EACH ROW
    EXECUTE FUNCTION trigger_log_changes();

CREATE TRIGGER log_room_status_changes
    AFTER INSERT OR UPDATE OR DELETE ON public.room_status
    FOR EACH ROW
    EXECUTE FUNCTION trigger_log_changes();

CREATE TRIGGER log_room_availability_settings_changes
    AFTER INSERT OR UPDATE OR DELETE ON public.room_availability_settings
    FOR EACH ROW
    EXECUTE FUNCTION trigger_log_changes();

CREATE TRIGGER log_inmate_availability_settings_changes
    AFTER INSERT OR UPDATE OR DELETE ON public.inmate_availability_settings
    FOR EACH ROW
    EXECUTE FUNCTION trigger_log_changes();

CREATE TRIGGER log_inmate_settings_changes
    AFTER INSERT OR UPDATE OR DELETE ON public.inmate_settings
    FOR EACH ROW
    EXECUTE FUNCTION trigger_log_changes();

CREATE TRIGGER log_meeting_records_changes
    AFTER INSERT OR UPDATE OR DELETE ON public.meeting_records
    FOR EACH ROW
    EXECUTE FUNCTION trigger_log_changes();

CREATE TRIGGER log_meeting_feedbacks_changes
    AFTER INSERT OR UPDATE OR DELETE ON public.meeting_feedbacks
    FOR EACH ROW
    EXECUTE FUNCTION trigger_log_changes();

CREATE TRIGGER log_room_slots_changes
    AFTER INSERT OR UPDATE OR DELETE ON public.room_slots
    FOR EACH ROW
    EXECUTE FUNCTION trigger_log_changes();

CREATE TRIGGER log_visit_requests_status_changes
    AFTER INSERT OR UPDATE OR DELETE ON public.visit_requests_status
    FOR EACH ROW
    EXECUTE FUNCTION trigger_log_changes();

CREATE TRIGGER log_inmates_status_changes
    AFTER INSERT OR UPDATE OR DELETE ON public.inmates_status
    FOR EACH ROW
    EXECUTE FUNCTION trigger_log_changes();

CREATE TRIGGER log_settings_changes
    AFTER INSERT OR UPDATE OR DELETE ON public.settings
    FOR EACH ROW
    EXECUTE FUNCTION trigger_log_changes();

CREATE TRIGGER trigger_disable_auth_user
    AFTER UPDATE OF deleted_at ON public.users
    FOR EACH ROW
    WHEN (OLD.deleted_at IS NULL AND NEW.deleted_at IS NOT NULL)
    EXECUTE FUNCTION disable_auth_user_on_delete();

CREATE TRIGGER trigger_visit_status_change
    AFTER UPDATE OF status_id ON public.visit_requests
    FOR EACH ROW
    WHEN (OLD.status_id IS DISTINCT FROM NEW.status_id)
EXECUTE FUNCTION notify_visit_status_change();

CREATE TRIGGER trigger_update_visit
    BEFORE UPDATE OF status_id ON public.visit_requests
    FOR EACH ROW
    EXECUTE FUNCTION update_visit_title_and_status();

CREATE TRIGGER trigger_update_from_to
    BEFORE INSERT OR UPDATE OF datetime, duration ON public.visit_requests
    FOR EACH ROW
    EXECUTE FUNCTION update_from_to();

CREATE TRIGGER trigger_update_first_response_time
    BEFORE UPDATE OF status_id ON public.visit_requests
    FOR EACH ROW
    WHEN (NEW.status_id = '2' AND OLD.status_id <> '2')
    EXECUTE FUNCTION set_first_response_time();

CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW
    EXECUTE FUNCTION public.user_trigger_function();



COMMIT;
-- Grant permissions to all possible roles on all functions
-- Grant permissions to all possible roles on all functions
DO $$
DECLARE
func_record RECORD;
    role_record RECORD;
    func_signature text;
BEGIN
    -- Grant EXECUTE on all public functions to all roles that aren't superusers
FOR func_record IN
SELECT
    p.proname as funcname,
    p.oid,
    n.nspname as schemaname,
    pg_get_function_identity_arguments(p.oid) as funcargs
FROM pg_proc p
         JOIN pg_namespace n ON p.pronamespace = n.oid
WHERE n.nspname = 'public'
    LOOP
        -- Build the proper function signature
        IF func_record.funcargs = '' THEN
            func_signature := format('%I.%I()', func_record.schemaname, func_record.funcname);
ELSE
            func_signature := format('%I.%I(%s)', func_record.schemaname, func_record.funcname, func_record.funcargs);
END IF;

FOR role_record IN
SELECT rolname FROM pg_roles
WHERE rolname NOT IN ('postgres', 'pg_signal_backend')
  AND rolname NOT LIKE 'pg_%'
  AND rolname NOT LIKE 'rds%'
    LOOP
BEGIN
EXECUTE format('GRANT EXECUTE ON FUNCTION %s TO %I',
               func_signature,
               role_record.rolname);
RAISE NOTICE 'Granted execute on function % to role %',
                    func_signature, role_record.rolname;
EXCEPTION WHEN OTHERS THEN
                RAISE NOTICE 'Could not grant execute on function % to role %: %',
                    func_signature, role_record.rolname, SQLERRM;
END;
END LOOP;
END LOOP;

    -- Grant USAGE on public schema to all roles
FOR role_record IN
SELECT rolname FROM pg_roles
WHERE rolname NOT IN ('postgres', 'pg_signal_backend')
  AND rolname NOT LIKE 'pg_%'
  AND rolname NOT LIKE 'rds%'
    LOOP
BEGIN
EXECUTE format('GRANT USAGE ON SCHEMA public TO %I', role_record.rolname);
EXCEPTION WHEN OTHERS THEN
            RAISE NOTICE 'Could not grant usage on schema public to role %: %',
                role_record.rolname, SQLERRM;
END;
END LOOP;

    -- Grant SELECT on all tables to all roles (more restrictive than previous version)
FOR role_record IN
SELECT rolname FROM pg_roles
WHERE rolname NOT IN ('postgres', 'pg_signal_backend')
  AND rolname NOT LIKE 'pg_%'
  AND rolname NOT LIKE 'rds%'
    LOOP
BEGIN
EXECUTE format('GRANT SELECT ON ALL TABLES IN SCHEMA public TO %I', role_record.rolname);
EXCEPTION WHEN OTHERS THEN
            RAISE NOTICE 'Could not grant table permissions to role %: %',
                role_record.rolname, SQLERRM;
END;
END LOOP;
END $$;

-- Set default privileges for future objects (more restrictive than previous version)
ALTER DEFAULT PRIVILEGES IN SCHEMA public
GRANT SELECT ON TABLES TO PUBLIC;

ALTER DEFAULT PRIVILEGES IN SCHEMA public
GRANT SELECT ON SEQUENCES TO PUBLIC;

ALTER DEFAULT PRIVILEGES IN SCHEMA public
GRANT EXECUTE ON FUNCTIONS TO PUBLIC;

-- Special handling for Supabase roles
DO $$
BEGIN
    -- Grant basic permissions to anon and authenticated roles
    GRANT USAGE ON SCHEMA public TO anon, authenticated;
    GRANT SELECT ON ALL TABLES IN SCHEMA public TO anon, authenticated;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO anon, authenticated;

    -- Grant more permissions to service_role (Supabase admin role)
    GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO service_role;
    GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO service_role;
    GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA public TO service_role;

    -- Additional Supabase roles
    IF EXISTS (SELECT 1 FROM pg_roles WHERE rolname = 'dashboard_user') THEN
        GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO dashboard_user;
        GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA public TO dashboard_user;
END IF;

    IF EXISTS (SELECT 1 FROM pg_roles WHERE rolname = 'supabase_admin') THEN
        GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO supabase_admin;
        GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA public TO supabase_admin;
END IF;

    IF EXISTS (SELECT 1 FROM pg_roles WHERE rolname = 'supabase_storage_admin') THEN
        GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO supabase_storage_admin;
        GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA public TO supabase_storage_admin;
END IF;
EXCEPTION WHEN OTHERS THEN
    RAISE NOTICE 'Supabase specific permissions not applied: %', SQLERRM;
END $$;

-- Ensure pg_cron extension is available
CREATE EXTENSION IF NOT EXISTS pg_cron;

-- Safely unschedule the job if it already exists
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM cron.job WHERE jobname = 'visit-request-status-check'
    ) THEN
        PERFORM cron.unschedule('visit-request-status-check');
END IF;
END $$;

-- Recreate the cron job
SELECT cron.schedule(
               'visit-request-status-check',
               '*/1 * * * *',
               $$
                   -- Update visit_requests status to 'missed' if time has passed
                   UPDATE public."visit_requests"
        SET status_id = 7
        WHERE datetime + INTERVAL '1 minute' * duration < now()
          AND status_id IN (2, 3);

-- Insert notifications for all users if SLA is exceeded
WITH sla_due AS (
    SELECT value::interval AS due_interval
    FROM settings
    WHERE settings.module = 'sla' AND settings.key = 'request_response_due'
)
INSERT INTO users_notifications (user_id, visitor_id, visit_id, title_id, created_at, seen)
SELECT u.id, vr.visitor_id, vr.id, 15, NOW(), NULL
FROM public."visit_requests" vr
         CROSS JOIN users u
         CROSS JOIN sla_due
WHERE vr.status_id = 1
  AND NOW() - vr.created_at > sla_due.due_interval;
$$
);
