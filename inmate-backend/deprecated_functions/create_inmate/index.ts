// Follow this setup guide to integrate the Deno language server with your editor:
// https://deno.land/manual/getting_started/setup_your_environment
// This enables autocomplete, go to definition, etc.

// Setup type definitions for built-in Supabase Runtime APIs
import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { createClient } from 'npm:@supabase/supabase-js@2.39.3';

const supabase = createClient(Deno.env.get('SUPABASE_URL')!, Deno.env.get('SUPABASE_ANON_KEY')!);


// Helper to add CORS headers
function withCors(response) {
  response.headers.set("Access-Control-Allow-Origin", "*");
  response.headers.set("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
  response.headers.set("Access-Control-Allow-Headers", "Content-Type, Authorization");
  return response;
}

Deno.serve(async (req) => {
  if (req.method === "OPTIONS") {
    return withCors(new Response(null, { status: 204 }));
  }

  if (req.method !== "POST") {
    return withCors(new Response("Method Not Allowed", { status: 405 }));
  }

  try {
    const {
      full_name,
      national_id,
      nationality,
      assigned_building,
      assigned_room,
      status_id,
      number_of_visits,
      availability,
      is_default,
      inmate_code,
      
    } = await req.json();

    console.log("Received request data:", { full_name, national_id, nationality, assigned_building, assigned_room, status_id, number_of_visits, availability, is_default,inmate_code, });

    // Validate required fields
    // Validate required fields
    const requiredFields = ['full_name', 'inmate_code', 'assigned_building', 'number_of_visits'];
    const missingFields = requiredFields.filter(field => !eval(field));
    
    if (missingFields.length > 0) {
      console.error("Missing required fields:", missingFields);
      return withCors(
        new Response(
          JSON.stringify({ error: "Missing required fields", missingFields }),
          { status: 400 }
        )
      );
    }

    // Check for duplicate National ID
    const { data: existingInmate, error: findError } = await supabase
      .from("inmates")
      .select("*")
      .eq("national_id", national_id)

    if (existingInmate.length == 0 || national_id == null || national_id == ""){
      console.log("No duplicate found");
    }
    // elif (findError) {
    //   console.error("Error checking for duplicates:", findError);
    //   return withCors(
    //     new Response(
    //       JSON.stringify({ error: "Error checking for duplicates" }),
    //       { status: 500 }
    //     )
    //   );
    // }
    // console.log("darata:", existingInmate);
    if ( national_id && existingInmate.length > 0) {
      console.error("Duplicate National ID detected:", national_id);
      return withCors(
          new Response(
              JSON.stringify({ error: "Duplicate National ID detected",national_id }),
              {
                status: 409,
                headers: { "Content-Type": "application/json" } // Explicitly setting Content-Type
              }
          )
      );
    }// Check for duplicate National ID
    const { data: existingInmateCode } = await supabase
      .from("inmates")
      .select("id")
      .eq("inmate_code", inmate_code)
      .single();
    if (existingInmateCode == 0){
      console.log("No duplicate found");
    }
    // elif (findError) {
    //   console.error("Error checking for duplicates:", findError);
    //   return withCors(
    //     new Response(
    //       JSON.stringify({ error: "Error checking for duplicates" }),
    //       { status: 500 }
    //     )
    //   );
    // }

    if (existingInmateCode) {
      console.error("Duplicate Inmate Code detected:", inmate_code);
      return withCors(
        new Response(
          JSON.stringify({ error: "Duplicate Inmate Code detected" }),
          { status: 409 ,
            headers: { "Content-Type": "application/json" } // Explicitly setting Content-Type
          }
        )
      );
    }

    // Determine default flags
    let is_building_default = false;
    let is_room_default = false;

    if (is_default) {
      is_building_default = true;
      if (assigned_room) {
        is_room_default = true;
        is_building_default = false;
      }
    }

    // Insert new inmate
    const { data: newInmate, error: insertError } = await supabase
      .from("inmates")
      .insert([
        {
          full_name,
          national_id,
          nationality,
          number_of_visits,
          section_id: assigned_building,
          room_id: assigned_room || null,
          status_id,
          is_building_default,
          is_room_default,
          inmate_code,

        },
      ])
      .select("*");

    if (insertError) {
      console.error("Error adding inmate details:", insertError);
      return withCors(
        new Response(
          JSON.stringify({ error: "Error adding inmate details" }),
          { status: 500 ,
            headers: { "Content-Type": "application/json" } // Explicitly setting Content-Type
          }
        )
      );
    }

    console.log("New inmate added:", newInmate);

    // Skip availability if is_default is true
    if (!is_default && availability && availability.length > 0) {
      for (const item of availability) {
        const { day, times } = item;

        // Insert into inmate_availability_settings
        const { data: availabilitySetting, error: availabilityError } = await supabase
          .from("inmate_availability_settings")
          .insert([
            {
              day,
              inmate_id: newInmate[0].id,
            },
          ])
          .select("*")
          .single();

        if (availabilityError) {
          console.error("Error adding availability settings:", availabilityError);
          return withCors(
            new Response(
              JSON.stringify({ error: "Error adding availability settings" }),
              { status: 500 }
            )
          );
        }

        console.log("Availability setting added:", availabilitySetting);

        const settingId = availabilitySetting.id;

        // Insert times into availability_times
        if (times && times.length > 0) {
          const timesData = times.map((time) => ({
            setting_id: settingId,
            type: "inmate",
            available_from: time.from,
            available_to: time.to,
          }));

          const { error: timesError } = await supabase
            .from("availability_times")
            .insert(timesData);

          if (timesError) {
            console.error("Error adding availability times:", timesError);
            return withCors(
              new Response(
                JSON.stringify({ error: "Error adding availability times" }),
                { status: 500 }
              )
            );
          }

          console.log("Availability times added for setting:", settingId);
        }
      }
    }

    console.log("Inmate creation process completed successfully");
    return withCors(
      new Response(
        JSON.stringify({ message: "Inmate added successfully", inmate: newInmate }),
        { status: 200 ,
          headers: { "Content-Type": "application/json" } // Explicitly setting Content-Type
        }
      )
    );
  } catch (error) {
    console.error("Unexpected error:", error);
    return withCors(
      new Response(
        JSON.stringify({ error: "Internal Server Error" }),
        { status: 500 }
      )
    );
  }
})

/* To invoke locally:

  1. Run `supabase start` (see: https://supabase.com/docs/reference/cli/supabase-start)
  2. Make an HTTP request:

  curl -i --location --request POST 'http://127.0.0.1:54321/functions/v1/create_inmate' \
    --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0' \
    --header 'Content-Type: application/json' \
    --data '{"name":"Functions"}'

*/
