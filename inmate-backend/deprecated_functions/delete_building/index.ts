import 'https://esm.sh/@supabase/functions-js/src/edge-runtime.d.ts';
import { createClient } from 'npm:@supabase/supabase-js@2.39.3';

const supabase = createClient(Deno.env.get('SUPABASE_URL')!, Deno.env.get('SUPABASE_ANON_KEY')!);

export const corsHeaders = {
  'Access-Control-Allow-Origin': '*', // Replace with your frontend URL
  'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Max-Age': '86400',
};

Deno.serve(async (req) => {
  try {
    const { buildingId } = await req.json();
    const headers = {
      'Access-Control-Allow-Origin': '*', // Replace with your frontend URL
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
      'Access-Control-Max-Age': '86400',
      // ... other headers
    };
    if (req.method === 'OPTIONS') {
      return new Response(null, { headers });
    }
    if (!buildingId || typeof buildingId !== 'string') {
      return new Response(
        JSON.stringify({ error: 'Invalid or missing buildingId.' }),
        { status: 400, headers: corsHeaders }
      );
    }

    console.log('Received buildingId:', buildingId);

    // Check if the building exists
    const { data: building, error: buildingError } = await supabase
      .from('sections')
      .select('*')
      .eq('id', buildingId)
      .single();

    if (buildingError || !building) {
      return new Response(
        JSON.stringify({ error: `Building with ID ${buildingId} does not exist.` }),
        { status: 404, headers: corsHeaders }
      );
    }

    console.log('Building found:', building);

    // Check for linked rooms
    const { data: linkedRooms, error: roomError } = await supabase
      .from('rooms')
      .select('*')
      .eq('section_id', buildingId);

    if (roomError) {
      return new Response(
        JSON.stringify({ error: 'Error checking linked rooms.' }),
        { status: 500, headers: corsHeaders }
      );
    }

    if (linkedRooms && linkedRooms.length > 0) {
      console.log('Linked rooms found:', linkedRooms);
      return new Response(
        JSON.stringify({
          error: `Building with ID ${buildingId} has linked rooms. Please resolve linked rooms before deleting.`,
          linkedRooms,
        }),
        { status: 400, headers: corsHeaders }
      );
    }

    // Update the `is_deleted` field and set `deleted_at` for the building
    const { error: updateError } = await supabase
      .from('sections')
      .update({ 
        is_deleted: true,
        deleted_at: new Date().toISOString()
      })
      .eq('id', buildingId);
    
    if (updateError) {
      return new Response(
        JSON.stringify({ error: 'Error updating the is_deleted field and deleted_at for the building.' }),
        { status: 500, headers: corsHeaders }
      );
    }
    
    console.log('Building marked as deleted and timestamp set successfully.');

    console.log('Building marked as deleted successfully.');

    return new Response(
      JSON.stringify({ message: `Building with ID ${buildingId} marked as deleted successfully.` }),
      { status: 200, headers: corsHeaders }
    );
  } catch (error) {
    console.error('Error:', error.message);
    return new Response(
      JSON.stringify({ error: error.message }),
      { status: 500, headers: corsHeaders }
    );
  }
});
