// Follow this setup guide to integrate the Deno language server with your editor:
// https://deno.land/manual/getting_started/setup_your_environment
// This enables autocomplete, go to definition, etc.

// Setup type definitions for built-in Supabase Runtime APIs
import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import {createClient} from "npm:@supabase/supabase-js@2.39.3"
// import { PrismaClient } from '@prisma/client';
// //
// // // Initialize Prisma client
// const prisma = new PrismaClient();
const supabase = createClient(
    Deno.env.get("SUPABASE_URL")!,
    Deno.env.get("SUPABASE_ANON_KEY")!,
)
console.log("Hello from Functions!")

Deno.serve(async (req) => {
  const { id } = await req.json();

  // Fetch the inmate's info before deleting
  const { data: inmate, error: fetchError } = await supabase
      .from('inmates')
      .select('*')
      .eq('id', id)
      .single();

  if (fetchError) {
    return new Response(
        JSON.stringify({ error: fetchError.message }),
        { status: 400, headers: { "Content-Type": "application/json" } },
    );
  }

  // Proceed to delete the inmate
  const { error: deleteError } = await supabase
      .from('inmates')
      .delete()
      .eq('id', id);

  if (deleteError) {
    return new Response(
        JSON.stringify({ error: deleteError.message }),
        { status: 400, headers: { "Content-Type": "application/json" } },
    );
  }

  return new Response(
      JSON.stringify({ message: 'Inmate deleted', inmate }),
      { headers: { "Content-Type": "application/json" } },
  );
});

/* To invoke locally:

  1. Run `supabase start` (see: https://supabase.com/docs/reference/cli/supabase-start)
  2. Make an HTTP request:

  curl -i --location --request POST 'http://127.0.0.1:54321/functions/v1/delete_inmate' \
    --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0' \
    --header 'Content-Type: application/json' \
    --data '{"name":"Functions"}'

*/
