import 'https://esm.sh/@supabase/functions-js/src/edge-runtime.d.ts';
import { createClient } from 'npm:@supabase/supabase-js@2.39.3';

const supabase = createClient(Deno.env.get('SUPABASE_URL')!, Deno.env.get('SUPABASE_ANON_KEY')!);

export const corsHeaders = {
  'Access-Control-Allow-Origin': '*', // Replace with your frontend URL
  'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Max-Age': '86400',
};

Deno.serve(async (req) => {
  try {
    const { roomId } = await req.json();
    const headers = {
      'Access-Control-Allow-Origin': '*', // Replace with your frontend URL
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
      'Access-Control-Max-Age': '86400',
      // ... other headers
    };
    if (req.method === 'OPTIONS') {
      return new Response(null, { headers });
    }
    if (!roomId || typeof roomId !== 'string') {
      return new Response(
        JSON.stringify({ error: 'Invalid or missing roomId.' }),
        { status: 400, headers: corsHeaders }
      );
    }

    console.log('Received roomId:', roomId);

    // Check if the room exists
    const { data: room, error: roomError } = await supabase
      .from('rooms')
      .select('*')
      .eq('id', roomId)
      .single();

    if (roomError || !room) {
      return new Response(
        JSON.stringify({ error: `Room with ID ${roomId} does not exist.` }),
        { status: 404, headers: corsHeaders }
      );
    }

    console.log('Room found:', room);

    // // Check for linked rooms
    const { data: linkedMeetings, error: meetingssError } = await supabase
      .from('meetings')
      .select('*')
      .eq('room_id', roomId);

    // if (meetingssError) {
    //   return new Response(
    //     JSON.stringify({ error: 'Error checking linked meetings.' }),
    //     { status: 500, headers: corsHeaders }
    //   );
    // }
    //
    if (linkedMeetings && linkedMeetings.length > 0) {
      console.log('Linked rooms found:', linkedMeetings);
      return new Response(
        JSON.stringify({
          error: `Room with ID ${roomId} has linked meetings. Please resolve linked meetings   before deleting.`,
          linkedMeetings,
        }),
        { status: 400, headers: corsHeaders }
      );
    }

    // Update the `is_deleted` field and set `deleted_at` for the room
    const { error: updateError } = await supabase
      .from('rooms')
      .update({
        is_deleted: true,
        deleted_at: new Date().toISOString(),
        active: false,
      })
      .eq('id', roomId);

    if (updateError) {
      return new Response(
        JSON.stringify({ error: 'Error updating the is_deleted field and deleted_at for the room.' }),
        { status: 500, headers: corsHeaders }
      );
    }

    console.log('Room marked as deleted and timestamp set successfully.');

    console.log('Room marked as deleted successfully.');

    return new Response(
      JSON.stringify({ message: `Room with ID ${roomId} marked as deleted successfully.` }),
      { status: 200, headers: corsHeaders }
    );
  } catch (error) {
    console.error('Error:', error.message);
    return new Response(
      JSON.stringify({ error: error.message }),
      { status: 500, headers: corsHeaders }
    );
  }
});
