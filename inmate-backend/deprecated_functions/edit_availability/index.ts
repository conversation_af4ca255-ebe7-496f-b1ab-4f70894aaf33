import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import {createClient} from "npm:@supabase/supabase-js@2.39.3";

const supabase = createClient(
    Deno.env.get("SUPABASE_URL")!,
    Deno.env.get("SUPABASE_ANON_KEY")!
);



export const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

console.log("Hello from Functions!");
Deno.serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }
  const { inmate_id, data: apiData } = await req.json();

  // Fetch the inmate's availability settings from the database
  const { data: inmateAvailabiltyData, error: fetchError } = await supabase
      .from('inmate_availability_settings')
      .select('*')
      .eq('inmate_id', inmate_id);

  if (fetchError) {
    return new Response(
        JSON.stringify({ error: fetchError.message }),
        { status: 400, headers: { ...corsHeaders,"Content-Type": "application/json" } },
    );
  }

  // Convert database data to a map for quick lookup by "day"
  const dbDataMap = new Map(inmateAvailabiltyData.map(record => [record.day, record]));

  // Iterate over the API data
  for (const apiRecord of apiData) {
    const dbRecord = dbDataMap.get(apiRecord.day);

    if (dbRecord) {
      // Record exists in both database and API data -> update it
      const { available_from, available_to } = apiRecord;

      const { error: updateError } = await supabase
        .from('inmate_availability_settings')
        .update({ available_from, available_to })
        .eq('id', dbRecord.id);

      if (updateError) {
        return new Response(
          JSON.stringify({ error: `Update failed: ${updateError.message}` }),
          { status: 400, headers: { ...corsHeaders,"Content-Type": "application/json" } },
        );
      }

      // Remove the record from the dbDataMap as it's already handled
      dbDataMap.delete(apiRecord.day);

    } else {
      // Record exists in API data but not in the database -> create new record
      const { day, available_from, available_to } = apiRecord;

      const { error: insertError } = await supabase
        .from('inmate_availability_settings')
        .insert({ day, available_from, available_to, inmate_id });

      if (insertError) {
        return new Response(
          JSON.stringify({ error: `Insert failed: ${insertError.message}` }),
          { status: 400, headers: { ...corsHeaders,"Content-Type": "application/json" } },
        );
      }
    }
  }

  // Records left in dbDataMap are those that exist in the database but not in the API data -> delete them
  for (const dbRecord of dbDataMap.values()) {
    const { error: deleteError } = await supabase
      .from('inmate_availability_settings')
      .delete()
      .eq('id', dbRecord.id);

    if (deleteError) {
      return new Response(
        JSON.stringify({ error: `Delete failed: ${deleteError.message}` }),
        { status: 400, headers: { ...corsHeaders,"Content-Type": "application/json" } },
      );
    }
  }

  return new Response(
    JSON.stringify({ message: 'Inmate availability settings successfully updated' }),
    { headers: { ...corsHeaders,"Content-Type": "application/json" } },
  );
});
