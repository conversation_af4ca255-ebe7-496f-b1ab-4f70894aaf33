import 'https://esm.sh/@supabase/functions-js/src/edge-runtime.d.ts';
import { createClient } from 'npm:@supabase/supabase-js@2.39.3';

const supabase = createClient(Deno.env.get('SUPABASE_URL')!, Deno.env.get('SUPABASE_ANON_KEY')!);

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
  'Access-Control-Allow-Headers': 'Authorization, X-Client-Info, apikey, Content-Type',
  'Access-Control-Max-Age': '86400',
};

const isValidTimeFormat = (time: string): boolean =>
  /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$/.test(time);

const validateEditInput = (section_id: any, buildingCode: any, buildingAvailability: any, filteredDays: any[]) => {
  if (!section_id || typeof section_id !== 'string') {
    throw new Error('Invalid section_id: must be a valid string.');
  }
  if (!buildingCode || typeof buildingCode !== 'string') {
    throw new Error('Invalid buildingCode: must be a non-empty string.');
  }
  if (typeof buildingAvailability !== 'boolean') {
    throw new Error('Invalid buildingAvailability: must be a boolean.');
  }
  if (!Array.isArray(filteredDays) || filteredDays.length === 0) {
    throw new Error('Invalid filteredDays: must be a non-empty array.');
  }

  // for (const { day, times } of filteredDays) {
  //   if (!day || typeof day !== 'string') {
  //     throw new Error('Invalid day: must be a non-empty string.');
  //   }
  //   if (!Array.isArray(times) || times.length === 0) {
  //     throw new Error(`Invalid times for day ${day}: must be a non-empty array.`);
  //   }
  //   for (const { from, to } of times) {
  //     if (!from || !to || !isValidTimeFormat(from) || !isValidTimeFormat(to)) {
  //       throw new Error(`Invalid time format in times for day ${day}: must be in HH:mm:ss format.`);
  //     }
  //   }
  // }
};

Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 204,
      headers: {
        ...corsHeaders,
        'Access-Control-Allow-Origin': '*', // Specific origin
        'Access-Control-Allow-Headers': 'Authorization, X-Client-Info, apikey, Content-Type'
      }
    });
  }

  try {
    const { section_id, buildingCode, buildingAvailability, filteredDays } = await req.json();

    // Validate input
    validateEditInput(section_id, buildingCode, buildingAvailability, filteredDays);

    // Ensure the section_id is valid
    const { data: sectionData, error: sectionError } = await supabase
      .from('sections')
      .select('id')
      .eq('id', section_id)
      .single();

    if (sectionError || !sectionData) {
      throw new Error(`Invalid section_id: ${section_id} does not exist.`);
    }

    // Update section details
    const { error: updatesectionsError } = await supabase
      .from('sections')
      .update({ name: buildingCode, is_available: buildingAvailability })
      .eq('id', section_id);

    if (updatesectionsError) {
      throw new Error(`Error updating sections table: ${updatesectionsError.message}`);
    }

    // Fetch all relevant setting IDs
    const { data: settingsData, error: fetchSettingsError } = await supabase
      .from('section_availability_settings')
      .select('id')
      .eq('section_id', section_id);

    if (fetchSettingsError) {
      throw new Error(`Error fetching availability settings: ${fetchSettingsError.message}`);
    }

    const settingIds = settingsData.map(({ id }: { id: number }) => id);

    // Delete existing availability times
    if (settingIds.length > 0) {
      const { error: deleteTimesError } = await supabase
        .from('availability_times')
        .delete()
        .in('setting_id', settingIds);

      if (deleteTimesError) {
        throw new Error(`Error deleting availability times: ${deleteTimesError.message}`);
      }
    }

    // Delete old settings
    const { error: deleteSettingsError } = await supabase
      .from('section_availability_settings')
      .delete()
      .eq('section_id', section_id);

    if (deleteSettingsError) {
      throw new Error(`Error deleting old section_availability_settings: ${deleteSettingsError.message}`);
    }

    // Insert new settings
    const availabilitySettingsToInsert = filteredDays.map(({ day }) => ({
      section_id: section_id,
      day,
    }));

    const { data: sectionAvailabilities, error: availabilityError } = await supabase
      .from('section_availability_settings')
      .insert(availabilitySettingsToInsert)
      .select('id, day');

    if (availabilityError || !sectionAvailabilities) {
      throw new Error(`Error inserting into section_availability_settings: ${availabilityError?.message || 'No data returned'}`);
    }

    const dayToSettingId = sectionAvailabilities.reduce((acc, { id, day }) => {
      acc[day] = id;
      return acc;
    }, {} as Record<string, number>);

    // Insert new availability times
    const availabilityTimesToInsert = filteredDays.flatMap(({ day, times }) =>
      times.map(({ from, to }) => ({
        setting_id: dayToSettingId[day],
        type: 'section',
        available_from: from,
        available_to: to,
      }))
    );

    const { error: timesError } = await supabase
      .from('availability_times')
      .insert(availabilityTimesToInsert);

    if (timesError) {
      throw new Error(`Error inserting into availability_times: ${timesError.message}`);
    }

    return new Response(
      JSON.stringify({ message: 'Section and availability updated successfully' }),
      {
        headers: {
          ...corsHeaders,
          'Access-Control-Allow-Origin': '*',
          'Content-Type': 'application/json'
        },
        status: 200,
      }
    );
  } catch (error) {
    const errorMessage = (error as Error).message || 'An unknown error occurred.';
    return new Response(
      JSON.stringify({
        error: 'An error occurred while processing your request.',
        message: errorMessage,
        status_code: 400,
        headers: {
          'content_length': req.headers.get('content-length'),
          'content_type': req.headers.get('content-type'),
          'date': req.headers.get('date'),
          'server': req.headers.get('server'),
        },
        hint: 'Please verify your request body and ensure all required fields are included.',
      }),
      {
        headers: {
          ...corsHeaders,
          'Access-Control-Allow-Origin': '*',
          'Content-Type': 'application/json'
        },
        status: 400,
      }
    );
  }
});