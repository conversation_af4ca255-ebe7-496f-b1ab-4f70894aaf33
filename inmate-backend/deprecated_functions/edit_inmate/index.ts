// Follow this setup guide to integrate the Deno language server with your editor:
// https://deno.land/manual/getting_started/setup_your_environment
// This enables autocomplete, go to definition, etc.

// Setup type definitions for built-in Supabase Runtime APIs
import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { createClient } from 'npm:@supabase/supabase-js@2.39.3';

const supabase = createClient(Deno.env.get('SUPABASE_URL')!, Deno.env.get('SUPABASE_ANON_KEY')!);


// Helper to add CORS headers
function withCors(response) {
  response.headers.set("Access-Control-Allow-Origin", "*");
  response.headers.set("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
  response.headers.set("Access-Control-Allow-Headers", "Content-Type, Authorization");
  return response;
}

Deno.serve(async (req) => {
  if (req.method === "OPTIONS") {
    return withCors(new Response(null, { status: 204 }));
  }

  if (req.method !== "POST") {
    return withCors(new Response("Method Not Allowed", { status: 405 }));
  }

  try {
    const {
      id, // Added ID to identify the inmate
      full_name,
      national_id,
      nationality,
      assigned_building,
      assigned_room,
      status,
      number_of_visits,
      availability,
      is_default,
      inmate_code,
       
    } = await req.json();

    console.log("Received request data:", {
      id,
      full_name,
      national_id,
      assigned_building,
      assigned_room,
      status,
      number_of_visits,
      availability,
      is_default,
    });

    // Validate required fields
    if (!id) {
      return withCors(
        new Response(
          JSON.stringify({ error: "Inmate ID is required" }),
          { status: 400 }
        )
      );
    }

    // Check if inmate exists
    const { data: existingInmate, error: findError } = await supabase
      .from("inmates")
      .select("*")
      .eq("id", id)
      .single();

    if (findError || !existingInmate) {
      console.error("Inmate not found:", findError);
      return withCors(
        new Response(
          JSON.stringify({ error: "Inmate not found" }),
          { status: 404 }
        )
      );
    }

    console.log("Existing inmate found:", existingInmate);

    // Determine default flags
    let is_building_default = false;
    let is_room_default = false;

    if (is_default) {
      is_building_default = true;
      if (assigned_room) {
        is_room_default = true;
        is_building_default = false;
      }
    }

    // Update inmate details
    const { error: updateError } = await supabase
      .from("inmates")
      .update({
        full_name,
        national_id,
        nationality,
        number_of_visits,
        section_id: assigned_building,
        room_id: assigned_room || null,
        status,
        is_building_default,
        is_room_default,
        inmate_code,
         
      },)
      .eq("id", id);

    if (updateError) {
      console.error("Error updating inmate details:", updateError);
      return withCors(
        new Response(
          JSON.stringify({ error: "Error updating inmate details" }),
          { status: 500 }
        )
      );
    }

    console.log("Inmate updated successfully:", id);

    // Skip availability if is_default is true
    if (!is_default && availability && availability.length > 0) {
      // Delete existing availability settings for this inmate
      const { error: deleteError } = await supabase
        .from("inmate_availability_settings")
        .delete()
        .eq("inmate_id", id);

      if (deleteError) {
        console.error("Error deleting old availability settings:", deleteError);
        return withCors(
          new Response(
            JSON.stringify({ error: "Error updating availability settings" }),
            { status: 500 }
          )
        );
      }

      console.log("Old availability settings deleted for inmate:", id);

      for (const item of availability) {
        const { day, times } = item;

        // Insert new availability settings
        const { data: availabilitySetting, error: availabilityError } = await supabase
          .from("inmate_availability_settings")
          .insert([
            {
              day,
              inmate_id: id,
            },
          ])
          .select("*")
          .single();

        if (availabilityError) {
          console.error("Error adding availability settings:", availabilityError);
          return withCors(
            new Response(
              JSON.stringify({ error: "Error adding availability settings" }),
              { status: 500 }
            )
          );
        }

        console.log("Availability setting added:", availabilitySetting);

        const settingId = availabilitySetting.id;

        // Insert times into AvaliabilityTimes
        if (times && times.length > 0) {
          const timesData = times.map((time) => ({
            setting_id: settingId,
            type: "inmate",
            available_from: time.from,
            available_to: time.to,
          }));

          const { error: timesError } = await supabase
            .from("availability_type")
            .insert(timesData);

          if (timesError) {
            console.error("Error adding availability times:", timesError);
            return withCors(
              new Response(
                JSON.stringify({ error: "Error adding availability times" }),
                { status: 500 }
              )
            );
          }

          console.log("Availability times added for setting:", settingId);
        }
      }
    }

    console.log("Inmate update process completed successfully");
    return withCors(
      new Response(
        JSON.stringify({ message: "Inmate updated successfully" }),
        { status: 200 }
      )
    );
  } catch (error) {
    console.error("Unexpected error:", error);
    return withCors(
      new Response(
        JSON.stringify({ error: "Internal Server Error" }),
        { status: 500 }
      )
    );
  }
});

/* To invoke locally:

  1. Run `supabase start` (see: https://supabase.com/docs/reference/cli/supabase-start)
  2. Make an HTTP request:

  curl -i --location --request POST 'http://127.0.0.1:54321/functions/v1/create_inmate' \
    --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0' \
    --header 'Content-Type: application/json' \
    --data '{"name":"Functions"}'

*/
