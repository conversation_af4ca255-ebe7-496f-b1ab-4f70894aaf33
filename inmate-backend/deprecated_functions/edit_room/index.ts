import 'https://esm.sh/@supabase/functions-js/src/edge-runtime.d.ts';
import { createClient } from 'npm:@supabase/supabase-js@2.39.3';

const supabase = createClient(Deno.env.get('SUPABASE_URL')!, Deno.env.get('SUPABASE_ANON_KEY')!);

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Max-Age': '86400',
};

const isValidTimeFormat = (time: string): boolean =>
  /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$/.test(time);

const validateEditInput = (roomId: any, roomCode: any, roomAvailability: any, isBuildingDefault: any, filteredDays: any[]) => {
  if (!roomId || typeof roomId !== 'string') {
    throw new Error('Invalid roomId: must be a valid string.');
  }
  if (!roomCode || typeof roomCode !== 'string') {
    throw new Error('Invalid roomCode: must be a non-empty string.');
  }
  if (typeof roomAvailability !== 'boolean') {
    throw new Error('Invalid roomAvailability: must be a boolean.');
  }
  if (typeof isBuildingDefault !== 'boolean') {
    throw new Error('Invalid isBuildingDefault: must be a boolean.');
  }
  if (!isBuildingDefault && (!Array.isArray(filteredDays) || filteredDays.length === 0)) {
    throw new Error('Invalid filteredDays: must be a non-empty array when isBuildingDefault is false.');
  }

  // if (!isBuildingDefault) {
  //   for (const { day, times } of filteredDays) {
  //     if (!day || typeof day !== 'string') {
  //       throw new Error('Invalid day: must be a non-empty string.');
  //     }
  //     if (!Array.isArray(times) || times.length === 0) {
  //       throw new Error(`Invalid times for day ${day}: must be a non-empty array.`);
  //     }
  //     for (const { from, to } of times) {
  //       if (!from || !to || !isValidTimeFormat(from) || !isValidTimeFormat(to)) {
  //         throw new Error(`Invalid time format in times for day ${day}: must be in HH:mm:ss format.`);
  //       }
  //     }
  //   }
  // }
};

Deno.serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const { roomId, roomCode, roomAvailability, isBuildingDefault, filteredDays } = await req.json();

    // Validate input
    validateEditInput(roomId, roomCode, roomAvailability, isBuildingDefault, filteredDays);

    // Ensure the roomId is valid
    const { data: roomData, error: roomError } = await supabase
      .from('rooms')
      .select('id')
      .eq('id', roomId)
      .single();

    if (roomError || !roomData) {
      throw new Error(`Invalid roomId: ${roomId} does not exist.`);
    }

    // Update room details
    const { error: updateRoomError } = await supabase
      .from('rooms')
      .update({
        name: roomCode,
        active: roomAvailability,
        is_building_default: isBuildingDefault,
      })
      .eq('id', roomId);

    if (updateRoomError) {
      throw new Error(`Error updating Room table: ${updateRoomError.message}`);
    }

    if (!isBuildingDefault) {
      // Step 1: Fetch all relevant setting IDs for the room
      const { data: settingsData, error: fetchSettingsError } = await supabase
        .from('room_availability_settings')
        .select('id')
        .eq('room_id', roomId);

      if (fetchSettingsError) {
        throw new Error(`Error fetching availability settings: ${fetchSettingsError.message}`);
      }

      const settingIds = settingsData.map(({ id }: { id: string }) => id);

      // Step 2: Delete all availability times for the fetched setting IDs
      const { error: deleteTimesError } = await supabase
        .from('availability_type')
        .delete()
        .in('setting_id', settingIds);

      if (deleteTimesError) {
        throw new Error(`Error deleting availability times: ${deleteTimesError.message}`);
      }

      // Step 3: Delete old RoomAvaliabilitySettings
      const { error: deleteSettingsError } = await supabase
        .from('room_availability_settings')
        .delete()
        .eq('room_id', roomId);

      if (deleteSettingsError) {
        throw new Error(`Error deleting old RoomAvaliabilitySettings: ${deleteSettingsError.message}`);
      }

      // Step 4: Insert updated RoomAvaliabilitySettings
      const availabilitySettingsToInsert = filteredDays.map(({ day }) => ({
        room_id: roomId,
        day,
        isAvailable : true
      }));

      const { data: roomAvailabilities, error: availabilityError } = await supabase
        .from('room_availability_settings')
        .insert(availabilitySettingsToInsert)
        .select('id, day');

      if (availabilityError || !roomAvailabilities) {
        throw new Error(`Error inserting into room_availability_settings: ${availabilityError?.message || 'No data returned'}`);
      }

      const dayToSettingId = roomAvailabilities.reduce((acc, { id, day }) => {
        acc[day] = id;
        return acc;
      }, {} as Record<string, string>);

      // Step 5: Insert updated AvaliabilityTimes
      const availabilityTimesToInsert = filteredDays.flatMap(({ day, times }) =>
        times.map(({ from, to }) => ({
          setting_id: dayToSettingId[day],
          type: 'room',
          available_from: from,
          available_to: to,
        }))
      );

      const { error: timesError } = await supabase
        .from('availability_type')
        .insert(availabilityTimesToInsert);

      if (timesError) {
        throw new Error(`Error inserting into AvaliabilityTimes: ${timesError.message}`);
      }
    }

    return new Response(
      JSON.stringify({ message: 'Room and availability updated successfully' }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    );
  } catch (error) {
    return new Response(
      JSON.stringify({ error: (error as Error).message }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      }
    );
  }
});