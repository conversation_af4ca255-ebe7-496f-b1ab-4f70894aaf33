// Follow this setup guide to integrate the Deno language server with your editor:
// https://deno.land/manual/getting_started/setup_your_environment
// This enables autocomplete, go to definition, etc.

// Setup type definitions for built-in Supabase Runtime APIs

// import { error } from "console";
import "https://esm.sh/@supabase/functions-js/src/edge-runtime.d.ts"
import {createClient} from "npm:@supabase/supabase-js@2.39.3"


const supabase = createClient(
    Deno.env.get("SUPABASE_URL")!,
    Deno.env.get("SUPABASE_ANON_KEY")!,
)


/**
 * Validates if the input string is a valid date in "YYYY-MM-DD" format
 * and checks if the date is in the future.
 *
 * @param {string} dateString - The input date as a string.
 * @returns {Object} - An object with a boolean `isValid` and a message.
 */
function validateDate(dateString) {
    // Regex to ensure date is in YYYY-MM-DD format
    const regex = /^\d{4}-\d{2}-\d{2}$/;

    // If the date string doesn't match the regex, return an invalid response
    if (!regex.test(dateString)) {
        return { isValid: false, message: "Invalid date format. Use YYYY-MM-DD." };
    }

    const date = new Date(dateString);
    const now = new Date();
    now.setHours(0, 0, 0, 0); // Reset today's time to midnight

    // Check if the provided date is in the past
    if (date < now) {
        return { isValid: false, message: "The date has already passed." };
    }

    // Return a valid response if the date is in the correct format and is in the future
    return { isValid: true, message: "" };
}



function calculateVisitTimes(visits) {
  return visits.map(visit => {
      // Parse the datetime string to a Date object
      const startDateTime = new Date(visit.datetime);
      
      // Extract the start time in "HH:mm:ss" format
      const startTime = startDateTime.toTimeString().split(' ')[0]; // e.g., "09:00:00"
      
      // Calculate the end time by adding duration to the start time
      const endDateTime = new Date(startDateTime.getTime() + visit.duration * 60000); // duration in minutes
      const endTime = endDateTime.toTimeString().split(' ')[0]; // e.g., "10:30:00"
      
      // Return the object with start and end times
      return {
          start: startTime,
          end: endTime
      };
  });
}


function removeOverlappingIntervals(result, inmateVisits) {
  // Function to check if two time intervals overlap
  function isOverlapping(start1, end1, start2, end2) {
      return (start1 < end2 && start2 < end1);
  }

  // Filter the result to remove overlapping intervals
  return result.filter(interval => {
      const intervalStart = interval.start;
      const intervalEnd = interval.end;

      // Check against all visit intervals for overlap
      for (const visit of inmateVisits) {
          if (isOverlapping(intervalStart, intervalEnd, visit.start, visit.end)) {
              return false; // This interval overlaps, so exclude it
          }
      }
      return true; // This interval does not overlap, so keep it
  });
}



/**
 * Get the start (Monday) and end (Sunday) of the week for a given date.
 *
 * @param {string} dateString - The input date in "YYYY-MM-DD" format.
 * @returns {Object} - An object with `start` (Monday) and `end` (Sunday) as Date objects.
 */
function getWeekStartAndEnd(dateString) {
    const date = new Date(dateString);
    const day = date.getDay();
    const diff = (day + 6) % 7; // Find out how many days to subtract to get to Monday
    const monday = new Date(date);
    monday.setDate(date.getDate() - diff); // Set the date to the previous Monday
    const sunday = new Date(monday);
    sunday.setDate(monday.getDate() + 6); // Set the date to the upcoming Sunday
    return { start: monday, end: sunday }; // Return start (Monday) and end (Sunday) of the week
}
/**
 * Converts a time string (HH:MM:SS) into a Date object.
 *
 * @param {string} timeString - The input time as a string.
 * @returns {Date} - A Date object with hours, minutes, and seconds set.
 */
function createDateFromTimeString(timeString) {
    const [hours, minutes, seconds] = timeString.split(":").map(Number); // Split time into components
    const now = new Date();
    now.setHours(hours, minutes, seconds, 0); // Set the Date object with the provided time
    return now; // Return the updated Date object
}

/**
 * Generates 30-minute intervals between two time points.
 *
 * @param {string} availableFrom - The start time (HH:MM:SS).
 * @param {string} availableTo - The end time (HH:MM:SS).
 * @returns {Array} - An array of intervals, each having a start and end time in "HH:MM:SS".
 */
function getTimeIntervals(availableFrom, availableTo) {
    const intervals = [];
    const start = createDateFromTimeString(availableFrom); // Convert start time to Date object
    const end = createDateFromTimeString(availableTo); // Convert end time to Date object

    // Loop through the times, creating 30-minute intervals
    while (start < end) {
        const intervalEnd = new Date(start);
        intervalEnd.setMinutes(start.getMinutes() + 30); // Add 30 minutes to create the end of the interval

        intervals.push({
            start: start.toTimeString().slice(0, 8), // Format start time as "HH:MM:SS"
            end: intervalEnd.toTimeString().slice(0, 8), // Format end time as "HH:MM:SS"
        });

        start.setMinutes(start.getMinutes() + 30); // Move start to the next 30-minute slot
    }

    return intervals; // Return array of time intervals
}

/**
 * Filters out occupied time slots from available time slots.
 *
 * @param {Array} availableTimes - Array of available time intervals.
 * @param {Array} occupiedTimes - Array of occupied time intervals.
 * @returns {Array} - Filtered array of available time intervals that don't overlap with occupied ones.
 */
function removeOccupiedTimes(availableTimes, occupiedTimes) {
  const occupiedTimes2 = [
    { start: "11:00:00", end: "12:30:00" }]
  console.log("remove availableTimes : >>>>>> " , availableTimes)
  console.log("remove occupiedTimes : >>>>>> " , occupiedTimes)

  return availableTimes.filter((available) => {
      return !occupiedTimes.some((occupied) => {
          // Check if available time overlaps with occupied time
          return (
              (available.start < occupied.end && available.end > occupied.start)// Starts exactly when occupied ends
          );
      });
  });
}



/**
 * Calculates the end time based on a start time and a given duration (in minutes).
 *
 * @param {string} startTime - The start time in "HH:MM".
 * @param {number} duration - The duration in minutes.
 * @returns {string} - The calculated end time in "HH:MM".
 */
function calculateEndTime(startTime, duration) {
    const [hours, minutes] = startTime.split(":").map(Number); // Split the start time into hours and minutes
    const endDate = new Date();
    endDate.setHours(hours, minutes + duration); // Add duration to minutes
    return `${endDate.getHours()}:${endDate.getMinutes()}`; // Return the end time in "HH:MM" format
}

/**
 * Extracts the time part (HH:MM:SS) from a datetime string.
 *
 * @param {string} datetime - The input datetime string.
 * @returns {string} - The time part of the datetime in "HH:MM:SS".
 */
function extractTimeFromDateTime(datetime) {
  const date = new Date(datetime);

  // Pad the hours, minutes, and seconds with leading zeros if they are less than 10
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');

  return `${hours}:${minutes}:${seconds}`;  
    // const date = new Date(datetime);
    // return `${date.getHours()}:${date.getMinutes()}`; // Return the extracted time
}

/**
 * Extracts the date part (YYYY-MM-DD) from a datetime string.
 *
 * @param {string} datetimeString - The input datetime string.
 * @returns {string} - The date part of the datetime in "YYYY-MM-DD".
 */
function extractDateFromDateTime(datetimeString) {
    const datetime = new Date(datetimeString);
    return datetime.toISOString().split("T")[0]; // Return the extracted date in "YYYY-MM-DD"
}

/**
 * Filters meetings to return only those that match a specific date.
 *
 * @param {Array} meetings - Array of meetings, each with a datetime.
 * @param {string} targetDate - The target date (in "YYYY-MM-DD") to filter by.
 * @returns {Array} - Array of meetings that match the target date.
 */
function filterMeetingsByDate(meetings, targetDate) {
    return meetings.filter((meeting) => {
        const meetingDate = meeting.datetime.split('T')[0]; // Extract the date part of the meeting datetime
        return meetingDate === targetDate; // Compare it with the target date
    });
}

/**
 * Get the day of the week (as a string) from a given date.
 *
 * @param {string} dateString - The input date in "YYYY-MM-DD" format.
 * @returns {string} - The day of the week (e.g., "MONDAY").
 */
function getDayOfWeek(dateString) {
    const date = new Date(dateString);
    const daysOfWeek = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
    return daysOfWeek[date.getUTCDay()]; // Return the day of the week as a string
}


export const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

Deno.serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }
  const { date,visitor_id, inmate_id} = await req.json();

  const {isValid,message} = validateDate(date)
  const dayOfWeek = getDayOfWeek(date); // Get the day of the week as enum
  
  if (!isValid) {
    return new Response(JSON.stringify({ message,error_code:10000,status:400  }), { headers: { ...corsHeaders, 'Content-Type': 'application/json' },status: 400 });
  }
  console.log("visitor_id====>>>>>", visitor_id);
  const visitor = await supabase
  .from('visitors') // Replace 'visitors' with your actual table name
  .select('*')
  .eq('id', visitor_id)
  .single();
  console.log("visitor====>>>>>", visitor);

  if (visitor.error || !visitor.data) {
    return new Response(JSON.stringify({ message: `Visitor ID does not exist.${visitor}`,error_code:10000,status:400  }), {headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 404 });
  }

    const { data, errorr } = await supabase
        .from('visitors') // Ensure the table name is correct (usually lowercase)
        .select('blocked')
        .eq('id', visitor_id)
        .single(); // Fetch a single visitor

// Logging the result to see the response structure
    console.log("visitorblocked====>>>>>", data);

    if (errorr) {
        console.error("Error fetching visitor:", errorr);
        return new Response(JSON.stringify({ message: "Error fetching visitor data.",error_code:10000,status:400  }), { headers: { ...corsHeaders, 'Content-Type': 'application/json' },status: 500 });
    }

// Check if visitor is found and blocked
    if (data && data.blocked === true) {
        return new Response(JSON.stringify({ message: "This visitor is blocked." ,error_code:10000,status:400 }), {headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 403 });
    }
    const inmate = await supabase
    .from('inmates') // Replace 'inmates' with your actual table name
    .select('*')
    .eq('id', inmate_id)
    .single();

  if (inmate.error || !inmate.data) {
    return new Response(JSON.stringify({ message: "Inmate ID does not exist.",error_code:10000,status:400  }), { headers: { ...corsHeaders, 'Content-Type': 'application/json' },status: 404 });
  }



  console.log("aaaaaaaaaaaaaaaaaaaaaaaaa" , inmate.data)

  if (inmate.data.unavailable){
    return new Response(JSON.stringify({ message: "Inmate is unavailable for visits.",error_code:10000,status:400  }), { headers: { ...corsHeaders, 'Content-Type': 'application/json' },status: 400 });

  }
  // ========================================================================== 
  // Check for pending visit requests
  const requestCheck = await supabase
    .from('visit_requests') // Replace with your actual table name
    .select('*')
    .eq('visitor_id', visitor_id)
    .eq('inmate_id', inmate_id)
    .eq('status', 'PENDING'); // Assuming 'status' field holds the request status


  if (requestCheck.error) {
    return new Response(JSON.stringify({ message: 'Error checking visit requests.',error_code:10000,status:400  }), {headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 400 });
  }

  if (requestCheck.data.length > 0) {
    return new Response(JSON.stringify({ message: "There are pending requests for this visitor and inmate." ,error_code:10000,status:409 }), {headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 409 });
  }


  // ================================================================
// ===================== check number of visit requests =======================

const { start, end } = getWeekStartAndEnd(date);
console.log("start ===>",start)
console.log("end ===>",end)
console.log("startiso ===>",start.toISOString())
console.log("end iso===>",end.toISOString())
// Check for visit requests within that week
// ========================== inmate requests in the week =============================
const visitRequestCount = await supabase
    .from('visit_requests')
    .select('id', { count: 'exact' })
    .eq('inmate_id', inmate_id) // Filter by inmate ID
    .in('status', ['APPROVED', 'COMPLETED', 'STARTS_SOON']) // Filter by status
    .gte('datetime', start.toISOString()) // Filter for start of the week
    .lte('datetime', end.toISOString()); // Filter for end of the week
if (visitRequestCount.error) {
  return new Response(JSON.stringify({ message: "Error counting visit requests.",error_code:10000,status:400  }), { headers: { ...corsHeaders, 'Content-Type': 'application/json' },status: 500 });
}

const count = visitRequestCount.count || 0;

const inmate_numberofVisits = await supabase
    .from('inmates')
    .select('number_of_visits')
    .eq('id', inmate_id)
    .single();

if (inmate_numberofVisits.error || !inmate_numberofVisits.data) {
  return new Response(JSON.stringify({ message: "Inmate ID does not exist." ,error_code:10000,status:400 }), { status: 404 });
}

const maxVisits = inmate_numberofVisits.data.number_of_visits;
// Check if the inmate has exceeded the limit
if (count >= maxVisits) {
  return new Response(JSON.stringify({ message: `Inmate has exceeded the limit of ${maxVisits} visit requests this week.` ,error_code:10000,status:400 }), { headers: { ...corsHeaders, 'Content-Type': 'application/json' },status: 403 });
}

// return new Response(JSON.stringify({ message: `Inmate has ${maxVisits - count} visits remaining this week.` }), { status: 200 });
// ====================
  // Query the inmate availability from the Supabase table
  let { data: inmate_availability_settings, error } = await supabase
      .from('inmate_availability_settings')
      .select("*")
      .eq('inmate_id', inmate_id)
      .eq('day', dayOfWeek);  // Check for matching available days (enum)
  console.log("inmate ************************************",inmate_availability_settings)
  console.log("day of week ========================", dayOfWeek)

  if (error) {
    console.log("eror in inmate availability>>>>>>>>>>>>>>" , error)
    // return res.status(500).json({ error: 'Error checking inmate availability' });
    return new Response(JSON.stringify({ message: "Error checking inmate availability" ,error_code:10000,status:404 }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 404,
    });
  }

  // Check if the inmate is available on the given day
  if (!inmate_availability_settings || inmate_availability_settings.length === 0) {
    // return res.status(400).json({ message: 'Inmate is not available on this day' });
    return new Response(JSON.stringify({ message: "Inmate is not available on this day",error_code:10000,status:400 }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 400,
    });
  }
  // console.log("================>>>>>vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv>>>>>>>>>>>>>>",inmate_data_availablity)
// ========================

    /////////=============check the visit if the time has
  // Assuming inmate.data contains 'available_from' and 'available_to' fields
  if (inmate.error || !inmate.data) {
    return new Response(JSON.stringify({ message: "Inmate ID does not exist." ,error_code:10000,status:404}), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 404,
    });
  }

  if (inmate.data.unavailable) {
    return new Response(
        JSON.stringify({ message: "Inmate is unavailable for visits.",error_code:10000,status:400 }),

        {headers: { ...corsHeaders, 'Content-Type': 'application/json' },
         status: 400 }
    );
  }

  // const availablity = await supabase
  //   .from('inmate_availability_settings')
  //   .select('*')
  //   .eq('inmate_id',inmate_id)

  console.log("avilalabuyeyfe=======",inmate_availability_settings)
    // Assuming inmate.data contains 'available_from' and 'available_to' fields
  let { available_from, available_to } = inmate_availability_settings[0];
  console.log("inmateAvailablity mn el table el gdyd from",available_from)
  console.log("inmateAvailablity mn el table el gdyd to",available_to)
  if (available_to === "00:00:00"){
    available_to = "24:00:00"
  }else if(available_to === "00:30:00"){
    available_to = "24:30:00"


  }
  console.log("inmateAvailablity mn el^^^^^^^^^^^^^^^^^^^^^^^^^^66",available_to)
  if (!available_from || !available_to) {
    return new Response(
        JSON.stringify({ message: "Inmate availability not specified." ,error_code:10000,status:400}),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400 }
    );
  }

  // Step 2: Fetch inmate data with section and room details
  const { data: inmateData, error: inmateError } = await supabase
      .from('inmates')
      .select('section_id')
      .eq('id', inmate_id)
      .single();

  if (inmateError || !inmateData) {
    return new Response(JSON.stringify({ message: "Inmate not found." ,error_code:10000,status:404}), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
       status: 404 });
  }
  console.log("aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa>>>>>>>>>>>>>>>>>>>>>>>>>>>>>..",inmateData);
  const { section_id } = inmateData;
  console.log("sssssssssssssss",section_id)
  // Step 3: Generate 30-minute intervals from available_from to available_to
  const inmateAvailableTimes = getTimeIntervals(available_from, available_to);
  console.log(inmateAvailableTimes,"<========================= available times")
// Step 5: Get visitor's visit times for the specific date
  const { data: visitorVisits, error: visitorVisitsError } = await supabase
      .from("visit_requests")
      .select(`
      datetime,
      duration,
      room_id (id , section_id (name) , name)  // Get room details via foreign key
    `)
      // .select('*')
      .eq("visitor_id", visitor_id)
      .in("status", ["APPROVED", "STARTS_SOON"]);
      // .eq("inmate_id", inmate_id);
  console.log(visitorVisits,"<<<<<<<<<<<<=========visitor visits")
  if (visitorVisitsError) {
    return new Response(
        JSON.stringify({ message: "Error fetching visitor visits.",error_code:10000,status:500 }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 500 }
    );
  }

  // Step 5: Extract time from the visit's datetime and calculate the start and end times
  const visitorTimes = visitorVisits
      .filter((visit) => new Date(visit.datetime).toISOString().split("T")[0] === date)
      .map((visit) => {
        const visitStartTime = extractTimeFromDateTime(visit.datetime); // Extract time from datetime
        const visitEndTime = calculateEndTime(visitStartTime, visit.duration); // Calculate end time
        console.log("((((((((((((((()))))))))))))))))))))) >>>>" , visitStartTime)
        return {
          start: visitStartTime,
          end: visitEndTime,
          // room: visit.room_id.name, // Include room from visit
        };
      });
  console.log("vvvvvvvvvvvvvvvvvvvvvv",visitorTimes)
  // Step 6: Fetch room IDs for the inmate's section
  const { data: sectionRooms, error: sectionRoomsError } = await supabase
      .from("Room")
      .select("id, name , section_id") // Get room ids and names
      .eq("section_id", section_id); // Filter by inmate's section
  console.log("sectionRoom===>",sectionRooms)
  if (sectionRoomsError || !sectionRooms.length) {
    return new Response(
        JSON.stringify({ message: "Error fetching section rooms or no rooms found.",error_code:10000, status:500}),
        {headers: { ...corsHeaders, 'Content-Type': 'application/json' },
         status: 500 }
    );
  }

  const roomIdsInSection = sectionRooms.map(room => room.section_id);
  console.log("idsection============>>>",roomIdsInSection)
  // Step 7: Fetch all the meetings in the rooms within the inmate's section for the specific date
  const currentDatetime = new Date().toISOString(); // Get the current date and time in ISO format
 console.log("sadddddddddddjldiojsfdadsfapinfdsaio",typeof currentDatetime)
  const { data: sectionMeetings, error: sectionMeetingsError } = await supabase
      .from("visit_requests")
      .select(`
    datetime, 
    duration, 
    room_id (id, name ,section_id)
    status
  `)
      .in('status', ['APPROVED', 'STARTS_SOON'])

        // .eq(`room_id (section_id)`, roomIdsInSection); // Fetch all meetings for the section's rooms
  console.log("=================================ddddddddddddddddddddddd=================",sectionMeetings)
  if (sectionMeetingsError) {
    console.error('Error fetching meetings:', sectionMeetingsError);
    return new Response(
        JSON.stringify({ message: "Error fetching section meetings." ,error_code:10000,status:500}),
        {headers: { ...corsHeaders, 'Content-Type': 'application/json' },
         status: 500 }
    );
  }





  // Step 1: Get the Inmate and the associated section ID
  const { data: inmateSection, error: sectionError } = await supabase
      .from('inmates')
      .select('section_id')
      .eq('id', inmate_id)
      // .single();
  console.log("inmateSection=========>>>",inmateSection)
  if (sectionError) {
    console.error("Error fetching inmate section:", sectionError);
    return;
  }

  const section_id = Object.values(inmateSection[0])
  console.log("seeeeeeeecccccccccccccccccccttttttttionnnnnnnn===================>>>>>>>>>",section_id)
// Step 2: Get all rooms in the inmate’s section
  const { data: sectionRoosms, error: roomsError } = await supabase
      .from('rooms')
      .select('id')
      .eq('section_id', section_id);

  if (roomsError) {
    console.error("Error fetching rooms in section:", roomsError);
    return;
  }
  console.log("sectionRoosmsssssssssssssssssssss",sectionRoosms)

  const roomIds = Object.values(sectionRoosms[0]) // Extract room IDs
  console.log("rooooooooooooooooooooooooooooooommmmmmmmmmmmmmmmm iidddddddddddddddddddddd",roomIds)
// Step 3: Get all visit requests for the rooms
  const { data: roomVisits, error: visitsError } = await supabase
      .from('visit_requests')
      .select('*') // Fetch all columns or specific columns as per your need
      .eq('room_id', roomIds) // Use the room IDs to filter visit requests
      .in("status", ['APPROVED', 'STARTS_SOON'])

  if (visitsError) {
    console.error("Error fetching room visits:", visitsError);
    return;
  }

  console.log('Visits in section rooms:', roomVisits);
    // Filter the results after fetching based on the date entered by the user
    const filteredMeetings = filterMeetingsByDate(roomVisits, date);

    console.log('Filtered Meetings:', filteredMeetings);
    console.log("roomIdsInSection:", roomIdsInSection);
    console.log("Date entered by user:", date);

// Step 8: Extract time from the visit's datetime and calculate the start and end times for filtered meetings
    const filteredMeetingTimes = filteredMeetings.map((meeting) => {
        const meetingStartTime = extractTimeFromDateTime(meeting.datetime);
        const meetingEndTime = calculateEndTime(meetingStartTime, meeting.duration);

        return {
            start: meetingStartTime,
            end: meetingEndTime,
            room: meeting.room_id.name, // Include room name
            date: meeting.datetime,
        };
    });
    const filterPastTimes = (availableTimes, supabaseTimeZoneOffset) => {
        // Get the current time and adjust based on your timezone offset
        const currentTime = new Date();
        const currentUTC = currentTime.getTime() + currentTime.getTimezoneOffset() * 60000; // Convert to UTC
        const adjustedCurrentTime = new Date(currentUTC + supabaseTimeZoneOffset * 3600000); // Adjust for Supabase timezone

        const adjustedCurrentHours = adjustedCurrentTime.getHours();
        const adjustedCurrentMinutes = adjustedCurrentTime.getMinutes();

        // Function to convert time strings ("HH:mm:ss") to minutes since the start of the day
        const getTimeInMinutes = (timeStr) => {
            const [hours, minutes] = timeStr.split(':').map(Number);
            return hours * 60 + minutes;
        };

        // Get current time in minutes
        const adjustedCurrentTimeInMinutes = adjustedCurrentHours * 60 + adjustedCurrentMinutes;

        // Filter the available times to remove past slots
        return availableTimes.filter((timeSlot) => {
            const startTimeInMinutes = getTimeInMinutes(timeSlot.start);
            return startTimeInMinutes >= adjustedCurrentTimeInMinutes; // Keep only future times
        });
    };
    const isToday = (date) => {
        const today = new Date();
        return date.getDate() === today.getDate() &&
            date.getMonth() === today.getMonth() &&
            date.getFullYear() === today.getFullYear();
    };

    const handleRequest = (requestDate, availableTimes) => {
        const requestDateObj = new Date(requestDate); // Convert request date to Date object

        if (isToday(requestDateObj)) {
            // Only call the function if the request is for today
            const filteredTimes = filterPastTimes(availableTimes, supabaseTimeZoneOffset);
            console.log("Filtered Times for Today:", filteredTimes);
            return filteredTimes;
        } else {
            console.log("Request is not for today. No filtering applied.");
            return availableTimes; // Or handle as needed
        }
    };
    const supabaseTimeZoneOffset = +3; // Adjust this value based on your Supabase timezone

// Step 9: Remove both the visitor's occupied times and filtered meeting times from inmate's available times
    const allOccupiedTimes = [...visitorTimes, ...filteredMeetingTimes];
    console.log("allOccupiedTimes" ,allOccupiedTimes)
    console.log("inmateAvailableTimes",inmateAvailableTimes)
    const updatedAvailableTimes = removeOccupiedTimes(inmateAvailableTimes, allOccupiedTimes);
    const filteredAvailableTimes = filterPastTimes(updatedAvailableTimes,supabaseTimeZoneOffset);
    console.log("updatedAvailableTimes >>>" , updatedAvailableTimes)
    const result = handleRequest(date, updatedAvailableTimes);
    console.log("result " , result)
    const visitRequestInmateData= await supabase
    .from('visit_requests')
    .select('*')
    .eq('inmate_id', inmate_id) // Filter by inmate ID
    .in('status', ['APPROVED', 'COMPLETED', 'STARTS_SOON']) // Filter by status
    .gte('datetime', start.toISOString()) // Filter for start of the week
    .lte('datetime', end.toISOString());


    // console.log("xzxzxzx 3333333 ****************** " , visitRequestInmateData)
    // console.log("2222222222222 ****************** " , visitRequestInmateData.data[0].datetime)

    // const startDateTime = new Date(visitRequestInmateData.data[0].datetime);
    
    // // Extract the start time in "HH:mm:ss" format
    // const startTime = startDateTime.toTimeString().split(' ')[0];
    // console.log("zzzzzzzzzzzzzzzzzzzzzzzzzz $$********* " , startTime)

    // const endDateTime = new Date(startDateTime.getTime() + visitRequestInmateData.data[0].duration * 60000); // duration in minutes
    // const endTime = endDateTime.toTimeString().split(' ')[0]; // "10:30:00"

    // console.log("wwwwwwwwwwwwwwwwwwwwwwwwww $$********* " , endTime)





// Step 10: Return the response with visitor's visit times, inmate's available times, room, section, and all meetings in the section for debugging
  const response = {
      result,
  };


  console.log("x0x0x0x0x00x0x00x0x00x0x00x0x00x0x" ,response.result )
  const visiteList = calculateVisitTimes(visitRequestInmateData.data)
  console.log("@@@@@@@@@@@@@@@@@@@@@@@@@@@@ " , visiteList)
  if(visitRequestInmateData){
    const new_result = removeOverlappingIntervals(response.result,visiteList)
    response.result = new_result
  }

  console.log("xxxxxxxxxxxxxxxxxxxxxx" , response.result.length)



  if(!response.result.length){
    return new Response(
      JSON.stringify({ message: "NO Available slots." ,error_code:10090,status:400}),
      {headers: { ...corsHeaders, 'Content-Type': 'application/json' },
       status: 400 }
  );
  }
  // Step 10: Return the response with the desired format
  return new Response(
      JSON.stringify({
        message: "Data fetched successfully",
        status: 200
        ,error_code:10000,
        data: response,
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
         ,
        status: 200 }
  );
});


/* To invoke locally:

  1. Run `supabase start` (see: https://supabase.com/docs/reference/cli/supabase-start)
  2. Make an HTTP request:

  curl -i --location --request POST 'http://127.0.0.1:54321/functions/v1/get_available_slots' \
    --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0' \
    --header 'Content-Type: application/json' \
    --data '{"name":"Functions"}'

*/
