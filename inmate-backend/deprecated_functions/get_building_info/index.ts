import 'https://esm.sh/@supabase/functions-js/src/edge-runtime.d.ts';
import { createClient } from 'npm:@supabase/supabase-js@2.39.3';

const supabase = createClient(Deno.env.get('SUPABASE_URL')!, Deno.env.get('SUPABASE_ANON_KEY')!);

const corsHeaders = {
  'Access-Control-Allow-Origin': '*', // Replace * with your frontend URL for tighter security
  'Access-Control-Allow-Methods': 'POST, GET, OPTIONS', // Specify allowed methods
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Max-Age': '86400', // Cache preflight response for 24 hours,


};

Deno.serve(async (req) => {
  try {
    const headers = {
      'Access-Control-Allow-Origin': '*', // Replace with your frontend URL
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
      'Access-Control-Max-Age': '86400',
      // ... other headers
    };
    if (req.method === 'OPTIONS') {
      return new Response(null, { headers });
    }

    const url = new URL(req.url);
    const section_id = url.searchParams.get('section_id');

    if (!section_id) {
      throw new Error('section_id is required');
    }

    // Step 1: Fetch section details including the is_available field
    const { data: sectionData, error: sectionError } = await supabase
      .from('sections')
      .select('*')
      .eq('id', section_id)
      .single();

    if (sectionError) {
      throw new Error(`Error fetching Section details: ${sectionError.message}`);
    }

    // Step 2: Fetch availability settings for the given section ID
    const { data: settingsData, error: settingsError } = await supabase
      .from('section_availability_settings')
      .select('id, day')
      .eq('section_id', section_id);

    if (settingsError) {
      throw new Error(`Error fetching Section_Availability_Settings: ${settingsError.message}`);
    }

    if (!settingsData || settingsData.length === 0) {
      return new Response(
        JSON.stringify({
          section_id,
          isAvailable: sectionData.is_available,
          section_name: sectionData.name,
          availability: [],
          message: 'No availability settings found for the given section_id'
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200,
        }
      );
    }

    // Step 3: Extract setting IDs
    const settingIds = settingsData.map(({ id }) => id);

    // Step 4: Fetch availability times for the fetched setting IDs
    const { data: timesData, error: timesError } = await supabase
      .from('availability_type')
      .select('setting_id, available_from, available_to')
      .in('setting_id', settingIds);

    if (timesError) {
      throw new Error(`Error fetching AvaliabilityTimes: ${timesError.message}`);
    }

    // Step 5: Combine settings and times into a structured response
    const availabilityInfo = settingsData.map((setting) => ({
      day: setting.day,
      settingId: setting.id,
      times: timesData
        .filter((time) => time.setting_id === setting.id)
        .map(({ available_from, available_to }) => ({
          from: available_from,
          to: available_to
        })),
    }));

    // Step 6: Return the final response with section details
    return new Response(
      JSON.stringify({
        section_id,
        isAvailable: sectionData.is_available,
        section_name: sectionData.name,
        availability: availabilityInfo,
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    );
  } catch (error) {
    return new Response(
      JSON.stringify({ error: (error as Error).message }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      }
    );
  }
});
