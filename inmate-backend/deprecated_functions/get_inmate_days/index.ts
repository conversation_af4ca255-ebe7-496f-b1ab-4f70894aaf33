import 'https://esm.sh/@supabase/functions-js/src/edge-runtime.d.ts';
import { createClient } from 'npm:@supabase/supabase-js@2.39.3';

// Initialize Supabase client
const supabase = createClient(
    Deno.env.get('SUPABASE_URL')!,
    Deno.env.get('SUPABASE_ANON_KEY')!
);

// CORS Headers
const corsHeaders = {
    'Access-Control-Allow-Origin': '*', // Replace * with your frontend URL for tighter security
    'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
    'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
    'Access-Control-Max-Age': '86400', // Cache preflight response for 24 hours
};

// Handle incoming requests
Deno.serve(async (req) => {
    try {
        // Handle CORS preflight request
        if (req.method === 'OPTIONS') {
            return new Response(null, { headers: corsHeaders });
        }

        // Parse URL parameters
        const url = new URL(req.url);
        const inmateId = url.searchParams.get('inmateId');
        if (!inmateId) throw new Error('inmateId is required');

        // Fetch inmate details
        const { data: inmateData, error: inmateError } = await supabase
            .from('inmates')
            .select(`
        id, full_name, national_id, number_of_visits, section_id(id), room_id(id, is_building_default, section_id), 
        deleted_at, is_building_default, is_room_default, is_deleted, inmate_code, nationality, status
      `)
            .eq('id', inmateId)
            .single();

        if (inmateError) throw new Error(`Error fetching Inmate details: ${inmateError.message}`);

        console.log("Inmate Data:", inmateData);

        // Fetch availability settings based on the inmate's attributes
        let settingsTable = 'inmate_availability_settings';
        let filterColumn = 'inmate_id';
        let filterValue = inmateId;

        if (inmateData.is_building_default) {
            settingsTable = 'section_availability_settings';
            filterColumn = 'section_id';
            filterValue = inmateData.section_id;
        } else if (inmateData.room_id?.is_building_default) {
            settingsTable = 'section_availability_settings';
            filterColumn = 'section_id';
            filterValue = inmateData.room_id.section_id;
        } else if (inmateData.is_room_default) {
            settingsTable = 'room_availability_settings';
            filterColumn = 'room_id';
            filterValue = inmateData.room_id.id;
        }

        // Fetch settings data
        const { data: settingsData, error: settingsError } = await supabase
            .from(settingsTable)
            .select('id, day')
            .eq(filterColumn, filterValue);

        if (settingsError) throw new Error(`Error fetching availability settings: ${settingsError.message}`);

        // Extract setting IDs
        const settingIds = settingsData.map(({ id }) => id);

        // Fetch availability times
        const { data: timesData, error: timesError } = await supabase
            .from('availability_type')
            .select('setting_id, available_from, available_to')
            .in('setting_id', settingIds);

        if (timesError) throw new Error(`Error fetching AvaliabilityTimes: ${timesError.message}`);

        // Structure response data
        const availabilityInfo = settingsData.map((setting) => ({
            day: setting.day,

        }));

        // Return final response
        return new Response(
            JSON.stringify( availabilityInfo ),
            { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 200 }
        );
    } catch (error) {
        return new Response(
            JSON.stringify({ error: (error as Error).message }),
            { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 400 }
        );
    }
});
