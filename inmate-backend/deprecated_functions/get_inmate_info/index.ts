import 'https://esm.sh/@supabase/functions-js/src/edge-runtime.d.ts';
import { createClient } from 'npm:@supabase/supabase-js@2.39.3';

const supabase = createClient(Deno.env.get('SUPABASE_URL')!, Deno.env.get('SUPABASE_ANON_KEY')!);

const corsHeaders = {
  'Access-Control-Allow-Origin': '*', // Replace * with your frontend URL for tighter security
  'Access-Control-Allow-Methods': 'POST, GET, OPTIONS', // Specify allowed methods
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Max-Age': '86400', // Cache preflight response for 24 hours,


};

Deno.serve(async (req) => {
  try {
    const headers = {
      'Access-Control-Allow-Origin': '*', // Replace with your frontend URL
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
      'Access-Control-Max-Age': '86400',
      // ... other headers
    };
    if (req.method === 'OPTIONS') {
      return new Response(null, { headers });
    }

    const url = new URL(req.url);
    const inmateId = url.searchParams.get('inmateId');

    if (!inmateId) {
      throw new Error('inmateId is required');
    }

    // Step 1: Fetch inmate details including the is_available field
    const { data: inmateData, error: inmateError } = await supabase
      .from('inmates')
      .select('*')
      .eq('id', inmateId)
      .single();

    if (inmateError) {
      throw new Error(`Error fetching Inmate details: ${inmateError.message}`);
    }

    // Step 2: Fetch availability settings for the given inmate ID
    const { data: settingsData, error: settingsError } = await supabase
      .from('inmate_availability_settings')
      .select('id, day')
      .eq('inmate_id', inmateId);

    if (settingsError) {
      throw new Error(`Error fetching Inmate_Availability_Settings: ${settingsError.message}`);
    }

    if (!settingsData || settingsData.length === 0) {
      return new Response(
        JSON.stringify({
          inmateId,
          inmate_data: inmateData,
          availability: [],
          message: 'No availability settings found for the given inmateId'
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200,
        }
      );
    }

    // Step 3: Extract setting IDs
    const settingIds = settingsData.map(({ id }) => id);

    // Step 4: Fetch availability times for the fetched setting IDs
    const { data: timesData, error: timesError } = await supabase
      .from('availability_type')
      .select('setting_id, available_from, available_to')
      .in('setting_id', settingIds);

    if (timesError) {
      throw new Error(`Error fetching AvaliabilityTimes: ${timesError.message}`);
    }

    // Step 5: Combine settings and times into a structured response
    const availabilityInfo = settingsData.map((setting) => ({
      day: setting.day,
      settingId: setting.id,
      times: timesData
        .filter((time) => time.setting_id === setting.id)
        .map(({ available_from, available_to }) => ({
          from: available_from,
          to: available_to
        })),
    }));

    // Step 6: Return the final response with inmate details
    return new Response(
      JSON.stringify({
        inmate_data: inmateData,
        availability: availabilityInfo,
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    );
  } catch (error) {
    return new Response(
      JSON.stringify({ error: (error as Error).message }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      }
    );
  }
});
