// Follow this setup guide to integrate the Deno language server with your editor:
// https://deno.land/manual/getting_started/setup_your_environment
// This enables autocomplete, go to definition, etc.

// Setup type definitions for built-in Supabase Runtime APIs
import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import {createClient} from "npm:@supabase/supabase-js@2.39.3"
// import { PrismaClient } from '@prisma/client';
// //
// // // Initialize Prisma client
// const prisma = new PrismaClient();
const headers = new Headers();
headers.set("Access-Control-Allow-Origin", "*"); // Allow all origins
headers.set("Access-Control-Allow-Methods", "GET, POST, OPTIONS"); // Allow methods
headers.set("Access-Control-Allow-Headers", "Content-Type, Authorization"); // Allow headers



const supabase = createClient(
    Deno.env.get("SUPABASE_URL")!,
    Deno.env.get("SUPABASE_ANON_KEY")!,
    // {
    //   db: {
    //     schema: 'auth'
    //   }
    // },
)

const supabase2 = createClient(
    Deno.env.get("SUPABASE_URL")!,
    Deno.env.get("SUPABASE_ANON_KEY")!,
  {
    db: {
      schema: 'auth'
    }
  },
)


Deno.serve(async (req) => {
  const headers = new Headers();
  headers.set("Access-Control-Allow-Origin", "*"); // Allow all origins
  headers.set("Access-Control-Allow-Credentials","true")
  headers.set("Access-Control-Allow-Methods", "GET, POST,HEAD,PUT,PATCH,DELETE,TRACE,CONNECT, OPTIONS"); // Allow methods
  headers.set("Access-Control-Allow-Headers", "Authorization, Foo, Bar, Accept, Accept-Language, Content-Language"); // Allow headers

  if (req.method === "OPTIONS") {
    return new Response(null, { headers });
  }

  const { adminData, error } = await supabase
    .from('admin')
    .select('*');

  if (error) {
    return new Response(JSON.stringify({ error }), {headers:headers, status: 400 });
  }


  // =============================================================================
    const { data: usersData, error: usersError } = await supabase2
    .schema('auth')
    .from('users') // 'users' is from 'auth' schema
    .select('*');


    console.log("aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa" , usersData)
    if (usersError) {
      console.error('Error fetching users data:', usersError);
    }

    // Merge the data manually on the client-side
    // const mergedData = adminData.map(admin => {
    //   const user = usersData.find(user => user.id === admin.id);
    //   return {
    //     ...admin,
    //    email: user ? user.email : null // Attach the email field
  //     //   };
  //     // });
  //   // ======== =====================================================================




  return new Response(JSON.stringify(usersData), { headers:headers,status: 200 });
});

/* To invoke locally:

  1. Run `supabase start` (see: https://supabase.com/docs/reference/cli/supabase-start)
  2. Make an HTTP request:

  curl -i --location --request POST 'http://127.0.0.1:54321/functions/v1/get_inmates' \
    --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0' \
    --header 'Content-Type: application/json' \
    --data '{"name":"Functions"}'

*/
