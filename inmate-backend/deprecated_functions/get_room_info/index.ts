// Follow this setup guide to integrate the Deno language server with your editor:
// https://deno.land/manual/getting_started/setup_your_environment
// This enables autocomplete, go to definition, etc.

// Setup type definitions for built-in Supabase Runtime APIs
import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { createClient } from 'npm:@supabase/supabase-js@2.39.3';

const supabase = createClient(Deno.env.get('SUPABASE_URL')!, Deno.env.get('SUPABASE_ANON_KEY')!);

const corsHeaders = {
  'Access-Control-Allow-Origin': '*', // Replace * with your frontend URL for tighter security
  'Access-Control-Allow-Methods': 'POST, GET, OPTIONS', // Specify allowed methods
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Max-Age': '86400', // Cache preflight response for 24 hours,


};
Deno.serve(async (req) => {
  try {
    const headers = {
      'Access-Control-Allow-Origin': '*', // Replace with your frontend URL
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
      'Access-Control-Max-Age': '86400',
      // ... other headers
    };
    if (req.method === 'OPTIONS') {
      return new Response(null, { headers });
    }

    const url = new URL(req.url);
    const roomId = url.searchParams.get('roomId');

    if (!roomId) {
      throw new Error('roomId is required');
    }

    // Step 1: Fetch room details including the is_available field
    const { data: roomData, error: roomError } = await supabase
      .from('rooms')
      .select('*')
      .eq('id', roomId)
      .single();

    if (roomError) {
      throw new Error(`Error fetching Section details: ${roomError.message}`);
    }

    // Step 2: Fetch availability settings for the given room ID
    const { data: settingsData, error: settingsError } = await supabase
      .from('room_availability_settings')
      .select('id, day')
      .eq('room_id', roomId);

    if (settingsError) {
      throw new Error(`Error fetching section_availability_settings: ${settingsError.message}`);
    }

    if (!settingsData || settingsData.length === 0) {
      return new Response(
        JSON.stringify({
          roomId,
          roomCode: roomData.name,
          roomAvailability: roomData.active,
          isBuildingDefault: roomData.is_building_default,
          availability: []
          ,message: 'No availability settings found for the given roomId' }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200,
        }
      );
    }

    // Step 3: Extract setting IDs
    const settingIds = settingsData.map(({ id }) => id);

    // Step 4: Fetch availability times for the fetched setting IDs
    const { data: timesData, error: timesError } = await supabase
      .from('availability_type')
      .select('setting_id, available_from, available_to')
      .in('setting_id', settingIds);

    if (timesError) {
      throw new Error(`Error fetching AvaliabilityTimes: ${timesError.message}`);
    }

    // Step 5: Combine settings and times into a structured response
    const availabilityInfo = settingsData.map((setting) => ({
      day: setting.day,
      settingId: setting.id,
      times: timesData
        .filter((time) => time.setting_id === setting.id)
        .map(({ available_from, available_to }) => ({
          from: available_from,
          to: available_to
        })),
    }));

    // Step 6: Return the final response with room details
    return new Response(
      JSON.stringify({
        roomId,
        roomCode: roomData.name,
        roomAvailability: roomData.active,
        isBuildingDefault: roomData.is_building_default,
        availability: availabilityInfo,
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    );
  } catch (error) {
    return new Response(
      JSON.stringify({ error: (error as Error).message }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      }
    );
  }
})

/* To invoke locally:

  1. Run `supabase start` (see: https://supabase.com/docs/reference/cli/supabase-start)
  2. Make an HTTP request:

  curl -i --location --request POST 'http://127.0.0.1:54321/functions/v1/get_room_info' \
    --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0' \
    --header 'Content-Type: application/json' \
    --data '{"name":"Functions"}'

*/
