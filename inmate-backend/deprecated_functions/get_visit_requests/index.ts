// Follow this setup guide to integrate the Deno language server with your editor:
// https://deno.land/manual/getting_started/setup_your_environment
// This enables autocomplete, go to definition, etc.

// Setup type definitions for built-in Supabase Runtime APIs

// import { error } from "console";
import "https://esm.sh/@supabase/functions-js/src/edge-runtime.d.ts"
import {createClient} from "npm:@supabase/supabase-js@2.39.3"


const supabase = createClient(
    Deno.env.get("SUPABASE_URL")!,
    Deno.env.get("SUPABASE_ANON_KEY")!,
)

export const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

Deno.serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }
    const { page = 1, limit = 10 } = await req.json();
    const offset = (page - 1) * limit;
  // check if room exists with same name
  // ===================================
  // ===================================

  const { data: visitRequests, error: selectError, count } = await supabase
  .from('visit_requests')
  .select(`
    *
  `, { count: 'exact' }) // Include total count for pagination
  .range(offset, offset + limit - 1) // Apply limit and offset for pagination

// Error handling
if (selectError) {
  console.error("Error fetching visit requests:", selectError)
  return new Response(
    JSON.stringify({ error: "Error fetching visit requests", details: selectError }),
    { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  )
}



const { data: test_inmateVisitor, error: inmateVisitorError } = await supabase
.from('Inmate_Visitor')
.select('relation, relation_degree')
.eq('inmate_id', "8f8caeb0-31ff-4f21-830e-7a271a70c79f")
.eq('visitor_id', "98dbff32-7497-437f-81a7-9506b806a2d4")
.single(); // Fetch a single result, assuming a one-to-one relation between inmate and visitor


console.log("cvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv",test_inmateVisitor , inmateVisitorError)



// ========================================================================
console.log("ccccccccccccccccccccccccccccccc", visitRequests[0])
const enrichedVisitRequests = await Promise.all(
  visitRequests.map(async (visitRequest) => {
    const { inmate_id } = visitRequest.inmate_id;
    const visitor_id = visitRequest.visitor_id;
    // Query Inmate_Visitor for the corresponding inmate_id and visitor_id
    const { data: inmateVisitor, error: inmateVisitorError } = await supabase
      .from('inmate_visitors')
      .select('relation, relation_degree')
      .eq('inmate_id', inmate_id)
      .eq('visitor_id', visitor_id)
      .single(); // Fetch a single result, assuming a one-to-one relation between inmate and visitor


      console.log("aaaaaaaaaaaaaaaaxxxxxxxxxxxxxxxxx" , inmateVisitor)
    // Handle any errors in the Inmate_Visitor query
    if (inmateVisitorError) {
      console.error(`Error fetching inmate_visitor for visit_request ${visitRequest.id}:`, inmateVisitorError);
      return {
        ...visitRequest,
        inmate_visitor_error: "Error fetching inmate visitor data",
      };
    }

    // Return the visit request enriched with inmate visitor details
    return {
      ...visitRequest,
      inmate_visitor: inmateVisitor,
    };
  })
);



// ========================================================================


























// Return the paginated response
return new Response(
  JSON.stringify({
    data: enrichedVisitRequests,
    total: count,
    limit,
    offset,
  }),
  {
    status: 200,
    headers: { ...corsHeaders, 'Content-Type': 'application/json' },
  }
)
})
