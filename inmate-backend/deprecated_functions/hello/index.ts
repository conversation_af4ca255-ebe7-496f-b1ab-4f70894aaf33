// Follow this setup guide to integrate the Deno language server with your editor:
// https://deno.land/manual/getting_started/setup_your_environment
// This enables autocomplete, go to definition, etc.

// Setup type definitions for built-in Supabase Runtime APIs
import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import {createClient} from "npm:@supabase/supabase-js@2.39.3"
// import { PrismaClient } from '@prisma/client';
// //
// // // Initialize Prisma client
// const prisma = new PrismaClient();
const supabase = createClient(
    Deno.env.get("SUPABASE_URL")!,
    Deno.env.get("SUPABASE_ANON_KEY")!,
)
console.log("Hello from Functions!")

Deno.serve(async (req) => {
  // Fetch all records from the "Inmate" table
  const { data: inmates, error } = await supabase.from("Inmate").select("*");

  // If there is an error, log it
  if (error) console.log(error);

  // Prepare the response data
  const data = {
    message: "List of inmates",
    inmates: inmates || [],
  }

  // Return the response as JSON
  return new Response(
      JSON.stringify(data),
      { headers: { "Content-Type": "application/json" } },
  );
});


/* To invoke locally:

  1. Run `supabase start` (see: https://supabase.com/docs/reference/cli/supabase-start)
  2. Make an HTTP request:

  curl -i --location --request POST 'http://127.0.0.1:54321/functions/v1/hello' \
    --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0' \
    --header 'Content-Type: application/json' \
    --data '{"name":"Functions"}'

*/
