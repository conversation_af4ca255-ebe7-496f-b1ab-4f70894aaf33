// Setup type definitions for built-in Supabase Runtime APIs
import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import {createClient} from "npm:@supabase/supabase-js@2.39.3";

// Initialize Supabase client
const supabase = createClient(
    Deno.env.get("SUPABASE_URL")!,
    Deno.env.get("SUPABASE_ANON_KEY")!
);

// Define CORS headers
const headers = new Headers();
headers.set("Access-Control-Allow-Origin", "*"); // Allow all origins
headers.set("Access-Control-Allow-Methods", "GET, POST, OPTIONS"); // Allow methods
headers.set("Access-Control-Allow-Headers", "Content-Type, Authorization"); // Allow headers
headers.set("Access-Control-Allow-Credentials", "true");

// Define the interfaces for the incoming event
interface RecordingInfoEvent {
  record_id: string;
  room_id: string;
  room_sid: string;
  file_path: string;
  file_size: number;
  duration: number;
  creation_time: number;
  room_creation_time: number;
}

interface Room {
  room_info: {
    room_id: string; // The room_id to match with the meeting ID
  };
}

interface CommonNotifyEvent {
  event: 'recording_proceeded';
  room: Room;
  recording_info: RecordingInfoEvent;
}

// Helper function to extract time portion from a Unix timestamp
function extractTimeFromUnixTimestamp(unixTimestamp: number): string {
  const date = new Date(unixTimestamp * 1000); // Convert to JavaScript Date object
  return date.toTimeString().split(' ')[0]; // Extract time in HH:MM:SS format
}

// Serve function for webhook
Deno.serve(async (req) => {
  if (req.method === "OPTIONS") {
    // Handle CORS preflight requests
    return new Response(null, { headers });
  }

  if (req.method === "POST") {
    try {
      // Parse the incoming request body as JSON
      const body = await req.json() as CommonNotifyEvent;

      // Validate the event
      if (body.event !== 'recording_proceeded') {
        return new Response(JSON.stringify({ error: "Invalid event type" }), { headers, status: 400 });
      }

      const { room_id, record_id, file_path, file_size, duration, creation_time } = body.recording_info;

      // Ensure the room_id is present
      if (!room_id) {
        return new Response(JSON.stringify({ error: "Missing room_id" }), { headers, status: 400 });
      }

      // Fetch the meeting by room_id
      const { data: meeting, error: meetingError } = await supabase
        .from('meetings')
        .select('id')
        .eq('id', room_id)
        .single();

      // If no meeting is found, return an error
      if (meetingError || !meeting) {
        return new Response(JSON.stringify({ error: "Meeting not found" }), { headers, status: 404 });
      }

      // Extract time portion (HH:MM:SS) from the creation_time
      const timePortion = extractTimeFromUnixTimestamp(creation_time);

      // Insert a new meeting record with the time portion and duration
      const { data: newRecord, error: recordError } = await supabase
        .from('meeting_records')
        .insert({
          meeting_id: room_id,          
          session_id: record_id,        
          recording_url: file_path,     
          record_size: file_size,       
          duration: duration,           // Insert the duration value from input
          created_at: timePortion,      // Use only the time portion (HH:MM:SS)
          deleted_at: null,             
        })
        .single();

      // Check for errors during the insert operation
      if (recordError) {
        return new Response(JSON.stringify({ error: recordError.message }), { headers, status: 400 });
      }

      // Return a success response
      return new Response(JSON.stringify({ message: "Recording data saved successfully", data: newRecord }), { headers, status: 200 });

    } catch (err) {
      // Handle unexpected errors
      return new Response(JSON.stringify({ error: "Internal Server Error" }), { headers, status: 500 });
    }
  }

  // Handle any other methods (e.g., GET, etc.)
  return new Response("Method not allowed", { status: 405, headers });
});

/* To invoke locally:

  1. Run `supabase start` (see: https://supabase.com/docs/reference/cli/supabase-start)
  2. Make an HTTP request:

  curl -i --location --request POST 'http://127.0.0.1:54321/functions/v1/start_recording_webhook' \
    --header 'Authorization: Bearer <your_auth_token>' \
    --header 'Content-Type: application/json' \
    --data '{
      "event": "start_recording",
      "room": { "room_info": { "room_id": "ba418cb8-cb19-4cb9-8ddb-52f529a82cc8" } },
      "recording_info": {
        "record_id": "some-record-id",
        "room_id": "ba418cb8-cb19-4cb9-8ddb-52f529a82cc8",
        "room_sid": "some-room-sid",
        "file_path": "/path/to/recording",
        "file_size": 123456,
        "duration": 3,
        "creation_time": 1634161234,
        "room_creation_time": 1634160000
      }
    }'

*/

