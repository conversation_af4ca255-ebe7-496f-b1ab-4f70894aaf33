import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import {createClient} from "npm:@supabase/supabase-js@2.39.3";

const supabase = createClient(
    Deno.env.get("SUPABASE_URL")!,
    Deno.env.get("SUPABASE_ANON_KEY")!
);



export const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

// Define CORS headers
const headers = new Headers();
headers.set("Access-Control-Allow-Origin", "*"); // Allow all origins
headers.set("Access-Control-Allow-Methods", "GET, POST, OPTIONS"); // Allow methods
headers.set("Access-Control-Allow-Headers", "Content-Type, Authorization"); // Allow headers
headers.set("Access-Control-Allow-Credentials", "true");


interface RecordingInfoEvent {
  record_id: string;
  room_id: string;
  room_sid: string;
  file_path: string;
  file_size: number;
  duration: number;
  creation_time: number;
  room_creation_time: number;
}

interface Room {
  room_info: {
    room_id: string; // The room_id to match with the meeting ID
  };
}

interface CommonNotifyEvent {
  event: 'recording_proceeded';
  room: Room;
  recording_info: RecordingInfoEvent;
}










function extractTimeFromUnixTimestamp(unixTimestamp: number): string {
  const date = new Date(unixTimestamp * 1000); // Convert to JavaScript Date object
  return date.toTimeString().split(' ')[0]; // Extract time in HH:MM:SS format
}









Deno.serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  const { event, room,id,created_at,recording_info } = await req.json();

  const roomId = room.room_id;
      




  if(event === "room_started"){
    const { data, error } = await supabase
    .from('meetings') // Your Meeting table
    .update({
      status: 'started',               // Set status to STARTED
      started_at: new Date().toISOString(),  // Set started_at to the current time
    })
    .eq('id', roomId); // Update where the id matches room_id

  // Check for any errors during the update
  if (error) {
    return new Response(JSON.stringify({ error: error.message }), { headers, status: 400 });

  }

  
  const { data: VisitRequestdata, error:VisitRequestError } = await supabase
  .from('visit_requests') // Your Meeting table
  .update({
    meeting_status: 'started',               // Set status to STARTED
  })
  .eq('meeting_id', roomId);

    if (VisitRequestError) {
    return new Response(JSON.stringify({ error: VisitRequestError.message }), { headers, status: 400 });

  }

  }else if (event === "room_finished"){
    const { data, error } = await supabase
    .from('meetings') // Your Meeting table
    .update({
      status: 'ended',               // Set status to STARTED
      ended_at: new Date().toISOString(),  // Set started_at to the current time
    })
    .eq('id', roomId); // Update where the id matches room_id


    const { data: VisitRequestdata, error:VisitRequestError } = await supabase
    .from('visit_requests') // Your Meeting table
    .update({
      meeting_status: 'ended',               // Set status to STARTED
    })
    .eq('meeting_id', roomId);

  }else if (event === "recording_proceeded"){

    // const body = await req.json() as CommonNotifyEvent;

    const {record_id, file_path, file_size } = recording_info;


      // Ensure the room_id is present
      if (!roomId) {
        return new Response(JSON.stringify({ error: "Missing room_id" }), { headers, status: 400 });
      }

      // Fetch the meeting by room_id
      // const { data: meeting, error: meetingError } = await supabase
      //   .from('Meeting')
      //   .select('id')
      //   .eq('id', roomId)
      //   .single();

      // // If no meeting is found, return an error
      // if (meetingError || !meeting) {
      //   return new Response(JSON.stringify({ error: "Meeting not found" }), { headers, status: 404 });
      // }

      // Extract time portion (HH:MM:SS) from the creation_time
      // const timePortion = extractTimeFromUnixTimestamp(creation_time);

      // Insert a new meeting record with the time portion and duration
      const { data: newRecord, error: recordError } = await supabase
        .from('meeting_records')
        .insert({
          meeting_id: roomId,          
          session_id: record_id,        
          recording_url: file_path,     
          record_size: Math.floor(file_size),       
          duration: null,           // Insert the duration value from input
          created_at: null,      // Use only the time portion (HH:MM:SS)
          deleted_at: null,             
        })
        .single();




      // Check for errors during the insert operation
      if (recordError) {
        return new Response(JSON.stringify({ error: recordError.message }), { headers, status: 400 });
      }

      // Return a success response
      return new Response(JSON.stringify({ message: "Recording data saved successfully", data: newRecord }), { headers, status: 200 });

    }
  
  return new Response(
    JSON.stringify({ message: 'meeting updated' }),
    { headers: { ...corsHeaders,"Content-Type": "application/json" } },
  );
});
