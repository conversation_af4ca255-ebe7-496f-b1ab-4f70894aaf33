// Follow this setup guide to integrate the Deno language server with your editor:
// https://deno.land/manual/getting_started/setup_your_environment

import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import {createClient} from "npm:@supabase/supabase-js@2.39.3"

// Initialize Supabase client with environment variables
const supabase = createClient(
    Deno.env.get("SUPABASE_URL")!,
    Deno.env.get("SUPABASE_SERVICE_ROLE_KEY")!
);

Deno.serve(async (req: Request) => {
  try {
    const now = new Date();
    const oneHourLater = new Date(now.getTime() + 60 * 60 * 1000);

    // Fetch visits where the status is 'APPROVED' and check if the meeting is within one hour
    const { data: visitRequests, error } = await supabase
        .from('visit_requests')
        .select('*')
        .eq('status', 'APPROVED')
        .lte('datetime', oneHourLater.toISOString())
        .gt('datetime', now.toISOString());
    console.log("time log",visitRequests)
    // Handle errors during data fetch
    if (error) {
      return new Response(`Error fetching visit requests: ${error.message}`, { status: 500 });
    }

    // If no matching visits, return response
    if (!visitRequests || visitRequests.length === 0) {
      return new Response('No visits to update.', { status: 200 });
    }

    // Loop over fetched visit requests and update their status to 'STARTS_SOON'
    const { error: updateError } = await supabase
        .from('visit_requests')
        .update({ status: 'starts_soon' })
        .in('id', visitRequests.map((visit) => visit.id));  // Update based on IDs

    // Handle any errors during the update process
    if (updateError) {
      return new Response(`Error updating visit statuses: ${updateError.message}`, { status: 500 });
    }

    return new Response(`Updated ${visitRequests.length} visit(s) to 'STARTS_SOON'`, { status: 200 });

  } catch (err) {
    return new Response(`Server Error: ${err.message}`, { status: 500 });
  }
});
