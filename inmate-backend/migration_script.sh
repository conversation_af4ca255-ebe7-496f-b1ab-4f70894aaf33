#!/bin/bash
# Usage: ./migration_script.sh <migration_name>
# Example: ./migration_script.sh create_table_users
# This script will create a migration file with the given name, run the migration, and then restore the database to its previous state
# This script assumes that the database is running on port 54322 and the database name is 'postgres'
# The database will be restored to its previous state after the migration is run

# Variables
DB_URL="postgres://postgres:postgres@127.0.0.1:54322/postgres"
DUMP_FILE="dump.sql"

# Check if migration name is provided
if [ -z "$1" ]; then
  echo "Error: Migration name is required."
  echo "Usage: $0 <migration_name>"
  exit 1
fi

# Dump the current database state
echo "Dumping the current database state..."
pg_dump "$DB_URL" > "$DUMP_FILE" || {
  echo "Error: Failed to dump the database."
  exit 1
}

# Resetting database
echo "Resetting database..."
npx prisma migrate reset || {
  echo "Error: Failed to reset database."
  exit 1
}

# Create and run the migration
echo "Creating and running the migration..."
npx prisma migrate dev --name "$1" || {
  echo "Error: Failed to create or run the migration."
  exit 1
}

# Restore the database to its previous state
echo "Restoring the database to its previous state..."
psql "$DB_URL" < "$DUMP_FILE" || {
  echo "Error: Failed to restore the database."
  exit 1
}

echo "Migration script completed successfully."
