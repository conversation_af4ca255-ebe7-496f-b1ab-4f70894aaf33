-- CreateEnum
CREATE TYPE "VISITREQUESTSTATUS" AS ENUM ('ACCEPTED', 'PENDING', 'STARTS_SOON', 'DENIED', 'CANCELLED', 'MISSED', 'COMPLETED');

-- Create<PERSON>num
CREATE TYPE "RoleName" AS ENUM ('SUPERADMIN', 'ADMIN');

-- CreateEnum
CREATE TYPE "InmateVitorRelationDegree" AS ENUM ('FIRST', 'SECOND', 'OTHERS');

-- CreateEnum
CREATE TYPE "InmateVitorRelation" AS ENUM ('BROTHER', 'SISTER', 'HUSBUND', 'LAWYER', 'DAUGHTER');

-- CreateEnum
CREATE TYPE "section" AS ENUM ('BUILDING1', 'BUILDING2', 'BUILDING3');

-- CreateEnum
CREATE TYPE "Days" AS ENUM ('SUNDAY', 'MONDAY', 'TUESDAY', 'WENDESDAY', 'THURSDAY', 'FRIDAY', 'SATURDAY');

-- CreateTable
CREATE TABLE "Inmate" (
    "id" TEXT NOT NULL,
    "full_name" TEXT NOT NULL,
    "national_id" TEXT NOT NULL,
    "age" INTEGER NOT NULL,
    "number_of_visits" INTEGER NOT NULL,
    "section" "section" NOT NULL,
    "unavailable" BOOLEAN NOT NULL,

    CONSTRAINT "Inmate_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Visitor" (
    "id" TEXT NOT NULL,
    "full_name" TEXT NOT NULL,
    "national_id" TEXT NOT NULL,
    "age" INTEGER NOT NULL,

    CONSTRAINT "Visitor_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Notification" (
    "id" TEXT NOT NULL,
    "datetime" TIMESTAMP(3) NOT NULL,
    "visitor_id" TEXT NOT NULL,
    "visit_id" TEXT NOT NULL,
    "recipient_id" TEXT NOT NULL,
    "message" TEXT NOT NULL,

    CONSTRAINT "Notification_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Visit_Request" (
    "id" TEXT NOT NULL,
    "visitor_id" TEXT NOT NULL,
    "approved_by" TEXT NOT NULL,
    "inmate_id" TEXT NOT NULL,
    "room_id" TEXT NOT NULL,
    "datetime" TIMESTAMP(3) NOT NULL,
    "timezone" TEXT NOT NULL,
    "status" "VISITREQUESTSTATUS" NOT NULL,
    "visit_number" TEXT NOT NULL,
    "duration" INTEGER NOT NULL,
    "user_full_name" TEXT NOT NULL,
    "user_national_id" TEXT NOT NULL,
    "relation" TEXT NOT NULL,
    "user_document" TEXT NOT NULL,

    CONSTRAINT "Visit_Request_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "User" (
    "id" TEXT NOT NULL,
    "role_id" TEXT NOT NULL,
    "full_name" TEXT NOT NULL,

    CONSTRAINT "User_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Role" (
    "id" TEXT NOT NULL,
    "name" "RoleName" NOT NULL,

    CONSTRAINT "Role_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Permission" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,

    CONSTRAINT "Permission_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Role_Permission" (
    "id" TEXT NOT NULL,
    "role_id" TEXT NOT NULL,
    "permission_id" TEXT NOT NULL,

    CONSTRAINT "Role_Permission_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "User_Permission" (
    "id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "permission_id" TEXT NOT NULL,

    CONSTRAINT "User_Permission_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Inmate_Visitor" (
    "id" TEXT NOT NULL,
    "inmate_id" TEXT NOT NULL,
    "visitor_id" TEXT NOT NULL,
    "is_white_list" BOOLEAN NOT NULL,
    "relation_degree" "InmateVitorRelationDegree" NOT NULL,
    "relation" "InmateVitorRelation" NOT NULL,

    CONSTRAINT "Inmate_Visitor_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Room" (
    "id" TEXT NOT NULL,
    "section" "section" NOT NULL,
    "name" TEXT NOT NULL,
    "active" BOOLEAN NOT NULL,

    CONSTRAINT "Room_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "RoomAvaliabilitySettings" (
    "id" TEXT NOT NULL,
    "day" "Days" NOT NULL,
    "room_id" TEXT NOT NULL,
    "available_from" TIME NOT NULL,
    "available_to" TIME NOT NULL,

    CONSTRAINT "RoomAvaliabilitySettings_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "InmateAvaliabilitySettings" (
    "id" TEXT NOT NULL,
    "day" "Days" NOT NULL,
    "inmate_id" TEXT NOT NULL,

    CONSTRAINT "InmateAvaliabilitySettings_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "Inmate_national_id_key" ON "Inmate"("national_id");

-- CreateIndex
CREATE UNIQUE INDEX "Visitor_national_id_key" ON "Visitor"("national_id");

-- AddForeignKey
ALTER TABLE "Notification" ADD CONSTRAINT "Notification_visitor_id_fkey" FOREIGN KEY ("visitor_id") REFERENCES "Visitor"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Notification" ADD CONSTRAINT "Notification_visit_id_fkey" FOREIGN KEY ("visit_id") REFERENCES "Visit_Request"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Notification" ADD CONSTRAINT "Notification_recipient_id_fkey" FOREIGN KEY ("recipient_id") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Visit_Request" ADD CONSTRAINT "Visit_Request_inmate_id_fkey" FOREIGN KEY ("inmate_id") REFERENCES "Inmate"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Visit_Request" ADD CONSTRAINT "Visit_Request_room_id_fkey" FOREIGN KEY ("room_id") REFERENCES "Room"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Visit_Request" ADD CONSTRAINT "Visit_Request_visitor_id_fkey" FOREIGN KEY ("visitor_id") REFERENCES "Visitor"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Visit_Request" ADD CONSTRAINT "Visit_Request_approved_by_fkey" FOREIGN KEY ("approved_by") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "User" ADD CONSTRAINT "User_role_id_fkey" FOREIGN KEY ("role_id") REFERENCES "Role"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Role_Permission" ADD CONSTRAINT "Role_Permission_role_id_fkey" FOREIGN KEY ("role_id") REFERENCES "Role"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Role_Permission" ADD CONSTRAINT "Role_Permission_permission_id_fkey" FOREIGN KEY ("permission_id") REFERENCES "Permission"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "User_Permission" ADD CONSTRAINT "User_Permission_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "User_Permission" ADD CONSTRAINT "User_Permission_permission_id_fkey" FOREIGN KEY ("permission_id") REFERENCES "Permission"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Inmate_Visitor" ADD CONSTRAINT "Inmate_Visitor_inmate_id_fkey" FOREIGN KEY ("inmate_id") REFERENCES "Inmate"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Inmate_Visitor" ADD CONSTRAINT "Inmate_Visitor_visitor_id_fkey" FOREIGN KEY ("visitor_id") REFERENCES "Visitor"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "RoomAvaliabilitySettings" ADD CONSTRAINT "RoomAvaliabilitySettings_room_id_fkey" FOREIGN KEY ("room_id") REFERENCES "Room"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "InmateAvaliabilitySettings" ADD CONSTRAINT "InmateAvaliabilitySettings_inmate_id_fkey" FOREIGN KEY ("inmate_id") REFERENCES "Inmate"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
