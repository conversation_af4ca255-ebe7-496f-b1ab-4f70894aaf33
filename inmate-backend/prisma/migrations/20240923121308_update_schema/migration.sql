/*
  Warnings:

  - You are about to drop the column `available_from` on the `RoomAvaliabilitySettings` table. All the data in the column will be lost.
  - You are about to drop the column `available_to` on the `RoomAvaliabilitySettings` table. All the data in the column will be lost.
  - Added the required column `available_from` to the `InmateAvaliabilitySettings` table without a default value. This is not possible if the table is not empty.
  - Added the required column `available_to` to the `InmateAvaliabilitySettings` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "Inmate" ADD COLUMN     "isDeleted" TIMESTAMP(3);

-- AlterTable
ALTER TABLE "InmateAvaliabilitySettings" ADD COLUMN     "available_from" TIME NOT NULL,
ADD COLUMN     "available_to" TIME NOT NULL;

-- AlterTable
ALTER TABLE "Room" ADD COLUMN     "isDeleted" TIMESTAMP(3);

-- AlterTable
ALTER TABLE "RoomAvaliabilitySettings" DROP COLUMN "available_from",
DROP COLUMN "available_to",
ADD COLUMN     "isAvailable" BOOLEAN NOT NULL DEFAULT true;

-- AlterTable
ALTER TABLE "User" ADD COLUMN     "isDeleted" TIMESTAMP(3);

-- AlterTable
ALTER TABLE "Visit_Request" ADD COLUMN     "isDeleted" TIMESTAMP(3);

-- AlterTable
ALTER TABLE "Visitor" ADD COLUMN     "isDeleted" TIMESTAMP(3);
