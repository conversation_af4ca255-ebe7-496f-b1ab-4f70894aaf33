-- This is an empty migration.

-- Your table migrations and other SQL statements might already be here

-- Add the function to handle notification creation
CREATE OR REPLACE FUNCTION notify_on_visit_request_change()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.status IS DISTINCT FROM OLD.status THEN
        INSERT INTO "Notification" ( datetime, visitor_id, visit_id, message,recipient_id)
        VALUES ( NOW(), NEW.visitor_id, NEW.id, 'Visit request status changed to ' || NEW.status,NEW.approved_by);
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger on the Visit_Request table
CREATE OR REPLACE TRIGGER visit_request_status_change
AFTER UPDATE ON "Visit_Request"
FOR EACH ROW
EXECUTE FUNCTION notify_on_visit_request_change();