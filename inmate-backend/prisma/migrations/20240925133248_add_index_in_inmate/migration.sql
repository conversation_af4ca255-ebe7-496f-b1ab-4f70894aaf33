/*
  Warnings:

  - You are about to drop the column `user_full_name` on the `Visit_Request` table. All the data in the column will be lost.
  - You are about to drop the column `user_national_id` on the `Visit_Request` table. All the data in the column will be lost.

*/
-- AlterEnum
ALTER TYPE "InmateVitorRelationDegree" ADD VALUE 'THIRD';

-- AlterTable
ALTER TABLE "Visit_Request" DROP COLUMN "user_full_name",
DROP COLUMN "user_national_id";

-- CreateTable
CREATE TABLE "Inmate_Visitor_Documents" (
    "id" TEXT NOT NULL DEFAULT gen_random_uuid(),
    "inmate_visitor_id" TEXT NOT NULL,
    "document_url" TEXT NOT NULL,

    CONSTRAINT "Inmate_Visitor_Documents_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "Inmate_national_id_idx" ON "Inmate" USING HASH ("national_id");

-- AddForeignKey
ALTER TABLE "Inmate_Visitor_Documents" ADD CONSTRAINT "Inmate_Visitor_Documents_inmate_visitor_id_fkey" FOREIGN KEY ("inmate_visitor_id") REFERENCES "Inmate_Visitor"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
