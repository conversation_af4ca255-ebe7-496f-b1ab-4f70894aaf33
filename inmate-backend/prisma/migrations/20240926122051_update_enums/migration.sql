/*
  Warnings:

  - The values [ACCEPTED,CANCELLED] on the enum `VISITREQUESTSTATUS` will be removed. If these variants are still used in the database, this will fail.

*/
-- AlterEnum
-- This migration adds more than one value to an enum.
-- With PostgreSQL versions 11 and earlier, this is not possible
-- in a single migration. This can be worked around by creating
-- multiple migrations, each migration adding only one value to
-- the enum.


ALTER TYPE "InmateVitorRelation" ADD VALUE 'MOTHER';
ALTER TYPE "InmateVitorRelation" ADD VALUE 'FATHER';
ALTER TYPE "InmateVitorRelation" ADD VALUE 'SON';
ALTER TYPE "InmateVitorRelation" ADD VALUE 'GRANDFATHER';
ALTER TYPE "InmateVitorRelation" ADD VALUE 'GRANDMOTHER';
ALTER TYPE "InmateVitorRelation" ADD VALUE 'GRANDSON';
ALTER TYPE "InmateVitorRelation" ADD VALUE 'GRANDDAUGHTER';
ALTER TYPE "InmateVitorRelation" ADD VALUE 'UNCEL';
ALTER TYPE "InmateVitorRelation" ADD VALUE 'AUNT';
ALTER TYPE "InmateVitorRelation" ADD VALUE 'NEPHEW';
ALTER TYPE "InmateVitorRelation" ADD VALUE 'NIECE';
ALTER TYPE "InmateVitorRelation" ADD VALUE 'COUSIN';
ALTER TYPE "InmateVitorRelation" ADD VALUE 'GREAT_UNCLE';
ALTER TYPE "InmateVitorRelation" ADD VALUE 'GREAT_AUNT';
ALTER TYPE "InmateVitorRelation" ADD VALUE 'GREAT_NEPHEW';
ALTER TYPE "InmateVitorRelation" ADD VALUE 'GREAT_NIECE';
ALTER TYPE "InmateVitorRelation" ADD VALUE 'LEGAL_REPRESENTATIVE';
ALTER TYPE "InmateVitorRelation" ADD VALUE 'GOVERNMENT_OFFICIAL';
ALTER TYPE "InmateVitorRelation" ADD VALUE 'SOCIAL_WORKER';

-- AlterEnum
BEGIN;
CREATE TYPE "VISITREQUESTSTATUS_new" AS ENUM ('APPROVED', 'PENDING', 'STARTS_SOON', 'DENIED', 'CANCELED', 'MISSED', 'COMPLETED');
ALTER TABLE "Visit_Request" ALTER COLUMN "status" TYPE "VISITREQUESTSTATUS_new" USING ("status"::text::"VISITREQUESTSTATUS_new");
ALTER TYPE "VISITREQUESTSTATUS" RENAME TO "VISITREQUESTSTATUS_old";
ALTER TYPE "VISITREQUESTSTATUS_new" RENAME TO "VISITREQUESTSTATUS";
DROP TYPE "VISITREQUESTSTATUS_old";
COMMIT;
