/*
  Warnings:

  - You are about to drop the column `section` on the `Inmate` table. All the data in the column will be lost.
  - You are about to drop the column `available_from` on the `InmateAvaliabilitySettings` table. All the data in the column will be lost.
  - You are about to drop the column `available_to` on the `InmateAvaliabilitySettings` table. All the data in the column will be lost.
  - You are about to drop the column `section` on the `Room` table. All the data in the column will be lost.
  - Added the required column `sectionid` to the `Inmate` table without a default value. This is not possible if the table is not empty.
  - Added the required column `sectionid` to the `Room` table without a default value. This is not possible if the table is not empty.

*/
-- CreateEnum
CREATE TYPE "MeetingStatus" AS ENUM ('Not_Started', 'Started', 'Ended');

-- AlterTable
ALTER TABLE "Inmate" DROP COLUMN "section",
ADD COLUMN     "sectionid" TEXT NOT NULL;

-- AlterTable
ALTER TABLE "InmateAvaliabilitySettings" DROP COLUMN "available_from",
DROP COLUMN "available_to";

-- AlterTable
ALTER TABLE "Room" DROP COLUMN "section",
ADD COLUMN     "sectionid" TEXT NOT NULL;

-- DropEnum
DROP TYPE "section";

-- CreateTable
CREATE TABLE "Section" (
    "id" TEXT NOT NULL DEFAULT gen_random_uuid(),
    "name" TEXT NOT NULL,

    CONSTRAINT "Section_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Meeting" (
    "id" TEXT NOT NULL DEFAULT gen_random_uuid(),
    "title" TEXT NOT NULL,
    "status" "MeetingStatus" NOT NULL,
    "started_at" TIME NOT NULL,
    "ended_at" TIME NOT NULL,
    "duration" INTEGER NOT NULL,
    "meeting_date_time" TIME NOT NULL,

    CONSTRAINT "Meeting_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "MeetingRecord" (
    "id" TEXT NOT NULL DEFAULT gen_random_uuid(),
    "meeting_id" TEXT NOT NULL,
    "session_id" TEXT NOT NULL DEFAULT gen_random_uuid(),
    "recording_url" TEXT NOT NULL,
    "record_size" INTEGER NOT NULL,
    "duration" INTEGER NOT NULL,
    "created_at" TIME NOT NULL,
    "deleted_at" TIME NOT NULL,

    CONSTRAINT "MeetingRecord_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "Inmate" ADD CONSTRAINT "Inmate_sectionid_fkey" FOREIGN KEY ("sectionid") REFERENCES "Section"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Room" ADD CONSTRAINT "Room_sectionid_fkey" FOREIGN KEY ("sectionid") REFERENCES "Section"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MeetingRecord" ADD CONSTRAINT "MeetingRecord_meeting_id_fkey" FOREIGN KEY ("meeting_id") REFERENCES "Meeting"("id") ON DELETE CASCADE ON UPDATE CASCADE;
