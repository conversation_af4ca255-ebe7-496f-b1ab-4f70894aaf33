-- Add the function to handle notification creation
CREATE OR REPLACE FUNCTION notify_on_visit_request_change()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.status IS DISTINCT FROM OLD.status THEN
        IF NEW.status = 'STARTS_SOON' THEN
            INSERT INTO "Notification" (datetime, visitor_id, visit_id, message, recipient_id)
            VALUES (NOW(), NEW.visitor_id, NEW.id, 'reminder', NEW.approved_by);
        ELSIF NEW.status = 'APPROVED' THEN
            INSERT INTO "Notification" (datetime, visitor_id, visit_id, message, recipient_id)
            VALUES (NOW(), NEW.visitor_id, NEW.id, 'approved', NEW.approved_by);
        ELSIF NEW.status = 'PENDING' THEN
            INSERT INTO "Notification" (datetime, visitor_id, visit_id, message, recipient_id)
            VALUES (NOW(), NEW.visitor_id, NEW.id, 'pending', NEW.approved_by);
        ELSIF NEW.status = 'DENIED' THEN
            INSERT INTO "Notification" (datetime, visitor_id, visit_id, message, recipient_id)
            VALUES (NOW(), NEW.visitor_id, NEW.id, 'denied', NEW.approved_by);
        END IF;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger on the Visit_Request table
CREATE OR REPLACE TRIGGER visit_request_status_change
AFTER UPDATE ON "Visit_Request"
FOR EACH ROW
EXECUTE FUNCTION notify_on_visit_request_change();
