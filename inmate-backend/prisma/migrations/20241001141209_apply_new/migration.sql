/*
  Warnings:

  - You are about to drop the column `available_from` on the `Inmate` table. All the data in the column will be lost.
  - You are about to drop the column `available_to` on the `Inmate` table. All the data in the column will be lost.
  - The `visit_number` column on the `Visit_Request` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - Added the required column `available_from` to the `RoomAvaliabilitySettings` table without a default value. This is not possible if the table is not empty.
  - Added the required column `available_to` to the `RoomAvaliabilitySettings` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE "Visit_Request" DROP CONSTRAINT "Visit_Request_approved_by_fkey";

-- AlterTable
ALTER TABLE "Inmate" DROP COLUMN "available_from",
DROP COLUMN "available_to";

-- AlterTable
ALTER TABLE "RoomAvaliabilitySettings" ADD COLUMN     "available_from" TIME NOT NULL,
ADD COLUMN     "available_to" TIME NOT NULL;

-- AlterTable
ALTER TABLE "Visit_Request" ADD COLUMN     "user_id" TEXT,
ALTER COLUMN "visitor_id" DROP NOT NULL,
DROP COLUMN "visit_number",
ADD COLUMN     "visit_number" SERIAL NOT NULL;

-- AlterTable
ALTER TABLE "Visitor" ADD COLUMN     "blocked" BOOLEAN NOT NULL DEFAULT true;

-- AddForeignKey
ALTER TABLE "Visit_Request" ADD CONSTRAINT "Visit_Request_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;
