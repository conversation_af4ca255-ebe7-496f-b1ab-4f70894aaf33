/*
  Warnings:

  - The values [UNCEL] on the enum `InmateVitorRelation` will be removed. If these variants are still used in the database, this will fail.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "InmateVitorRelation_new" AS ENUM ('MOTHER', '<PERSON>TH<PERSON>', '<PERSON>OTH<PERSON>', '<PERSON>ISTE<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON>CL<PERSON>', 'AUNT', 'NEPHEW', 'NIECE', 'COUSIN', 'GREAT_UNCLE', 'GREAT_AUNT', 'GREAT_NEPHEW', 'GREAT_NIECE', 'HUSBUND', '<PERSON>WYER', '<PERSON><PERSON><PERSON>_REPRESENTATIVE', 'GOVERNMENT_OFFICIAL', 'SOCIAL_WORKER');
ALTER TABLE "Inmate_Visitor" ALTER COLUMN "relation" TYPE "InmateVitorRelation_new" USING ("relation"::text::"InmateVitorRelation_new");
ALTER TYPE "InmateVitorRelation" RENAME TO "InmateVitorRelation_old";
ALTER TYPE "InmateVitorRelation_new" RENAME TO "InmateVitorRelation";
DROP TYPE "InmateVitorRelation_old";
COMMIT;
