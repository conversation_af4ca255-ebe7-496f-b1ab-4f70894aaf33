/*
  Warnings:

  - The values [WENDESDAY] on the enum `Days` will be removed. If these variants are still used in the database, this will fail.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "public"."Days_new" AS ENUM ('SUNDAY', 'MONDAY', 'TUESDAY', 'WEDNESDAY', 'THURSDAY', 'FRIDAY', 'SATURDAY');
ALTER TABLE "public"."RoomAvaliabilitySettings" ALTER COLUMN "day" TYPE "public"."Days_new" USING ("day"::text::"public"."Days_new");
ALTER TABLE "public"."InmateAvaliabilitySettings" ALTER COLUMN "day" TYPE "public"."Days_new" USING ("day"::text::"public"."Days_new");
ALTER TYPE "public"."Days" RENAME TO "Days_old";
ALTER TYPE "public"."Days_new" RENAME TO "Days";
DROP TYPE "public"."Days_old";
COMMIT;
