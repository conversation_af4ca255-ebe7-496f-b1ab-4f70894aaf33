-- This is an empty migration.
GRANT SELECT, INSERT, UPDATE,DELETE ON ALL TABLES IN SCHEMA "public" TO anon;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO anon;
-- GRANT USAGE ON SCHEMA auth TO anon, authenticated, service_role;
-- GRANT ALL ON ALL TABLES IN SCHEMA auth TO anon, authenticated, service_role;
-- GRANT ALL ON ALL ROUTINES IN SCHEMA auth TO anon, authenticated, service_role;
-- GRANT ALL ON ALL SEQUENCES IN SCHEMA auth TO anon, authenticated, service_role;
-- ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA auth GRANT ALL ON TABLES TO anon, authenticated, service_role;
-- ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA auth GRANT ALL ON ROUTINES TO anon, authenticated, service_role;
-- ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA auth GRANT ALL ON SEQUENCES TO anon, authenticated, service_role;
grant usage on schema "public" to anon;