-- This is an empty migration.
-- This is an empty migration.

-- This is an empty migration.

-- This is an empty migration.

-- Your table migrations and other SQL statements might already be here

-- Add the function to handle notification creation
CREATE OR REPLACE FUNCTION assign_room_to_visit()
RETURNS TRIGGER AS $$
BEGIN
  -- Assign room_id based on criteria
  SELECT r.id
  INTO NEW.room_id
  FROM public."Room" r
  JOIN public."Inmate" i ON i.sectionid = r.sectionid
  WHERE r.active = true
    AND i.id = NEW.inmate_id
    AND NOT EXISTS (
      SELECT 1
      FROM public."Visit_Request" vr
      WHERE vr.room_id = r.id
        AND vr.datetime <= NEW.datetime + interval '1 minute' * NEW.duration
        AND vr.datetime + interval '1 minute' * vr.duration >= NEW.datetime
        AND (vr.status = 'APPROVED' OR vr.status = 'STARTS_SOON')
        AND vr."isDeleted" IS NULL
    )
  ORDER BY random() -- Randomly pick any available room
  LIMIT 1;

  -- If no room is found, raise an error
  IF NEW.room_id IS NULL THEN
    RAISE EXCEPTION 'No available room for the requested time slot.';
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;


-- Create the trigger on the Visit_Request table
CREATE OR REPLACE TRIGGER set_room_in_visit_request
BEFORE INSERT ON public."Visit_Request"
FOR EACH ROW
EXECUTE FUNCTION assign_room_to_visit();