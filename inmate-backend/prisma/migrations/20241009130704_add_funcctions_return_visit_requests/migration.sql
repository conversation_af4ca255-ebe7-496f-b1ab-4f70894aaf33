CREATE OR REPLACE FUNCTION get_visitor_requests_with_relation(visitor_id_input UUID)
RETURNS TABLE(
    request_id TEXT,
    request_status TEXT,
    request_number TEXT,
    inamte_full_name TEXT,
    inmate_id TEXT,
    visitor_id TEXT,
    request_date timestamp,
    relation TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT vr.id,vr.status::TEXT ,vr.visit_number::TEXT , inm.full_name,vr.inmate_id, vr.visitor_id, vr.datetime, iv.relation::TEXT 
    FROM public."Visit_Request" vr
    JOIN public."Inmate_Visitor" iv ON vr.inmate_id = iv.inmate_id AND vr.visitor_id = iv.visitor_id
    JOIN public."Inmate" inm
    ON inm.id = vr.inmate_id
    WHERE vr.visitor_id = visitor_id_input::TEXT AND vr.status IN ('CANCELED','DENIED','APPROVED','PENDING') AND iv."isDeleted" IS null;
END;
$$ LANGUAGE plpgsql;
-- DROP FUNCTION get_visitor_requests_with_relation(uuid)
-- DROP FUNCTION get_visitor_requests_with_relation(uuid)




-- This is an empty migration.

-- This is an empty migration.

-- Your table migrations and other SQL statements might already be here

-- Add the function to handle notification creation
CREATE OR REPLACE FUNCTION notify_on_visit_request_change()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.status IS DISTINCT FROM OLD.status AND NEW.visitor_id IS NOT NULL THEN
        INSERT INTO "Notification" ( datetime, visitor_id, visit_id, message)
        VALUES ( NOW(), NEW.visitor_id, NEW.id, NEW.status);
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger on the Visit_Request table
CREATE OR REPLACE TRIGGER visit_request_status_change
AFTER UPDATE ON "Visit_Request"
FOR EACH ROW
EXECUTE FUNCTION notify_on_visit_request_change();