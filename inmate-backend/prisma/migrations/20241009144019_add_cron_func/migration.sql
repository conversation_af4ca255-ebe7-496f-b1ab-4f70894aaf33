-- AlterTable
ALTER TABLE "public"."Inmate" ALTER COLUMN "age" DROP NOT NULL;

-- CREATE EXTENSION IF NOT EXISTS pg_cron;
-- CREATE SCHEMA cron;

-- SELECT cron.schedule(
--     'visit-request-status-check',  -- name of the cron job
--     '*/5 * * * *',  -- every 5 minutes
--     $$
--     -- SQL script for checking and updating visit request statuses
--     -- Update Visit_Request status to 'starts soon' if it's within one hour
--     UPDATE public."Visit_Request"
--     SET status = 'STARTS_SOON'
--     WHERE status = 'APPROVED'
--       AND datetime <= now() + interval '1 hour'
--       AND datetime > now();

--     -- Update Visit_Request status to 'missed' if the time has passed
--     -- and the status is not completed, cancelled, or denied
--     UPDATE public."Visit_Request"
--     SET status = 'MISSED'
--     WHERE datetime < now()
--       AND status NOT IN ('COMPLETED', 'CANCELED', 'DENIED');
--     $$
-- );
