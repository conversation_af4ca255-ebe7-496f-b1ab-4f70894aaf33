-- Step 1: Create the trigger function
CREATE OR R<PERSON><PERSON>CE FUNCTION approve_visit_if_whitelisted()
RETURNS TRIGGER AS $$
BEGIN
    IF EXISTS (
        SELECT 1
        FROM "Inmate_Visitor"
        WHERE inmate_id = NEW.inmate_id
        AND visitor_id = NEW.visitor_id
        AND is_white_list = TRUE
    ) THEN
        NEW.status := 'APPROVED';
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Step 2: Create the trigger
CREATE TRIGGER check_whitelist_before_insert
BEFORE INSERT ON "Visit_Request"
FOR EACH ROW
EXECUTE FUNCTION approve_visit_if_whitelisted();
