/*
  Warnings:

  - The `started_at` column on the `Meeting` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The `ended_at` column on the `Meeting` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The `meeting_date_time` column on the `Meeting` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The `created_at` column on the `MeetingRecord` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The `deleted_at` column on the `MeetingRecord` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - You are about to drop the column `end_meeting_date` on the `Visit_Request` table. All the data in the column will be lost.
  - You are about to drop the column `start_meeting_date` on the `Visit_Request` table. All the data in the column will be lost.

*/
-- AlterTable
ALTER TABLE "public"."Meeting" DROP COLUMN "started_at",
ADD COLUMN     "started_at" TIMESTAMP(3),
DROP COLUMN "ended_at",
ADD COLUMN     "ended_at" TIMESTAMP(3),
DROP COLUMN "meeting_date_time",
ADD COLUMN     "meeting_date_time" TIMESTAMP(3);

-- AlterTable
ALTER TABLE "public"."MeetingRecord" ALTER COLUMN "session_id" DROP NOT NULL,
ALTER COLUMN "recording_url" DROP NOT NULL,
ALTER COLUMN "record_size" DROP NOT NULL,
ALTER COLUMN "duration" DROP NOT NULL,
DROP COLUMN "created_at",
ADD COLUMN     "created_at" TIMESTAMP(3),
DROP COLUMN "deleted_at",
ADD COLUMN     "deleted_at" TIMESTAMP(3);

-- AlterTable
ALTER TABLE "public"."Visit_Request" DROP COLUMN "end_meeting_date",
DROP COLUMN "start_meeting_date";
