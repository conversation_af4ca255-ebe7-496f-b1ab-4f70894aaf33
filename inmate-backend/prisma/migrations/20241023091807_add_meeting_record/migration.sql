-- AlterTable
ALTER TABLE "public"."Visit_Request" ADD COLUMN     "meeting_id" TEXT;

-- Add<PERSON><PERSON>ign<PERSON>ey
ALTER TABLE "public"."Visit_Request" ADD CONSTRAINT "Visit_Request_meeting_id_fkey" FOREIGN KEY ("meeting_id") REFERENCES "public"."Meeting"("id") ON DELETE CASCADE ON UPDATE CASCADE;


CREATE OR REPLACE FUNCTION create_meeting_on_approved()
RETURNS TRIGGER AS $$
DECLARE
new_meeting_id UUID;
BEGIN
  -- Check if the status is changing to 'APPROVED'
  IF NEW.status = 'APPROVED' THEN
    -- Insert a new meeting record with room_id from Visit_Request
    INSERT INTO public."Meeting" (id, title, status, meeting_date_time, duration, room_id)
    VALUES (
      gen_random_uuid(),
      'Meeting for visit request number ' || NEW.visit_number,
      'Not_Started',  -- Assuming the meeting starts at this status
      NEW.datetime,  -- Set meeting date time to the visit request's datetime
      NEW.duration,  -- Set the duration of the meeting based on visit request
      NEW.room_id    -- Assign the room_id from Visit_Request
    )
    RETURNING id INTO new_meeting_id;

    -- Update the visit request with the new meeting ID
UPDATE public."Visit_Request"
SET meeting_id = new_meeting_id
WHERE id = NEW.id;
END IF;

  -- Return the updated visit request record
RETURN NEW;
END;

$$ LANGUAGE plpgsql;

CREATE OR REPLACE TRIGGER trigger_create_meeting_on_approved
AFTER INSERT OR UPDATE OF status
ON public."Visit_Request"
FOR EACH ROW
WHEN (NEW.status = 'APPROVED')
EXECUTE FUNCTION create_meeting_on_approved();