

CREATE OR REPLACE FUNCTION get_visitor_requests_with_meeting_records_not_archived()
RETURNS TABLE(
    request_id TEXT,
    request_status TEXT,
    request_number TEXT,
    inmate_full_name TEXT,
    inmate_id TEXT,
    visitor_name TEXT,
    request_date TIMESTAMP,
    relation TEXT,
    meeting_status TEXT,
    meeting_date_time TIMESTAMP,
    record_id TEXT,
    recording_url TEXT,
    record_duration INT,
    archived BOOLEAN,
    record_size INT,
    feedback_rate INT,
    feedback_comment TEXT

) AS $$
BEGIN
    RETURN QUERY
SELECT DISTINCT ON (mrec.id)
        vr.id,
        vr.status::TEXT,
        vr.visit_number::TEXT,
        inm.full_name AS inmate_full_name,
        vr.inmate_id,
        vis.full_name AS visitor_name,
        vr.datetime AS request_date,
        iv.relation::TEXT,
        mtg.status::TEXT AS meeting_status,
        mtg.meeting_date_time,
        mrec.id,
        mrec.recording_url,
        mrec.duration AS record_duration,
        mrec.archived,
        mrec.record_size,
        feedback.rate,
        feedback.comment
    FROM public."Visit_Request" vr
    LEFT JOIN public."Inmate_Visitor" iv ON vr.inmate_id = iv.inmate_id AND vr.visitor_id = iv.visitor_id
    LEFT JOIN public."Inmate" inm ON inm.id = vr.inmate_id
    JOIN public."Visitor" vis ON vis.id = vr.visitor_id
    LEFT JOIN public."Meeting" mtg ON mtg.id = vr.meeting_id
    JOIN public."MeetingRecord" mrec ON mrec.meeting_id = mtg.id
    LEFT JOIN public."MeetingFeedback" feedback ON mrec.meeting_id = feedback.meeting_id
    WHERE mrec."deleted_at" IS NULL AND mrec.archived = FALSE;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION get_visitor_requests_with_meeting_records()
RETURNS TABLE(
    request_id TEXT,
    request_status TEXT,
    request_number TEXT,
    inmate_full_name TEXT,
    inmate_id TEXT,
    visitor_name TEXT,
    request_date TIMESTAMP,
    relation TEXT,
    meeting_status TEXT,
    meeting_date_time TIMESTAMP,
    record_id TEXT,
    recording_url TEXT,
    record_duration INT,
    archived BOOLEAN,
    record_size INT,
    feedback_rate INT,
    feedback_comment TEXT

) AS $$
BEGIN
    RETURN QUERY
SELECT DISTINCT ON (mrec.id)
        vr.id,
        vr.status::TEXT,
        vr.visit_number::TEXT,
        inm.full_name AS inmate_full_name,
        vr.inmate_id,
        vis.full_name AS visitor_name,
        vr.datetime AS request_date,
        iv.relation::TEXT,
        mtg.status::TEXT AS meeting_status,
        mtg.meeting_date_time,
        mrec.id,
        mrec.recording_url,
        mrec.duration AS record_duration,
        mrec.archived,
        mrec.record_size,
        feedback.rate,
        feedback.comment
    FROM public."Visit_Request" vr
    LEFT JOIN public."Inmate_Visitor" iv ON vr.inmate_id = iv.inmate_id AND vr.visitor_id = iv.visitor_id
    LEFT JOIN public."Inmate" inm ON inm.id = vr.inmate_id
    JOIN public."Visitor" vis ON vis.id = vr.visitor_id
    LEFT JOIN public."Meeting" mtg ON mtg.id = vr.meeting_id
    JOIN public."MeetingRecord" mrec ON mrec.meeting_id = mtg.id
    LEFT JOIN public."MeetingFeedback" feedback ON mrec.meeting_id = feedback.meeting_id
    WHERE mrec."deleted_at" IS NULL AND mrec.archived = TRUE;
END;
$$ LANGUAGE plpgsql;


CREATE OR REPLACE FUNCTION get_admin_requests_with_meeting_records()
RETURNS TABLE(
    request_id TEXT,
    request_status TEXT,
    request_number TEXT,
    inmate_full_name TEXT,
    inmate_id TEXT,
    admin_id TEXT,
    admin_name TEXT,
    request_date TIMESTAMP,
    meeting_status TEXT,
    meeting_date_time TIMESTAMP,
    record_id TEXT,
    recording_url TEXT,
    record_duration INT,
    archived BOOLEAN,
    record_size INT,
    feedback_rate INT,
    feedback_comment TEXT

) AS $$
BEGIN
    RETURN QUERY
SELECT DISTINCT ON (vr.id)
        vr.id,
        vr.status::TEXT,
        vr.visit_number::TEXT,
        inm.full_name AS inmate_full_name,
        vr.inmate_id,
        admin.id as admin_id,
        admin.full_name AS admin_name,
        vr.datetime AS request_date,
        mtg.status::TEXT AS meeting_status,
        mtg.meeting_date_time,
        mrec.id,
        mrec.recording_url,
        mrec.duration AS record_duration,
        mrec.archived,
        mrec.record_size,
        feedback.rate,
        feedback.comment
    FROM public."Visit_Request" vr

    LEFT JOIN public."Inmate" inm ON inm.id = vr.inmate_id
    JOIN public."User" admin ON admin.id = vr.user_id
    LEFT JOIN public."Meeting" mtg ON mtg.id = vr.meeting_id
    JOIN public."MeetingRecord" mrec ON mrec.meeting_id = mtg.id
    LEFT JOIN public."MeetingFeedback" feedback ON mrec.meeting_id = feedback.meeting_id
    WHERE mrec."deleted_at" IS NULL AND mrec.archived = TRUE AND vr.visitor_id IS NULL;
END;
$$ LANGUAGE plpgsql;







CREATE OR REPLACE FUNCTION get_admin_requests_with_meeting_records_not_archived()
RETURNS TABLE(
    request_id TEXT,
    request_status TEXT,
    request_number TEXT,
    inmate_full_name TEXT,
    inmate_id TEXT,
    admin_id TEXT,
    admin_name TEXT,
    request_date TIMESTAMP,
    meeting_status TEXT,
    meeting_date_time TIMESTAMP,
    record_id TEXT,
    recording_url TEXT,
    record_duration INT,
    archived BOOLEAN,
    record_size INT,
    feedback_rate INT,
    feedback_comment TEXT

) AS $$
BEGIN
    RETURN QUERY
SELECT DISTINCT ON (vr.id)
        vr.id,
        vr.status::TEXT,
        vr.visit_number::TEXT,
        inm.full_name AS inmate_full_name,
        vr.inmate_id,
        admin.id as admin_id,
        admin.full_name AS admin_name,
        vr.datetime AS request_date,
        mtg.status::TEXT AS meeting_status,
        mtg.meeting_date_time,
        mrec.id,
        mrec.recording_url,
        mrec.duration AS record_duration,
        mrec.archived,
        mrec.record_size,
        feedback.rate,
        feedback.comment
    FROM public."Visit_Request" vr

    LEFT JOIN public."Inmate" inm ON inm.id = vr.inmate_id
    JOIN public."User" admin ON admin.id = vr.user_id
    LEFT JOIN public."Meeting" mtg ON mtg.id = vr.meeting_id
    JOIN public."MeetingRecord" mrec ON mrec.meeting_id = mtg.id
    LEFT JOIN public."MeetingFeedback" feedback ON mrec.meeting_id = feedback.meeting_id
    WHERE mrec."deleted_at" IS NULL AND mrec.archived = FALSE AND vr.visitor_id IS NULL;
END;
$$ LANGUAGE plpgsql;
GRANT ALL PRIVILEGES ON FUNCTION get_visitor_requests_with_meeting_records_not_archived() TO postgres;
GRANT ALL PRIVILEGES ON FUNCTION get_visitor_requests_with_meeting_records() TO postgres;