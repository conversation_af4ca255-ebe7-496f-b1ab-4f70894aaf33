-- CreateTable
CREATE TABLE "public"."MeetingFeedback" (
    "id" TEXT NOT NULL DEFAULT gen_random_uuid(),
    "meeting_id" TEXT NOT NULL,
    "visitor_id" TEXT NOT NULL,
    "comment" TEXT,
    "rate" INTEGER NOT NULL,

    CONSTRAINT "MeetingFeedback_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "public"."MeetingFeedback" ADD CONSTRAINT "MeetingFeedback_meeting_id_fkey" FOREIGN KEY ("meeting_id") REFERENCES "public"."Meeting"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeign<PERSON><PERSON>
ALTER TABLE "public"."MeetingFeedback" ADD CONSTRAINT "MeetingFeedback_visitor_id_fkey" FOREIGN KEY ("visitor_id") REFERENCES "public"."Visitor"("id") ON DELETE CASCADE ON UPDATE CASCADE;


grant usage on schema "public" to anon;
-- This is an empty migration.
GRANT SELECT, INSERT, UPDATE,DELETE ON ALL TABLES IN SCHEMA "public" TO anon;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO anon;
-- GRANT USAGE ON SCHEMA auth TO anon, authenticated, service_role;
-- GRANT ALL ON ALL TABLES IN SCHEMA auth TO anon, authenticated, service_role;
-- GRANT ALL ON ALL ROUTINES IN SCHEMA auth TO anon, authenticated, service_role;
-- GRANT ALL ON ALL SEQUENCES IN SCHEMA auth TO anon, authenticated, service_role;
-- ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA auth GRANT ALL ON TABLES TO anon, authenticated, service_role;
-- ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA auth GRANT ALL ON ROUTINES TO anon, authenticated, service_role;
-- ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA auth GRANT ALL ON SEQUENCES TO anon, authenticated, service_role;
grant usage on schema "public" to anon,authenticated;

GRANT SELECT, INSERT, UPDATE,DELETE ON ALL TABLES IN SCHEMA "public" TO anon,authenticated;