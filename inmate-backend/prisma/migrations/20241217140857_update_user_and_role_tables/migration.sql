/*
  Warnings:

  - Changed the type of `name` on the `Role` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Added the required column `first_name` to the `User` table without a default value. This is not possible if the table is not empty.
  - Added the required column `last_name` to the `User` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "public"."Role" DROP COLUMN "name",
ADD COLUMN     "name" TEXT NOT NULL;

-- AlterTable
ALTER TABLE "public"."User" ADD COLUMN     "first_name" TEXT NOT NULL,
ADD COLUMN     "last_name" TEXT NOT NULL;

-- --handle public user function
-- CREATE OR REPLACE FUNCTION handle_public_user()
-- RETURNS TRIGGER AS $$
-- DECLARE
-- default_role_id UUID;
-- user_name TEXT;
--
-- BEGIN
-- -- Log the start of the function
-- RAISE NOTICE 'Trigger started for user ID: %, email: %', NEW.id, NEW.email;
--
-- -- Fetch the default role ID
-- SELECT id INTO default_role_id
-- FROM public."Role"
-- WHERE name = 'default_role' -- Replace with your desired role name
-- LIMIT 1;
--
-- IF default_role_id IS NULL THEN
-- RAISE NOTICE 'No default role found. Exiting trigger.';
-- RETURN NEW;
-- END IF;
-- user_name := split_part(NEW.email, '@', 1);
--
-- -- Insert the user into public."User" table
-- INSERT INTO public."User"(user_auth_id, email, role_id,password,full_name)
-- VALUES (NEW.id, NEW.email, default_role_id,NEW.encrypted_password,user_name)
-- ON CONFLICT (email)
-- DO UPDATE SET user_auth_id = NEW.id;
--
-- -- Log successful insertion
-- RAISE NOTICE 'User successfully inserted/updated in public."User"';
--
-- RETURN NEW;
-- EXCEPTION WHEN OTHERS THEN
-- RAISE NOTICE 'Error in trigger: %', SQLERRM;
-- RETURN NEW;
-- END;
-- $$ LANGUAGE plpgsql SECURITY DEFINER;
--
--
-- --- trigger
-- CREATE OR REPLACE TRIGGER on_auth_user_created
-- AFTER INSERT ON auth.users FOR EACH ROW
-- EXECUTE FUNCTION handle_public_user ();