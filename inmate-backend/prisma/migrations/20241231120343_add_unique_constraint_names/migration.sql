/*
  Warnings:

  - You are about to drop the column `available_from` on the `InmateAvaliabilitySettings` table. All the data in the column will be lost.
  - You are about to drop the column `available_to` on the `InmateAvaliabilitySettings` table. All the data in the column will be lost.
  - Added the required column `number_of_visits` to the `InmateAvaliabilitySettings` table without a default value. This is not possible if the table is not empty.

*/
-- CreateEnum
CREATE TYPE "public"."AvailabilityType" AS ENUM ('ROOM', 'SECTION', 'INMATE');

-- AlterTable
ALTER TABLE "public"."Inmate" ADD COLUMN     "room_id" TEXT;

-- AlterTable
ALTER TABLE "public"."InmateAvaliabilitySettings" DROP COLUMN "available_from",
DROP COLUMN "available_to",
ADD COLUMN     "number_of_visits" INTEGER NOT NULL;

-- AlterTable
ALTER TABLE "public"."Section" ADD COLUMN     "is_available" BOOLEAN NOT NULL DEFAULT true,
ADD COLUMN     "is_deleted" BOOLEAN NOT NULL DEFAULT false;

-- AlterTable
ALTER TABLE "public"."User" ADD COLUMN     "user_auth_id" UUID,
ALTER COLUMN "full_name" DROP NOT NULL,
ALTER COLUMN "password" DROP NOT NULL,
ALTER COLUMN "first_name" DROP NOT NULL,
ALTER COLUMN "last_name" DROP NOT NULL;

-- CreateTable
CREATE TABLE "public"."Section_Availability_Settings" (
    "id" TEXT NOT NULL DEFAULT gen_random_uuid(),
    "day" TEXT NOT NULL,
    "section_id" TEXT NOT NULL,

    CONSTRAINT "Section_Availability_Settings_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."AvaliabilityTimes" (
    "id" TEXT NOT NULL DEFAULT gen_random_uuid(),
    "setting_id" TEXT NOT NULL,
    "type" "public"."AvailabilityType" NOT NULL,
    "available_from" TIME(6) NOT NULL,
    "available_to" TIME(6) NOT NULL,

    CONSTRAINT "AvaliabilityTimes_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."InmateSetting" (
    "id" TEXT NOT NULL DEFAULT gen_random_uuid(),
    "inmate_id" TEXT NOT NULL,
    "key" TEXT NOT NULL,
    "value" TEXT NOT NULL,

    CONSTRAINT "InmateSetting_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "InmateSetting_inmate_id_key_key" ON "public"."InmateSetting"("inmate_id", "key");

-- AddForeignKey
ALTER TABLE "public"."Inmate" ADD CONSTRAINT "Inmate_room_id_fkey" FOREIGN KEY ("room_id") REFERENCES "public"."Room"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."Section_Availability_Settings" ADD CONSTRAINT "Section_Availability_Settings_section_id_fkey" FOREIGN KEY ("section_id") REFERENCES "public"."Section"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."AvaliabilityTimes" ADD CONSTRAINT "fk_room_setting" FOREIGN KEY ("setting_id") REFERENCES "public"."RoomAvaliabilitySettings"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."AvaliabilityTimes" ADD CONSTRAINT "fk_section_setting" FOREIGN KEY ("setting_id") REFERENCES "public"."Section_Availability_Settings"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."AvaliabilityTimes" ADD CONSTRAINT "fk_inmate_setting" FOREIGN KEY ("setting_id") REFERENCES "public"."InmateAvaliabilitySettings"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."InmateSetting" ADD CONSTRAINT "InmateSetting_inmate_id_fkey" FOREIGN KEY ("inmate_id") REFERENCES "public"."Inmate"("id") ON DELETE CASCADE ON UPDATE CASCADE;
