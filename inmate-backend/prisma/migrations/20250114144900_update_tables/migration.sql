/*
  Warnings:

  - You are about to drop the column `number_of_visits` on the `InmateAvaliabilitySettings` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[inmate_code]` on the table `Inmate` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `is_suspended` to the `Inmate` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "public"."Inmate" ADD COLUMN     "is_suspended" BOOLEAN NOT NULL;

-- AlterTable
ALTER TABLE "public"."InmateAvaliabilitySettings" DROP COLUMN "number_of_visits";

-- CreateIndex
CREATE UNIQUE INDEX "Inmate_inmate_code_key" ON "public"."Inmate"("inmate_code");
