/*
  Warnings:

  - You are about to drop the `AvaliabilityTimes` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `Inmate` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `InmateAvaliabilitySettings` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `InmateSetting` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `Inmate_Visitor` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `Inmate_Visitor_Documents` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `Meeting` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `MeetingFeedback` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `MeetingRecord` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `Notification` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `Permission` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `Role` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `Role_Permission` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `Room` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `RoomAvaliabilitySettings` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `Section` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `Section_Availability_Settings` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `User` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `User_Permission` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `Visit_Request` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `Visitor` table. If the table is not empty, all the data it contains will be lost.

*/
-- CreateEnum
CREATE TYPE "inmate_status" AS ENUM ('AVAILABLE', 'UNAVAILABLE', 'SUSPENDED');

-- CreateEnum
CREATE TYPE "availability_type" AS ENUM ('room', 'section', 'inmate');

-- CreateEnum
CREATE TYPE "visit_request_status" AS ENUM ('APPROVED', 'PENDING', 'STARTS_SOON', 'DENIED', 'CANCELED', 'MISSED', 'COMPLETED');

-- CreateEnum
CREATE TYPE "days" AS ENUM ('sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday');

-- CreateEnum
CREATE TYPE "inmate_visitor_relation" AS ENUM ('MOTHER', 'FATHER', 'BROTHER', 'SISTER', 'SON', 'DAUGHTER', 'GRANDFATHER', 'GRANDMOTHER', 'GRANDSON', 'GRANDDAUGHTER', 'UNCLE', 'AUNT', 'NEPHEW', 'NIECE', 'COUSIN', 'GREAT_UNCLE', 'GREAT_AUNT', 'GREAT_NEPHEW', 'GREAT_NIECE', 'HUSBUND', 'LAWYER', 'LEGAL_REPRESENTATIVE', 'GOVERNMENT_OFFICIAL', 'SOCIAL_WORKER', 'OTHER');

-- CreateEnum
CREATE TYPE "meeting_status" AS ENUM ('not_started', 'started', 'ended');

-- DropForeignKey
ALTER TABLE "Inmate" DROP CONSTRAINT "Inmate_room_id_fkey";

-- DropForeignKey
ALTER TABLE "Inmate" DROP CONSTRAINT "Inmate_sectionid_fkey";

-- DropForeignKey
ALTER TABLE "InmateAvaliabilitySettings" DROP CONSTRAINT "InmateAvaliabilitySettings_inmate_id_fkey";

-- DropForeignKey
ALTER TABLE "InmateSetting" DROP CONSTRAINT "InmateSetting_inmate_id_fkey";

-- DropForeignKey
ALTER TABLE "Inmate_Visitor" DROP CONSTRAINT "Inmate_Visitor_inmate_id_fkey";

-- DropForeignKey
ALTER TABLE "Inmate_Visitor" DROP CONSTRAINT "Inmate_Visitor_visitor_id_fkey";

-- DropForeignKey
ALTER TABLE "Inmate_Visitor_Documents" DROP CONSTRAINT "Inmate_Visitor_Documents_inmate_visitor_id_fkey";

-- DropForeignKey
ALTER TABLE "Meeting" DROP CONSTRAINT "Meeting_room_id_fkey";

-- DropForeignKey
ALTER TABLE "MeetingFeedback" DROP CONSTRAINT "MeetingFeedback_meeting_id_fkey";

-- DropForeignKey
ALTER TABLE "MeetingFeedback" DROP CONSTRAINT "MeetingFeedback_visitor_id_fkey";

-- DropForeignKey
ALTER TABLE "MeetingRecord" DROP CONSTRAINT "MeetingRecord_meeting_id_fkey";

-- DropForeignKey
ALTER TABLE "Notification" DROP CONSTRAINT "Notification_visit_id_fkey";

-- DropForeignKey
ALTER TABLE "Notification" DROP CONSTRAINT "Notification_visitor_id_fkey";

-- DropForeignKey
ALTER TABLE "Role_Permission" DROP CONSTRAINT "Role_Permission_permission_id_fkey";

-- DropForeignKey
ALTER TABLE "Role_Permission" DROP CONSTRAINT "Role_Permission_role_id_fkey";

-- DropForeignKey
ALTER TABLE "Room" DROP CONSTRAINT "Room_sectionid_fkey";

-- DropForeignKey
ALTER TABLE "RoomAvaliabilitySettings" DROP CONSTRAINT "RoomAvaliabilitySettings_room_id_fkey";

-- DropForeignKey
ALTER TABLE "Section_Availability_Settings" DROP CONSTRAINT "Section_Availability_Settings_section_id_fkey";

-- DropForeignKey
ALTER TABLE "User" DROP CONSTRAINT "User_role_id_fkey";

-- DropForeignKey
ALTER TABLE "User_Permission" DROP CONSTRAINT "User_Permission_permission_id_fkey";

-- DropForeignKey
ALTER TABLE "User_Permission" DROP CONSTRAINT "User_Permission_user_id_fkey";

-- DropForeignKey
ALTER TABLE "Visit_Request" DROP CONSTRAINT "Visit_Request_inmate_id_fkey";

-- DropForeignKey
ALTER TABLE "Visit_Request" DROP CONSTRAINT "Visit_Request_meeting_id_fkey";

-- DropForeignKey
ALTER TABLE "Visit_Request" DROP CONSTRAINT "Visit_Request_room_id_fkey";

-- DropForeignKey
ALTER TABLE "Visit_Request" DROP CONSTRAINT "Visit_Request_user_id_fkey";

-- DropForeignKey
ALTER TABLE "Visit_Request" DROP CONSTRAINT "Visit_Request_visitor_id_fkey";

-- DropTable
DROP TABLE "AvaliabilityTimes";

-- DropTable
DROP TABLE "Inmate";

-- DropTable
DROP TABLE "InmateAvaliabilitySettings";

-- DropTable
DROP TABLE "InmateSetting";

-- DropTable
DROP TABLE "Inmate_Visitor";

-- DropTable
DROP TABLE "Inmate_Visitor_Documents";

-- DropTable
DROP TABLE "Meeting";

-- DropTable
DROP TABLE "MeetingFeedback";

-- DropTable
DROP TABLE "MeetingRecord";

-- DropTable
DROP TABLE "Notification";

-- DropTable
DROP TABLE "Permission";

-- DropTable
DROP TABLE "Role";

-- DropTable
DROP TABLE "Role_Permission";

-- DropTable
DROP TABLE "Room";

-- DropTable
DROP TABLE "RoomAvaliabilitySettings";

-- DropTable
DROP TABLE "Section";

-- DropTable
DROP TABLE "Section_Availability_Settings";

-- DropTable
DROP TABLE "User";

-- DropTable
DROP TABLE "User_Permission";

-- DropTable
DROP TABLE "Visit_Request";

-- DropTable
DROP TABLE "Visitor";

-- DropEnum
DROP TYPE "AvailabilityType";

-- DropEnum
DROP TYPE "Days";

-- DropEnum
DROP TYPE "InmateVitorRelation";

-- DropEnum
DROP TYPE "Inmate_Status";

-- DropEnum
DROP TYPE "MeetingStatus";

-- DropEnum
DROP TYPE "VISITREQUESTSTATUS";

-- CreateTable
CREATE TABLE "inmates"
(
    "id"                  TEXT            NOT NULL DEFAULT gen_random_uuid(),
    "full_name"           TEXT            NOT NULL,
    "national_id"         TEXT,
    "number_of_visits"    INTEGER         NOT NULL,
    "section_id"          TEXT            NOT NULL,
    "room_id"             TEXT,
    "deleted_at"          TIMESTAMP(3),
    "is_building_default" BOOLEAN,
    "is_room_default"     BOOLEAN,
    "is_deleted"          BOOLEAN,
    "inmate_code"         TEXT,
    "nationality"         TEXT,
    "status"              "inmate_status" NOT NULL,

    CONSTRAINT "inmates_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "visitors"
(
    "id"           TEXT    NOT NULL DEFAULT gen_random_uuid(),
    "full_name"    TEXT    NOT NULL,
    "national_id"  TEXT    NOT NULL,
    "is_deleted"   TIMESTAMP(3),
    "blocked"      BOOLEAN NOT NULL DEFAULT false,
    "phone_number" TEXT,
    "block_reason" TEXT,

    CONSTRAINT "visitors_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "notifications"
(
    "id"         TEXT         NOT NULL DEFAULT gen_random_uuid(),
    "datetime"   TIMESTAMP(3) NOT NULL,
    "visitor_id" TEXT         NOT NULL,
    "visit_id"   TEXT         NOT NULL,
    "message"    TEXT         NOT NULL,
    "seen"       BOOLEAN               DEFAULT false,

    CONSTRAINT "notifications_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "visit_requests"
(
    "id"                    TEXT                   NOT NULL DEFAULT gen_random_uuid(),
    "visitor_id"            TEXT,
    "inmate_id"             TEXT                   NOT NULL,
    "room_id"               TEXT,
    "datetime"              TIMESTAMP(3)           NOT NULL,
    "status"                "visit_request_status" NOT NULL,
    "duration"              INTEGER                NOT NULL,
    "is_deleted"            TIMESTAMP(3),
    "user_id"               TEXT,
    "visit_number"          SERIAL                 NOT NULL,
    "archived"              BOOLEAN                NOT NULL DEFAULT false,
    "meeting_id"            TEXT,
    "meeting_status"        "meeting_status",
    "requested_inmate_name" TEXT,

    CONSTRAINT "visit_requests_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "users"
(
    "id"           TEXT NOT NULL DEFAULT gen_random_uuid(),
    "role_id"      TEXT NOT NULL,
    "full_name"    TEXT,
    "is_deleted"   TIMESTAMP(3),
    "email"        TEXT,
    "password"     TEXT,
    "first_name"   TEXT,
    "last_name"    TEXT,
    "user_auth_id" UUID,

    CONSTRAINT "users_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "roles"
(
    "id"   TEXT NOT NULL DEFAULT gen_random_uuid(),
    "name" TEXT NOT NULL,

    CONSTRAINT "roles_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "permissions"
(
    "id"   TEXT NOT NULL DEFAULT gen_random_uuid(),
    "name" TEXT NOT NULL,

    CONSTRAINT "permissions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "role_permissions"
(
    "id"            TEXT NOT NULL DEFAULT gen_random_uuid(),
    "role_id"       TEXT NOT NULL,
    "permission_id" TEXT NOT NULL,

    CONSTRAINT "role_permissions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "user_permissions"
(
    "id"            TEXT NOT NULL DEFAULT gen_random_uuid(),
    "user_id"       TEXT NOT NULL,
    "permission_id" TEXT NOT NULL,

    CONSTRAINT "user_permissions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "sections"
(
    "id"           TEXT    NOT NULL DEFAULT gen_random_uuid(),
    "name"         TEXT    NOT NULL,
    "is_available" BOOLEAN NOT NULL DEFAULT true,
    "is_deleted"   BOOLEAN          DEFAULT false,
    "deleted_at"   TIMESTAMP(3),

    CONSTRAINT "sections_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "section_availability_settings"
(
    "id"         TEXT NOT NULL DEFAULT gen_random_uuid(),
    "day"        TEXT NOT NULL,
    "section_id" TEXT NOT NULL,

    CONSTRAINT "section_availability_settings_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "availability_times"
(
    "id"             TEXT                NOT NULL DEFAULT gen_random_uuid(),
    "setting_id"     TEXT                NOT NULL,
    "type"           "availability_type" NOT NULL,
    "available_from" TIME(6)             NOT NULL,
    "available_to"   TIME(6)             NOT NULL,

    CONSTRAINT "availability_times_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "inmate_visitors"
(
    "id"            TEXT NOT NULL DEFAULT gen_random_uuid(),
    "inmate_id"     TEXT NOT NULL,
    "visitor_id"    TEXT NOT NULL,
    "is_white_list" BOOLEAN,
    "other"         TEXT,
    "relation"      "inmate_visitor_relation",
    "is_deleted"    TIMESTAMP(3),

    CONSTRAINT "inmate_visitors_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "inmate_visitor_documents"
(
    "id"                TEXT NOT NULL DEFAULT gen_random_uuid(),
    "inmate_visitor_id" TEXT NOT NULL,
    "document_url"      TEXT NOT NULL,
    "document_name"     TEXT NOT NULL,
    "document_size"     TEXT NOT NULL,

    CONSTRAINT "inmate_visitor_documents_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "rooms"
(
    "id"                  TEXT    NOT NULL DEFAULT gen_random_uuid(),
    "name"                TEXT    NOT NULL,
    "active"              BOOLEAN NOT NULL,
    "section_id"          TEXT    NOT NULL,
    "is_building_default" BOOLEAN,
    "deleted_at"          TIMESTAMP(3),
    "is_deleted"          BOOLEAN,

    CONSTRAINT "rooms_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "room_availability_settings"
(
    "id"           TEXT    NOT NULL DEFAULT gen_random_uuid(),
    "day"          "days"  NOT NULL,
    "room_id"      TEXT    NOT NULL,
    "is_available" BOOLEAN NOT NULL DEFAULT true,

    CONSTRAINT "room_availability_settings_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "inmate_availability_settings"
(
    "id"        TEXT   NOT NULL DEFAULT gen_random_uuid(),
    "day"       "days" NOT NULL,
    "inmate_id" TEXT   NOT NULL,

    CONSTRAINT "inmate_availability_settings_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "meetings"
(
    "id"                TEXT             NOT NULL DEFAULT gen_random_uuid(),
    "title"             TEXT             NOT NULL,
    "status"            "meeting_status" NOT NULL,
    "duration"          INTEGER          NOT NULL,
    "started_at"        TIMESTAMP(3),
    "ended_at"          TIMESTAMP(3),
    "meeting_date_time" TIMESTAMP(3),
    "room_id"           TEXT,

    CONSTRAINT "meetings_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "meeting_records"
(
    "id"            TEXT NOT NULL DEFAULT gen_random_uuid(),
    "meeting_id"    TEXT NOT NULL,
    "session_id"    TEXT          DEFAULT gen_random_uuid(),
    "recording_url" TEXT,
    "record_size"   INTEGER,
    "duration"      INTEGER,
    "created_at"    TIMESTAMP(3),
    "deleted_at"    TIMESTAMP(3),
    "archived"      BOOLEAN       DEFAULT false,

    CONSTRAINT "meeting_records_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "meeting_feedbacks"
(
    "id"         TEXT    NOT NULL DEFAULT gen_random_uuid(),
    "meeting_id" TEXT    NOT NULL,
    "visitor_id" TEXT    NOT NULL,
    "comment"    TEXT,
    "rate"       INTEGER NOT NULL,

    CONSTRAINT "meeting_feedbacks_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "inmate_settings"
(
    "id"        TEXT NOT NULL DEFAULT gen_random_uuid(),
    "inmate_id" TEXT NOT NULL,
    "key"       TEXT NOT NULL,
    "value"     TEXT NOT NULL,

    CONSTRAINT "inmate_settings_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "inmates_inmate_code_key" ON "inmates" ("inmate_code");

-- CreateIndex
CREATE INDEX "idx_inmates_national_id" ON "inmates" USING HASH ("national_id");

-- CreateIndex
CREATE UNIQUE INDEX "visitors_national_id_key" ON "visitors" ("national_id");

-- CreateIndex
CREATE UNIQUE INDEX "users_email_key" ON "users" ("email");

-- CreateIndex
CREATE UNIQUE INDEX "unique_role_name" ON "roles" ("name");

-- CreateIndex
CREATE UNIQUE INDEX "unique_permission_name" ON "permissions" ("name");

-- CreateIndex
CREATE UNIQUE INDEX "unique_role_permission" ON "role_permissions" ("role_id", "permission_id");

-- CreateIndex
CREATE UNIQUE INDEX "inmate_settings_inmate_id_key_key" ON "inmate_settings" ("inmate_id", "key");

-- AddForeignKey
ALTER TABLE "inmates"
    ADD CONSTRAINT "inmates_room_id_fkey" FOREIGN KEY ("room_id") REFERENCES "rooms" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "inmates"
    ADD CONSTRAINT "inmates_section_id_fkey" FOREIGN KEY ("section_id") REFERENCES "sections" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "notifications"
    ADD CONSTRAINT "notifications_visit_id_fkey" FOREIGN KEY ("visit_id") REFERENCES "visit_requests" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "notifications"
    ADD CONSTRAINT "notifications_visitor_id_fkey" FOREIGN KEY ("visitor_id") REFERENCES "visitors" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "visit_requests"
    ADD CONSTRAINT "visit_requests_inmate_id_fkey" FOREIGN KEY ("inmate_id") REFERENCES "inmates" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "visit_requests"
    ADD CONSTRAINT "visit_requests_meeting_id_fkey" FOREIGN KEY ("meeting_id") REFERENCES "meetings" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "visit_requests"
    ADD CONSTRAINT "visit_requests_room_id_fkey" FOREIGN KEY ("room_id") REFERENCES "rooms" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "visit_requests"
    ADD CONSTRAINT "visit_requests_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "visit_requests"
    ADD CONSTRAINT "visit_requests_visitor_id_fkey" FOREIGN KEY ("visitor_id") REFERENCES "visitors" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "users"
    ADD CONSTRAINT "users_role_id_fkey" FOREIGN KEY ("role_id") REFERENCES "roles" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "role_permissions"
    ADD CONSTRAINT "role_permissions_permission_id_fkey" FOREIGN KEY ("permission_id") REFERENCES "permissions" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "role_permissions"
    ADD CONSTRAINT "role_permissions_role_id_fkey" FOREIGN KEY ("role_id") REFERENCES "roles" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_permissions"
    ADD CONSTRAINT "user_permissions_permission_id_fkey" FOREIGN KEY ("permission_id") REFERENCES "permissions" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_permissions"
    ADD CONSTRAINT "user_permissions_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "section_availability_settings"
    ADD CONSTRAINT "section_availability_settings_section_id_fkey" FOREIGN KEY ("section_id") REFERENCES "sections" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "inmate_visitors"
    ADD CONSTRAINT "inmate_visitors_inmate_id_fkey" FOREIGN KEY ("inmate_id") REFERENCES "inmates" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "inmate_visitors"
    ADD CONSTRAINT "inmate_visitors_visitor_id_fkey" FOREIGN KEY ("visitor_id") REFERENCES "visitors" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "inmate_visitor_documents"
    ADD CONSTRAINT "inmate_visitor_documents_inmate_visitor_id_fkey" FOREIGN KEY ("inmate_visitor_id") REFERENCES "inmate_visitors" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "rooms"
    ADD CONSTRAINT "rooms_section_id_fkey" FOREIGN KEY ("section_id") REFERENCES "sections" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "room_availability_settings"
    ADD CONSTRAINT "room_availability_settings_room_id_fkey" FOREIGN KEY ("room_id") REFERENCES "rooms" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "inmate_availability_settings"
    ADD CONSTRAINT "inmate_availability_settings_inmate_id_fkey" FOREIGN KEY ("inmate_id") REFERENCES "inmates" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "meetings"
    ADD CONSTRAINT "meetings_room_id_fkey" FOREIGN KEY ("room_id") REFERENCES "rooms" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "meeting_records"
    ADD CONSTRAINT "meeting_records_meeting_id_fkey" FOREIGN KEY ("meeting_id") REFERENCES "meetings" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "meeting_feedbacks"
    ADD CONSTRAINT "meeting_feedbacks_meeting_id_fkey" FOREIGN KEY ("meeting_id") REFERENCES "meetings" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "meeting_feedbacks"
    ADD CONSTRAINT "meeting_feedbacks_visitor_id_fkey" FOREIGN KEY ("visitor_id") REFERENCES "visitors" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "inmate_settings"
    ADD CONSTRAINT "inmate_settings_inmate_id_fkey" FOREIGN KEY ("inmate_id") REFERENCES "inmates" ("id") ON DELETE CASCADE ON UPDATE CASCADE;
