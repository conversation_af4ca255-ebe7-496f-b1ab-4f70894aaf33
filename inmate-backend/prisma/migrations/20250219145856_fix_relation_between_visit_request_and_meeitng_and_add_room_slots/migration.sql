/*
  Warnings:

  - You are about to drop the column `room_id` on the `meetings` table. All the data in the column will be lost.
  - You are about to drop the column `meeting_id` on the `visit_requests` table. All the data in the column will be lost.
  - You are about to drop the column `room_id` on the `visit_requests` table. All the data in the column will be lost.

*/
-- DropForeignKey
ALTER TABLE "meetings" DROP CONSTRAINT "meetings_room_id_fkey";

-- DropForeignKey
ALTER TABLE "visit_requests" DROP CONSTRAINT "visit_requests_meeting_id_fkey";

-- DropForeignKey
ALTER TABLE "visit_requests" DROP CONSTRAINT "visit_requests_room_id_fkey";

-- AlterTable
ALTER TABLE "meetings" DROP COLUMN "room_id",
ADD COLUMN     "visit_request_id" TEXT;

-- AlterTable
ALTER TABLE "visit_requests" DROP COLUMN "meeting_id",
DROP
COLUMN "room_id";

-- CreateTable
CREATE TABLE "room_slots"
(
    "id"         TEXT         NOT NULL DEFAULT gen_random_uuid(),
    "room_id"    TEXT         NOT NULL,
    "meeting_id" TEXT         NOT NULL,
    "slot_start" TIMESTAMP(3) NOT NULL,
    "slot_end"   TIMESTAMP(3) NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "room_slots_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "meetings"
    ADD CONSTRAINT "meetings_visit_request_id_fkey" FOREIGN KEY ("visit_request_id") REFERENCES "visit_requests" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "room_slots"
    ADD CONSTRAINT "room_slots_room_id_fkey" FOREIGN KEY ("room_id") REFERENCES "rooms" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "room_slots"
    ADD CONSTRAINT "room_slots_meeting_id_fkey" FOREIGN KEY ("meeting_id") REFERENCES "meetings" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;
