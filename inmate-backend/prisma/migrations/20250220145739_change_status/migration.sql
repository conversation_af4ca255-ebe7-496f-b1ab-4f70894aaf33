/*
  Warnings:

  - You are about to drop the column `meeting_status` on the `visit_requests` table. All the data in the column will be lost.
  - You are about to drop the column `status` on the `visit_requests` table. All the data in the column will be lost.
  - Added the required column `status_id` to the `visit_requests` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "visit_requests" DROP COLUMN "meeting_status",
DROP COLUMN "status",
ADD COLUMN     "status_id" INTEGER NOT NULL;

-- DropEnum
DROP TYPE "visit_request_status";

-- CreateTable
CREATE TABLE "visit_request_status" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,

    CONSTRAINT "visit_request_status_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "visit_request_status_name_key" ON "visit_request_status"("name");

-- AddF<PERSON>ign<PERSON>ey
ALTER TABLE "visit_requests" ADD CONSTRAINT "visit_requests_status_id_fkey" FOREIGN KEY ("status_id") REFERENCES "visit_request_status"("id") ON DELETE CASCADE ON UPDATE CASCADE;


INSERT INTO public."visit_request_status" (name)
VALUES
    ('Pending'),
    ('Approved'),
    ('Rejected'),
    ('Completed'),
    ('In Progress');
