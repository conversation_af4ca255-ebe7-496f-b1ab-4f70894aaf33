/*
  Warnings:

  - You are about to drop the column `status_id` on the `visit_requests` table. All the data in the column will be lost.
  - You are about to drop the `visit_request_status` table. If the table is not empty, all the data it contains will be lost.
  - Changed the type of `status` on the `inmates` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `status` on the `meetings` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Added the required column `status` to the `visit_requests` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE "visit_requests" DROP CONSTRAINT "visit_requests_status_id_fkey";

-- AlterTable
ALTER TABLE "inmates" DROP COLUMN "status",
ADD COLUMN     "status" TEXT NOT NULL;

-- AlterTable
ALTER TABLE "meetings" DROP COLUMN "status",
ADD COLUMN     "status" TEXT NOT NULL;

-- AlterTable
ALTER TABLE "visit_requests" DROP COLUMN "status_id",
ADD COLUMN     "status" TEXT NOT NULL;

-- DropTable
DROP TABLE "visit_request_status";

-- CreateTable
CREATE TABLE "status" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,

    CONSTRAINT "status_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "status_name_key" ON "status"("name");

-- AddForeignKey
ALTER TABLE "inmates" ADD CONSTRAINT "inmates_status_fkey" FOREIGN KEY ("status") REFERENCES "status"("name") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "visit_requests" ADD CONSTRAINT "visit_requests_status_fkey" FOREIGN KEY ("status") REFERENCES "status"("name") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "meetings" ADD CONSTRAINT "meetings_status_fkey" FOREIGN KEY ("status") REFERENCES "status"("name") ON DELETE CASCADE ON UPDATE CASCADE;



INSERT INTO "status" ("name") VALUES
                                  ('pending_approval'),
                                  ('scheduled'),
                                  ('starting_soon'),
                                  ('awaiting_admin'),
                                  ('running'),
                                  ('completed'),
                                  ('missed'),
                                  ('admin_cancelled'),
                                  ('visitor_cancelled'),
                                  ('denied');

ALTER FUNCTION get_visitor_requests_with_meeting_records_not_archived() OWNER TO postgres;
ALTER FUNCTION get_visitor_requests_with_meeting_records() OWNER TO postgres;

GRANT ALL PRIVILEGES ON FUNCTION get_visitor_requests_with_meeting_records_not_archived() TO postgres;
GRANT ALL PRIVILEGES ON FUNCTION get_visitor_requests_with_meeting_records() TO postgres;
DROP FUNCTION get_visitor_requests_with_meeting_records_not_archived();
DROP FUNCTION get_visitor_requests_with_meeting_records();

CREATE OR REPLACE FUNCTION get_visitor_requests_with_meeting_records_not_archived()
RETURNS TABLE(
    request_id TEXT,
    request_status TEXT,
    request_number TEXT,
    inmate_full_name TEXT,
    inmate_id TEXT,
    visitor_name TEXT,
    request_date TIMESTAMP,
    relation TEXT,
    meeting_status TEXT,
    meeting_date_time TIMESTAMP,
    record_id TEXT,
    recording_url TEXT,
    record_duration INT,
    archived BOOLEAN,
    record_size INT,
    feedback_rate INT,
    feedback_comment TEXT

) AS $$
BEGIN
RETURN QUERY
SELECT DISTINCT ON (mrec.id)
    vr.id,
    vr.status_id::TEXT,
    vr.visit_number::TEXT,
    inm.full_name AS inmate_full_name,
    vr.inmate_id,
    vis.full_name AS visitor_name,
    vr.datetime AS request_date,
    iv.relation::TEXT,
    mtg.status_id::TEXT AS meeting_status,
    mtg.meeting_date_time,
    mrec.id,
    mrec.recording_url,
    mrec.duration AS record_duration,
    mrec.archived,
    mrec.record_size,
    feedback.rate,
    feedback.comment
FROM public.visit_requests vr
    LEFT JOIN public.inmate_visitors iv
ON vr.inmate_id = iv.inmate_id
    AND vr.visitor_id = iv.visitor_id
    LEFT JOIN public.inmates inm
    ON inm.id = vr.inmate_id
    JOIN public.visitors vis
    ON vis.id = vr.visitor_id
    LEFT JOIN public.meetings mtg
    ON mtg.visit_request_id = vr.id
    JOIN public.meeting_records mrec
    ON mrec.meeting_id = mtg.id
    LEFT JOIN public.meeting_feedbacks feedback
    ON mrec.meeting_id = feedback.meeting_id
WHERE mrec.deleted_at IS NULL
  AND mrec.archived = FALSE;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION get_visitor_requests_with_meeting_records()
RETURNS TABLE(
    request_id TEXT,
    request_status TEXT,
    request_number TEXT,
    inmate_full_name TEXT,
    inmate_id TEXT,
    visitor_name TEXT,
    request_date TIMESTAMP,
    relation TEXT,
    meeting_status TEXT,
    meeting_date_time TIMESTAMP,
    record_id TEXT,
    recording_url TEXT,
    record_duration INT,
    archived BOOLEAN,
    record_size INT,
    feedback_rate INT,
    feedback_comment TEXT

) AS $$
BEGIN
RETURN QUERY
SELECT DISTINCT ON (mrec.id)
    vr.id,
    vr.status_id::TEXT,
    vr.visit_number::TEXT,
    inm.full_name AS inmate_full_name,
    vr.inmate_id,
    vis.full_name AS visitor_name,
    vr.datetime AS request_date,
    iv.relation::TEXT,
    mtg.status_id::TEXT AS meeting_status,
    mtg.meeting_date_time,
    mrec.id,
    mrec.recording_url,
    mrec.duration AS record_duration,
    mrec.archived,
    mrec.record_size,
    feedback.rate,
    feedback.comment
FROM public.visit_requests vr
    LEFT JOIN public.inmate_visitors iv
ON vr.inmate_id = iv.inmate_id
    AND vr.visitor_id = iv.visitor_id
    LEFT JOIN public.inmates inm
    ON inm.id = vr.inmate_id
    JOIN public.visitors vis
    ON vis.id = vr.visitor_id
    LEFT JOIN public.meetings mtg
    ON mtg.visit_request_id = vr.id
    JOIN public.meeting_records mrec
    ON mrec.meeting_id = mtg.id
    LEFT JOIN public.meeting_feedbacks feedback
    ON mrec.meeting_id = feedback.meeting_id
WHERE mrec.deleted_at IS NULL
  AND mrec.archived = TRUE;
END;
$$ LANGUAGE plpgsql;


CREATE OR REPLACE FUNCTION get_admin_requests_with_meeting_records()
RETURNS TABLE(
    request_id TEXT,
    request_status TEXT,
    request_number TEXT,
    inmate_full_name TEXT,
    inmate_id TEXT,
    admin_id TEXT,
    admin_name TEXT,
    request_date TIMESTAMP,
    meeting_status TEXT,
    meeting_date_time TIMESTAMP,
    record_id TEXT,
    recording_url TEXT,
    record_duration INT,
    archived BOOLEAN,
    record_size INT,
    feedback_rate INT,
    feedback_comment TEXT

) AS $$
BEGIN
RETURN QUERY
SELECT DISTINCT ON (vr.id)
    vr.id,
    vr.status::TEXT,
    vr.visit_number::TEXT,
    inm.full_name AS inmate_full_name,
    vr.inmate_id,
    admin.id as admin_id,
    admin.full_name AS admin_name,
    vr.datetime AS request_date,
    mtg.status::TEXT AS meeting_status,
    mtg.meeting_date_time,
    mrec.id,
    mrec.recording_url,
    mrec.duration AS record_duration,
    mrec.archived,
    mrec.record_size,
    feedback.rate,
    feedback.comment
FROM public."visit_requests" vr

    LEFT JOIN public."inmates" inm ON inm.id = vr.inmate_id
    JOIN public."users" admin ON admin.id = vr.user_id
    LEFT JOIN public."meetings" mtg ON mtg.id = vr.meeting_id
    JOIN public."meeting_records" mrec ON mrec.meeting_id = mtg.id
    LEFT JOIN public."meeting_feedbacks" feedback ON mrec.meeting_id = feedback.meeting_id
WHERE mrec."deleted_at" IS NULL AND mrec.archived = TRUE AND vr.visitor_id IS NULL;
END;
$$ LANGUAGE plpgsql;







CREATE OR REPLACE FUNCTION get_admin_requests_with_meeting_records_not_archived()
RETURNS TABLE(
    request_id TEXT,
    request_status TEXT,
    request_number TEXT,
    inmate_full_name TEXT,
    inmate_id TEXT,
    admin_id TEXT,
    admin_name TEXT,
    request_date TIMESTAMP,
    meeting_status TEXT,
    meeting_date_time TIMESTAMP,
    record_id TEXT,
    recording_url TEXT,
    record_duration INT,
    archived BOOLEAN,
    record_size INT,
    feedback_rate INT,
    feedback_comment TEXT

) AS $$
BEGIN
RETURN QUERY
SELECT DISTINCT ON (vr.id)
    vr.id,
    vr.status::TEXT,
    vr.visit_number::TEXT,
    inm.full_name AS inmate_full_name,
    vr.inmate_id,
    admin.id as admin_id,
    admin.full_name AS admin_name,
    vr.datetime AS request_date,
    mtg.status::TEXT AS meeting_status,
    mtg.meeting_date_time,
    mrec.id,
    mrec.recording_url,
    mrec.duration AS record_duration,
    mrec.archived,
    mrec.record_size,
    feedback.rate,
    feedback.comment
FROM public."visit_requests" vr

    LEFT JOIN public."inmates" inm ON inm.id = vr.inmate_id
    JOIN public."users" admin ON admin.id = vr.user_id
    LEFT JOIN public."meetings" mtg ON mtg.id = vr.meeting_id
    JOIN public."meeting_records" mrec ON mrec.meeting_id = mtg.id
    LEFT JOIN public."meeting_feedbacks" feedback ON mrec.meeting_id = feedback.meeting_id
WHERE mrec."deleted_at" IS NULL AND mrec.archived = FALSE AND vr.visitor_id IS NULL;
END;
$$ LANGUAGE plpgsql;


CREATE OR REPLACE FUNCTION create_meeting_on_approved()
RETURNS TRIGGER AS $$
BEGIN
    -- Check if the status is changing to 'APPROVED'
    IF NEW.status_id = '2' THEN
        -- Insert a new meeting record with room_id from Visit_Request
        INSERT INTO public."meetings" (
            id,
            title,
            status_id,
            meeting_date_time,
            duration,
            visit_request_id
        )
        VALUES (
            gen_random_uuid(),
            'Meeting for visit request number ' || NEW.visit_number,
            '1', -- Assuming the meeting starts at this status
            NEW.datetime, -- Set meeting date time to the visit request's datetime
            NEW.duration, -- Set the duration of the meeting based on visit request
            NEW.id -- Assign the room_id from Visit_Request
        );
END IF;


    -- Return the updated visit request record
RETURN NEW;
END;

$$ LANGUAGE plpgsql;

-- CREATE OR REPLACE TRIGGER trigger_create_meeting_on_approved
-- AFTER INSERT OR UPDATE OF status
--                 ON public."visit_requests"
--                     FOR EACH ROW
--                     WHEN (NEW.status = 'approved')
--                     EXECUTE FUNCTION create_meeting_on_approved();
--
--
--
CREATE OR REPLACE FUNCTION delete_user_from_public()
RETURNS TRIGGER AS $$
BEGIN
  -- Delete the user from public.users where the email matches
DELETE FROM public."users" WHERE email = OLD.email;
RETURN OLD;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- CREATE TRIGGER on_auth_user_delete
--     AFTER DELETE ON auth.users
--     FOR EACH ROW
--     EXECUTE FUNCTION delete_user_from_public();


GRANT DELETE , SELECT ON TABLE public."users" TO authenticator;
GRANT DELETE , SELECT ON TABLE public."users" TO service_role;
GRANT EXECUTE ON FUNCTION delete_user_from_public() TO authenticator;
GRANT EXECUTE ON FUNCTION delete_user_from_public() TO service_role;
-- GRANT DELETE, SELECT ON public.users TO service_role;
GRANT EXECUTE ON FUNCTION delete_user_from_public() TO service_role;


grant usage on schema "public" to anon;
grant usage on schema "public" to authenticated;
GRANT SELECT, INSERT, UPDATE , DELETE ON ALL TABLES IN SCHEMA "public" TO authenticated;
GRANT SELECT, INSERT, UPDATE , DELETE ON ALL TABLES IN SCHEMA "public" TO anon;