/*
  Warnings:

  - You are about to drop the column `status` on the `inmates` table. All the data in the column will be lost.
  - You are about to drop the column `status` on the `meetings` table. All the data in the column will be lost.
  - You are about to drop the column `meeting_id` on the `room_slots` table. All the data in the column will be lost.
  - You are about to drop the column `status` on the `visit_requests` table. All the data in the column will be lost.
  - You are about to drop the `status` table. If the table is not empty, all the data it contains will be lost.
  - Added the required column `visit_request_id` to the `room_slots` table without a default value. This is not possible if the table is not empty.
  - Changed the type of `day` on the `section_availability_settings` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Added the required column `status_id` to the `visit_requests` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE "inmate_settings" DROP CONSTRAINT "inmate_settings_inmate_id_fkey";

-- DropForeignKey
ALTER TABLE "inmate_visitors" DROP CONSTRAINT "inmate_visitors_inmate_id_fkey";

-- DropForeignKey
ALTER TABLE "inmate_visitors" DROP CONSTRAINT "inmate_visitors_visitor_id_fkey";

-- DropForeignKey
ALTER TABLE "inmates" DROP CONSTRAINT "inmates_room_id_fkey";

-- DropForeignKey
ALTER TABLE "inmates" DROP CONSTRAINT "inmates_section_id_fkey";

-- DropForeignKey
ALTER TABLE "inmates" DROP CONSTRAINT "inmates_status_fkey";

-- DropForeignKey
ALTER TABLE "meeting_feedbacks" DROP CONSTRAINT "meeting_feedbacks_meeting_id_fkey";

-- DropForeignKey
ALTER TABLE "meeting_feedbacks" DROP CONSTRAINT "meeting_feedbacks_visitor_id_fkey";

-- DropForeignKey
ALTER TABLE "meeting_records" DROP CONSTRAINT "meeting_records_meeting_id_fkey";

-- DropForeignKey
ALTER TABLE "meetings" DROP CONSTRAINT "meetings_status_fkey";

-- DropForeignKey
ALTER TABLE "notifications" DROP CONSTRAINT "notifications_visit_id_fkey";

-- DropForeignKey
ALTER TABLE "notifications" DROP CONSTRAINT "notifications_visitor_id_fkey";

-- DropForeignKey
ALTER TABLE "role_permissions" DROP CONSTRAINT "role_permissions_permission_id_fkey";

-- DropForeignKey
ALTER TABLE "role_permissions" DROP CONSTRAINT "role_permissions_role_id_fkey";

-- DropForeignKey
ALTER TABLE "room_slots" DROP CONSTRAINT "room_slots_meeting_id_fkey";

-- DropForeignKey
ALTER TABLE "user_permissions" DROP CONSTRAINT "user_permissions_permission_id_fkey";

-- DropForeignKey
ALTER TABLE "user_permissions" DROP CONSTRAINT "user_permissions_user_id_fkey";

-- DropForeignKey
ALTER TABLE "users" DROP CONSTRAINT "users_role_id_fkey";

-- DropForeignKey
ALTER TABLE "visit_requests" DROP CONSTRAINT "visit_requests_inmate_id_fkey";

-- DropForeignKey
ALTER TABLE "visit_requests" DROP CONSTRAINT "visit_requests_status_fkey";

-- DropForeignKey
ALTER TABLE "visit_requests" DROP CONSTRAINT "visit_requests_user_id_fkey";

-- DropForeignKey
ALTER TABLE "visit_requests" DROP CONSTRAINT "visit_requests_visitor_id_fkey";

-- AlterTable
ALTER TABLE "inmates" DROP COLUMN "status",
ADD COLUMN     "status_id" INTEGER NOT NULL DEFAULT 1;

-- AlterTable
ALTER TABLE "meetings" DROP COLUMN "status",
ADD COLUMN     "status_id" INTEGER NOT NULL DEFAULT 1;

-- AlterTable
ALTER TABLE "room_slots" DROP COLUMN "meeting_id",
ADD COLUMN     "visit_request_id" TEXT NOT NULL;

-- AlterTable
ALTER TABLE "section_availability_settings" DROP COLUMN "day",
ADD COLUMN     "day" "days" NOT NULL;

-- AlterTable
ALTER TABLE "visit_requests" DROP COLUMN "status",
ADD COLUMN     "from" TIMETZ(6),
ADD COLUMN     "status_id" INTEGER NOT NULL,
ADD COLUMN     "to" TIMETZ(6),
ALTER COLUMN "datetime" SET DATA TYPE TIMESTAMPTZ(6);

-- DropTable
DROP TABLE "status";

-- DropEnum
DROP TYPE "meeting_status";

-- CreateTable
CREATE TABLE "visit_requests_status" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,

    CONSTRAINT "visit_requests_status_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "inmates_status" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,

    CONSTRAINT "inmates_status_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "meeting_status" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,

    CONSTRAINT "meeting_status_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "visit_requests_status_name_key" ON "visit_requests_status"("name");

-- CreateIndex
CREATE UNIQUE INDEX "inmates_status_name_key" ON "inmates_status"("name");

-- CreateIndex
CREATE UNIQUE INDEX "meeting_status_name_key" ON "meeting_status"("name");

-- AddForeignKey
ALTER TABLE "inmates" ADD CONSTRAINT "inmates_room_id_fkey" FOREIGN KEY ("room_id") REFERENCES "rooms"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "inmates" ADD CONSTRAINT "inmates_section_id_fkey" FOREIGN KEY ("section_id") REFERENCES "sections"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "inmates" ADD CONSTRAINT "inmates_status_id_fkey" FOREIGN KEY ("status_id") REFERENCES "inmates_status"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "notifications" ADD CONSTRAINT "notifications_visit_id_fkey" FOREIGN KEY ("visit_id") REFERENCES "visit_requests"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "notifications" ADD CONSTRAINT "notifications_visitor_id_fkey" FOREIGN KEY ("visitor_id") REFERENCES "visitors"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "visit_requests" ADD CONSTRAINT "visit_requests_inmate_id_fkey" FOREIGN KEY ("inmate_id") REFERENCES "inmates"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "visit_requests" ADD CONSTRAINT "visit_requests_status_id_fkey" FOREIGN KEY ("status_id") REFERENCES "visit_requests_status"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "visit_requests" ADD CONSTRAINT "visit_requests_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "visit_requests" ADD CONSTRAINT "visit_requests_visitor_id_fkey" FOREIGN KEY ("visitor_id") REFERENCES "visitors"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "users" ADD CONSTRAINT "users_role_id_fkey" FOREIGN KEY ("role_id") REFERENCES "roles"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "role_permissions" ADD CONSTRAINT "role_permissions_permission_id_fkey" FOREIGN KEY ("permission_id") REFERENCES "permissions"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "role_permissions" ADD CONSTRAINT "role_permissions_role_id_fkey" FOREIGN KEY ("role_id") REFERENCES "roles"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_permissions" ADD CONSTRAINT "user_permissions_permission_id_fkey" FOREIGN KEY ("permission_id") REFERENCES "permissions"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_permissions" ADD CONSTRAINT "user_permissions_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "inmate_visitors" ADD CONSTRAINT "inmate_visitors_inmate_id_fkey" FOREIGN KEY ("inmate_id") REFERENCES "inmates"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "inmate_visitors" ADD CONSTRAINT "inmate_visitors_visitor_id_fkey" FOREIGN KEY ("visitor_id") REFERENCES "visitors"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "meetings" ADD CONSTRAINT "meetings_status_id_fkey" FOREIGN KEY ("status_id") REFERENCES "meeting_status"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "meeting_records" ADD CONSTRAINT "meeting_records_meeting_id_fkey" FOREIGN KEY ("meeting_id") REFERENCES "meetings"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "meeting_feedbacks" ADD CONSTRAINT "meeting_feedbacks_meeting_id_fkey" FOREIGN KEY ("meeting_id") REFERENCES "meetings"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "meeting_feedbacks" ADD CONSTRAINT "meeting_feedbacks_visitor_id_fkey" FOREIGN KEY ("visitor_id") REFERENCES "visitors"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "room_slots" ADD CONSTRAINT "room_slots_visit_request_id_fkey" FOREIGN KEY ("visit_request_id") REFERENCES "visit_requests"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "inmate_settings" ADD CONSTRAINT "inmate_settings_inmate_id_fkey" FOREIGN KEY ("inmate_id") REFERENCES "inmates"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
