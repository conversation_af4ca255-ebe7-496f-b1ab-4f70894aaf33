/*
  Warnings:

  - You are about to drop the column `meeting_id` on the `meeting_feedbacks` table. All the data in the column will be lost.
  - You are about to drop the column `meeting_id` on the `meeting_records` table. All the data in the column will be lost.
  - You are about to drop the `meeting_status` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `meetings` table. If the table is not empty, all the data it contains will be lost.
  - Added the required column `visit_request_id` to the `meeting_feedbacks` table without a default value. This is not possible if the table is not empty.
  - Added the required column `visit_request_id` to the `meeting_records` table without a default value. This is not possible if the table is not empty.
  - Added the required column `actual_duration` to the `visit_requests` table without a default value. This is not possible if the table is not empty.
  - Added the required column `title` to the `visit_requests` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE "meeting_feedbacks" DROP CONSTRAINT "meeting_feedbacks_meeting_id_fkey";

-- DropForeignKey
ALTER TABLE "meeting_records" DROP CONSTRAINT "meeting_records_meeting_id_fkey";

-- DropForeignKey
ALTER TABLE "meetings" DROP CONSTRAINT "meetings_status_id_fkey";

-- DropForeignKey
ALTER TABLE "meetings" DROP CONSTRAINT "meetings_visit_request_id_fkey";

-- AlterTable
ALTER TABLE "meeting_feedbacks" DROP COLUMN "meeting_id",
ADD COLUMN     "visit_request_id" TEXT NOT NULL;

-- AlterTable
ALTER TABLE "meeting_records" DROP COLUMN "meeting_id",
ADD COLUMN     "visit_request_id" TEXT NOT NULL;

-- AlterTable
ALTER TABLE "visit_requests" ADD COLUMN     "actual_duration" INTEGER,
ADD COLUMN     "ended_at" TIMESTAMP(3),
ADD COLUMN     "reason" TEXT,
ADD COLUMN     "started_at" TIMESTAMP(3),
ADD COLUMN     "title" TEXT ;

-- DropTable
DROP TABLE "meeting_status";

-- DropTable
DROP TABLE "meetings";

-- AddForeignKey
ALTER TABLE "meeting_records" ADD CONSTRAINT "meeting_records_visit_request_id_fkey" FOREIGN KEY ("visit_request_id") REFERENCES "visit_requests"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "meeting_feedbacks" ADD CONSTRAINT "meeting_feedbacks_visit_request_id_fkey" FOREIGN KEY ("visit_request_id") REFERENCES "visit_requests"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- insert status data
-- INSERT INTO "public"."visit_requests_status" VALUES ('1', 'pending_approval'), ('2', 'scheduled'), ('3', 'starting_soon'), ('4', 'awaiting_admin'), ('5', 'running'), ('6', 'completed'), ('7', 'missed'), ('8', 'admin_cancelled'), ('9', 'visitor_cancelled'), ('10', 'denied'),('11', 'started'),('12', 'not_started'),('13', 'not_started'),('14', 'ended');


-- Create the trigger function
-- Drop the trigger if it exists
DROP TRIGGER IF EXISTS visit_request_status_change ON visit_requests;

-- Drop the function if it exists
DROP FUNCTION IF EXISTS notify_on_visit_request_change();

CREATE OR REPLACE FUNCTION notify_visit_status_change()
RETURNS TRIGGER AS $$
DECLARE
status_name TEXT;
BEGIN
    -- Get the name of the status
SELECT name INTO status_name FROM visit_requests_status WHERE id = NEW.status_id;

-- Insert a notification only if the status_id has changed
IF OLD.status_id IS DISTINCT FROM NEW.status_id THEN
        INSERT INTO notifications (id, datetime, visitor_id, visit_id, message, seen)
        VALUES (
            gen_random_uuid(),
            NOW(),
            NEW.visitor_id,
            NEW.id,
            status_name,
            FALSE
        );
END IF;

RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger
CREATE TRIGGER trigger_visit_status_change
    AFTER UPDATE ON visit_requests
    FOR EACH ROW
    WHEN (OLD.status_id IS DISTINCT FROM NEW.status_id)
EXECUTE FUNCTION notify_visit_status_change();


DROP FUNCTION get_visitor_requests_with_meeting_records();
CREATE OR REPLACE FUNCTION public.get_visitor_requests_with_meeting_records()
RETURNS TABLE (
request_id text,
request_status text,
request_number text,
inmate_full_name text,
inmate_id text,
visitor_name text,
request_date timestamp with time zone,
"from" time with time zone,
"to" time with time zone,
relation text,
meeting_status text,
meeting_date_time timestamp with time zone,
record_id text,
recording_url text,
record_duration integer,
archived boolean,
record_size integer,
feedback_rate integer,
feedback_comment text

)
LANGUAGE plpgsql
AS $function$
BEGIN
RETURN QUERY
SELECT DISTINCT ON (mrec.id)
    vr.id,
    vr.status_id::TEXT,
    vr.visit_number::TEXT,
    inm.full_name AS inmate_full_name,
    vr.inmate_id,
    vis.full_name AS visitor_name,
    vr.datetime::timestamptz AS request_date,
    vr."from"::timetz AS "from", -- New field
    vr."to"::timetz AS "to", -- New field
    iv.relation::TEXT,
    mtg.status_id::TEXT AS meeting_status,
    mtg.meeting_date_time::timestamptz AS meeting_date_time,
    mrec.id,
    mrec.recording_url,
    mrec.duration AS record_duration,
    mrec.archived,
    mrec.record_size,
    feedback.rate,
    feedback.comment
FROM public.visit_requests vr
    LEFT JOIN public.inmate_visitors iv
ON vr.inmate_id = iv.inmate_id
    AND vr.visitor_id = iv.visitor_id
    LEFT JOIN public.inmates inm
    ON inm.id = vr.inmate_id
    JOIN public.visitors vis
    ON vis.id = vr.visitor_id
    LEFT JOIN public.meetings mtg
    ON mtg.visit_request_id = vr.id
    JOIN public.meeting_records mrec
    ON mrec.meeting_id = mtg.id
    LEFT JOIN public.meeting_feedbacks feedback
    ON mrec.meeting_id = feedback.meeting_id
WHERE mrec.deleted_at IS NULL
  AND mrec.archived = TRUE;
END;
$function$;
DROP FUNCTION get_visitor_requests_with_meeting_records_not_archived();
CREATE OR REPLACE FUNCTION public.get_visitor_requests_with_meeting_records_not_archived()
RETURNS TABLE (
request_id text,
request_status text,
request_number text,
inmate_full_name text,
inmate_id text,
visitor_name text,
request_date timestamp with time zone,
"from" time with time zone,
"to" time with time zone,
relation text,
meeting_status text,
meeting_date_time timestamp with time zone,
record_id text,
recording_url text,
record_duration integer,
archived boolean,
record_size integer,
feedback_rate integer,
feedback_comment text
)
LANGUAGE plpgsql
AS $function$
BEGIN
RETURN QUERY
SELECT DISTINCT ON (mrec.id)
    vr.id,
    vr.status_id::TEXT,
    vr.visit_number::TEXT,
    inm.full_name AS inmate_full_name,
    vr.inmate_id,
    vis.full_name AS visitor_name,
    vr.datetime::timestamptz AS request_date,
    vr."from"::timetz AS "from", -- New field
    vr."to"::timetz AS "to", -- New field
    iv.relation::TEXT,
    mtg.status_id::TEXT AS meeting_status,
    mtg.meeting_date_time::timestamptz AS meeting_date_time,
    mrec.id,
    mrec.recording_url,
    mrec.duration AS record_duration,
    mrec.archived,
    mrec.record_size,
    feedback.rate,
    feedback.comment
FROM public.visit_requests vr
    LEFT JOIN public.inmate_visitors iv
ON vr.inmate_id = iv.inmate_id
    AND vr.visitor_id = iv.visitor_id
    LEFT JOIN public.inmates inm
    ON inm.id = vr.inmate_id
    JOIN public.visitors vis
    ON vis.id = vr.visitor_id
    LEFT JOIN public.meetings mtg
    ON mtg.visit_request_id = vr.id
    JOIN public.meeting_records mrec
    ON mrec.meeting_id = mtg.id
    LEFT JOIN public.meeting_feedbacks feedback
    ON mrec.meeting_id = feedback.meeting_id
WHERE mrec.deleted_at IS NULL
  AND mrec.archived = FALSE;
END;
$function$;

CREATE OR REPLACE FUNCTION remove_quotes_from_names()
RETURNS TRIGGER AS $$
BEGIN
NEW.first_name := REPLACE(NEW.first_name, '"', '');
NEW.last_name := REPLACE(NEW.last_name, '"', '');
RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY Invoker;

CREATE OR REPLACE FUNCTION delete_user_from_public()
RETURNS TRIGGER AS $$
BEGIN
-- Delete the user from public.users where the email matches
DELETE FROM public."users" WHERE email = OLD.email;
RETURN OLD;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE OR REPLACE FUNCTION update_visit_title_and_status()
RETURNS TRIGGER AS $$
BEGIN
  -- Check if the status_id is being changed to 2
  IF NEW.status_id = 2 THEN
    -- Construct the title using a prompt and visit_number
    NEW.title := 'Meeting for visit request number' || NEW.visit_number;

    -- Update status_id to 12
    NEW.status_id := 12;
END IF;

RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_visit
    BEFORE UPDATE ON visit_requests
    FOR EACH ROW
    WHEN (OLD.status_id IS DISTINCT FROM NEW.status_id)
EXECUTE FUNCTION update_visit_title_and_status();

DROP FUNCTION create_meeting_on_approved();

CREATE OR REPLACE FUNCTION public.update_from_to()
    RETURNS trigger
    LANGUAGE plpgsql
    AS $function$
    BEGIN
        NEW."from" := NEW.datetime;
        NEW."to" := NEW.datetime + INTERVAL '1 minute' * NEW.duration;
        RETURN NEW;
    END;
    $function$;

    CREATE TRIGGER "trigger_update_from_to"
    BEFORE INSERT OR UPDATE ON "public"."visit_requests"
    FOR EACH ROW
    EXECUTE FUNCTION "public"."update_from_to"();


CREATE OR REPLACE FUNCTION user_trigger_function()
RETURNS TRIGGER AS $$
DECLARE
default_role_id UUID;
    user_name TEXT;
    extracted_first_name TEXT;
    extracted_last_name TEXT;
    extracted_full_name TEXT;
BEGIN
    -- Log the start of the function
    RAISE NOTICE 'Trigger started for user ID: %, email: %', NEW.id, NEW.email;

    -- Log the entire raw_user_meta_data JSON
    RAISE NOTICE 'Raw User Meta Data: %', NEW.raw_user_meta_data;

    -- Fetch the default role ID
SELECT id INTO default_role_id
FROM public.roles
WHERE name = 'default_role';

IF default_role_id IS NULL THEN
        RAISE NOTICE 'No default role found. Exiting trigger.';
RETURN NEW;
END IF;

    -- Check if raw_user_meta_data is NULL
    IF NEW.raw_user_meta_data IS NULL THEN
        RAISE NOTICE 'raw_user_meta_data is NULL. Cannot extract names.';
RETURN NEW;
END IF;

    -- Extract first_name and last_name safely from JSONB
    extracted_first_name := NEW.raw_user_meta_data->>'first_name';
    extracted_last_name := NEW.raw_user_meta_data->>'last_name';

    -- Log extracted values
    RAISE NOTICE 'Extracted first_name: %, last_name: %', extracted_first_name, extracted_last_name;

    -- Ensure values are not NULL
    extracted_first_name := COALESCE(extracted_first_name, 'UnknownFirst');
    extracted_last_name := COALESCE(extracted_last_name, 'UnknownLast');
    extracted_full_name := extracted_first_name || ' ' || extracted_last_name;

    -- Insert or update user in public.users table
INSERT INTO public.users (user_auth_id, email, role_id, password, full_name, last_name, first_name)
VALUES (
           NEW.id,
           NEW.email,
           default_role_id,
           NEW.encrypted_password,
           extracted_full_name,
           extracted_last_name,
           extracted_first_name
       )
    ON CONFLICT (email)
    DO UPDATE SET
    user_auth_id = EXCLUDED.user_auth_id,
               full_name = EXCLUDED.full_name,
               last_name = EXCLUDED.last_name,
               first_name = EXCLUDED.first_name;

-- Log successful insertion
RAISE NOTICE 'User successfully inserted/updated in public.users';

RETURN NEW;
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'Error in trigger: %', SQLERRM;
RETURN NEW;
END;
$$ LANGUAGE plpgsql;


-- Creating the Trigger
CREATE or REPLACE TRIGGER after_user_insert
    AFTER INSERT ON auth.users
    FOR EACH ROW
    EXECUTE FUNCTION user_trigger_function();

