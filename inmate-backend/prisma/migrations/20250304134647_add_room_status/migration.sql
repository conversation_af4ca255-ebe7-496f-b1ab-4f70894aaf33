-- AlterTable
ALTER TABLE "notifications" ALTER COLUMN "datetime" DROP NOT NULL,
ALTER COLUMN "datetime" SET DATA TYPE TIMESTAMPTZ(6);

-- AlterTable
ALTER TABLE "rooms" ADD COLUMN     "status_id" INTEGER NOT NULL DEFAULT 1;

-- CreateTable
CREATE TABLE "room_status" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,

    CONSTRAINT "room_status_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "room_status_name_key" ON "room_status"("name");

-- AddForeignKey
ALTER TABLE "rooms" ADD CONSTRAINT "rooms_status_id_fkey" FOREIGN KEY ("status_id") REFERENCES "room_status"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

INSERT INTO room_status (name) VALUES ('available'), ('connected');

INSERT INTO "public"."visit_requests_status"
VALUES
    (1, 'pending_approval'),
    (2, 'scheduled'),
    (3, 'starting_soon'),
    (4, 'awaiting_admin'),
    (5, 'running'),
    (6, 'completed'),
    (7, 'missed'),
    (8, 'admin_cancelled'),
    (9, 'visitor_cancelled'),
    (10, 'denied'),
    (11, 'started'),
    (12, 'not_started'),
    (13, 'ended');
INSERT INTO "public"."inmates_status"  VALUES (1, 'available'), (2, 'unavailable'), (3, 'suspended');