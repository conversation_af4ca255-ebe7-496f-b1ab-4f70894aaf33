SELECT cron.schedule(
    'visit-request-status-check',
    '*/1 * * * *',
    $$
    -- Update status to 3 ("in progress") if within active window on the same day
    UPDATE public."visit_requests"
    SET status_id = 3
    WHERE datetime + INTERVAL '1 minute' < now() + INTERVAL '7 minute'
      AND status_id IN (2);

   UPDATE public."visit_requests"
    SET status_id = 4
    WHERE datetime + INTERVAL '1 minute' * 1 < now()
      AND status_id IN (3);

    -- Update status to 7 ("missed") if time has passed and still not completed
    UPDATE public."visit_requests"
    SET status_id = 7
    WHERE datetime + INTERVAL '1 minute' * duration < now() - INTERVAL '5 minute'
      AND status_id IN (2, 3, 4);

    UPDATE public."visit_requests"

    SET status_id = 6
    WHERE datetime + INTERVAL '1 minute' * duration < now() - INTERVAL '1 minute'
      AND status_id IN (5);

    -- DELETE test visit requests that have passed their end time
    DELETE FROM public."visit_requests"
    WHERE test = true
    AND datetime + INTERVAL '1 minute' * duration < now();

    -- Insert SLA notifications only if created today
    WITH sla_due AS (
        SELECT (value || ' hours')::interval AS due_interval
        FROM settings
        WHERE module = 'sla' AND settings.key = 'request_response_due'
    )
    INSERT INTO users_notifications (user_id, visitor_id, visit_id, title_id, created_at, seen)
    SELECT u.user_auth_id, vr.visitor_id, vr.id, 15, NOW(), NULL
    FROM public."visit_requests" vr
    CROSS JOIN users u
    CROSS JOIN sla_due
    WHERE vr.status_id = 1
      AND u.deleted_at IS NULL
      AND NOW() > vr.created_at + sla_due.due_interval
      AND NOT EXISTS (
        SELECT 1 
        FROM users_notifications un 
        WHERE un.user_id = u.user_auth_id 
          AND un.visit_id = vr.id 
          AND un.title_id = 15
      );
    $$
);