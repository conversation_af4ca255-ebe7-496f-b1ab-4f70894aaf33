CREATE OR REPLACE FUNCTION generate_user_notification()
RETURNS TRIGGER AS $$
DECLARE
    title_id_var int; -- Changed variable name to avoid ambiguity
    sla_due interval;
BEGIN
    -- Skip notification if this is a test record
    IF NEW.test THEN
        RETURN NEW;
    END IF;

    -- Handle new visitation request on INSERT
    IF TG_OP = 'INSERT' THEN
        title_id_var := 1;
    ELSE
        -- Handle status updates on UPDATE
        CASE NEW.status_id
            WHEN 2 THEN
                -- Check if the SLA is exceeded
                SELECT (value || ' hours')::interval INTO sla_due
                FROM settings
                WHERE settings.module = 'sla' AND settings.key = 'request_response_due';

                IF NOW() - NEW.created_at > sla_due THEN
                    title_id_var := 14; -- SLA exceeded
                ELSE
                    title_id_var := 2; -- Normal status update
                END IF;
            WHEN 10 THEN
                title_id_var := 10;
            WHEN 9 THEN
                title_id_var := 9;
            WHEN 8 THEN
                title_id_var := 8;
            WHEN 6 THEN
                IF NEW.datetime + INTERVAL '1 minute' * NEW.duration > now()
                THEN
                    title_id_var := 55; -- Visit in progress
                ELSE
                    title_id_var := 6; -- Visit ended
                END IF;
            WHEN 5 THEN
                title_id_var := 5;
            WHEN 3 THEN
                title_id_var := 3;
            ELSE
                -- If status_id is not one of the specified values, do nothing
                RETURN NEW;
        END CASE;
        
        -- Check if the status has actually changed
        IF OLD.status_id = NEW.status_id THEN
            -- Status didn't change, don't create notification
            RETURN NEW;
        END IF;
    END IF;

    -- Insert notifications for all users (with duplicate prevention)
    INSERT INTO users_notifications (user_id, visitor_id, visit_id, title_id, created_at, seen)
    SELECT user_auth_id, NEW.visitor_id, NEW.id, title_id_var, NOW(), NULL
    FROM users u
    WHERE u.deleted_at IS NULL
    AND NOT EXISTS (
        SELECT 1 
        FROM users_notifications un 
        WHERE un.user_id = u.user_auth_id 
          AND un.visit_id = NEW.id 
          AND un.title_id = title_id_var
    );

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger to handle both INSERTS and UPDATES
CREATE TRIGGER request_status_trigger
AFTER INSERT OR UPDATE OF status_id ON visit_requests
FOR EACH ROW
EXECUTE FUNCTION generate_user_notification();
