generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

model inmates {
  id                           String                         @id @default(dbgenerated("gen_random_uuid()"))
  full_name                    String
  national_id                  String?
  number_of_visits             Int
  section_id                   String
  room_id                      String?
  deleted_at                   DateTime?
  is_building_default          Boolean?
  is_room_default              Boolean?
  inmate_code                  String?                        @unique
  nationality                  String?
  status_id                    Int                            @default(1)
  inmate_availability_settings inmate_availability_settings[]
  inmate_settings              inmate_settings[]
  visitor_relations            inmate_visitors[]
  room                         rooms?                         @relation(fields: [room_id], references: [id])
  section                      sections                       @relation(fields: [section_id], references: [id])
  status                       inmates_status                 @relation(fields: [status_id], references: [id])
  visit_requests               visit_requests[]

  @@index([national_id], map: "idx_inmates_national_id", type: Hash)
}

model visitors {
  id                    String                @id @default(dbgenerated("gen_random_uuid()"))
  full_name             String
  national_id           String                @unique
  blocked               Boolean               @default(false)
  phone_number          String?
  block_reason          String?
  deleted_at            DateTime?
  nationality           String?
  first_name            String?
  last_name             String?
  inmate_relations      inmate_visitors[]
  meeting_feedbacks     meeting_feedbacks[]
  visitor_notifications notifications[]
  users_notifications   users_notifications[]
  visit_requests        visit_requests[]
}

model notifications {
  id         String         @id @default(dbgenerated("gen_random_uuid()"))
  datetime   DateTime?      @db.Timestamp(6)
  visitor_id String
  visit_id   String
  message    String
  seen       Boolean?       @default(false)
  visit      visit_requests @relation(fields: [visit_id], references: [id])
  visitor    visitors       @relation(fields: [visitor_id], references: [id])
}

model visit_requests {
  id                      String                    @id @default(dbgenerated("gen_random_uuid()"))
  visitor_id              String?
  inmate_id               String
  datetime                DateTime                  @db.Timestamp(6)
  duration                Int
  user_id                 String?                   @db.Uuid
  visit_number            Int                       @default(autoincrement())
  archived                Boolean                   @default(false)
  requested_inmate_name   String?
  from                    DateTime?                 @db.Time(6)
  status_id               Int
  to                      DateTime?                 @db.Time(6)
  actual_duration         Int?
  ended_at                DateTime?
  reason                  String?
  started_at              DateTime?
  title                   String?
  deleted_at              DateTime?
  created_at              DateTime                  @default(now()) @db.Timestamp(6)
  first_response_time     DateTime?
  updated_at              DateTime                  @default(now()) @updatedAt @db.Timestamp(6)
  meeting_url             String?
  room_id                 String?
  test                    Boolean                   @default(false)
  is_flagged_word         Boolean                   @default(false)
  is_flagged_history      Boolean                   @default(false)
  meeting_feedbacks       meeting_feedbacks[]
  meeting_records         meeting_records[]
  visit_notifications     notifications[]
  room_slot               room_slots[]
  users_notifications     users_notifications[]
  meetingsTransacriptions MeetingsTransacriptions[] @relation("VisitToTranscription")
  room                    rooms?                    @relation(fields: [room_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "fk_visit_requests_rooms")
  inmate                  inmates                   @relation(fields: [inmate_id], references: [id])
  status                  visit_requests_status     @relation(fields: [status_id], references: [id])
  user                    users?                    @relation(fields: [user_id], references: [user_auth_id], onDelete: NoAction, onUpdate: NoAction)
  visitor                 visitors?                 @relation(fields: [visitor_id], references: [id])

  @@index([status_id])
  @@index([visitor_id])
  @@index([inmate_id])
  @@index([created_at])
}

model users {
  id                  String                @id @default(dbgenerated("gen_random_uuid()"))
  role_id             String
  full_name           String?
  email               String?               @unique
  password            String?
  first_name          String?
  last_name           String?
  user_auth_id        String?               @unique @db.Uuid
  deleted_at          DateTime?
  activity_logs       activity_logs[]
  permissions         user_permissions[]
  role                roles                 @relation(fields: [role_id], references: [id])
  users_notifications users_notifications[]
  visit_requests      visit_requests[]
}

model roles {
  id          String             @id @default(dbgenerated("gen_random_uuid()"))
  name        String
  deleted_at  DateTime?
  permissions role_permissions[]
  users       users[]
}

model permissions {
  id               String                 @id @default(dbgenerated("gen_random_uuid()"))
  name             String                 @unique(map: "unique_permission_name")
  category_id      Int
  category         permissions_categories @relation(fields: [category_id], references: [id])
  role_permissions role_permissions[]
  user_permissions user_permissions[]
}

model role_permissions {
  id            String      @id @default(dbgenerated("gen_random_uuid()"))
  role_id       String
  permission_id String
  permission    permissions @relation(fields: [permission_id], references: [id])
  role          roles       @relation(fields: [role_id], references: [id])

  @@unique([role_id, permission_id], map: "unique_role_permission")
}

model user_permissions {
  id            String      @id @default(dbgenerated("gen_random_uuid()"))
  user_id       String
  permission_id String
  permission    permissions @relation(fields: [permission_id], references: [id])
  user          users       @relation(fields: [user_id], references: [id])
}

model sections {
  id                            String                          @id @default(dbgenerated("gen_random_uuid()"))
  name                          String
  is_available                  Boolean                         @default(true)
  deleted_at                    DateTime?
  inmates                       inmates[]
  rooms                         rooms[]
  section_availability_settings section_availability_settings[]
  sectionOverrides              SectionOverride[]
}

model section_availability_settings {
  id         String   @id @default(dbgenerated("gen_random_uuid()"))
  section_id String
  day        days
  section    sections @relation(fields: [section_id], references: [id])
}

model availability_times {
  id             String            @id @default(dbgenerated("gen_random_uuid()"))
  setting_id     String
  type           availability_type
  available_from DateTime          @db.Time(6)
  available_to   DateTime          @db.Time(6)
}

model inmate_visitors {
  id                    String                     @id @default(dbgenerated("gen_random_uuid()"))
  inmate_id             String
  visitor_id            String
  is_white_list         Boolean?
  other                 String?
  relation              inmate_visitor_relation?
  deleted_at            DateTime?
  requested_inmate_name String?
  documents             inmate_visitor_documents[]
  inmate                inmates                    @relation(fields: [inmate_id], references: [id])
  visitor               visitors                   @relation(fields: [visitor_id], references: [id])
}

model inmate_visitor_documents {
  id                String          @id @default(dbgenerated("gen_random_uuid()"))
  inmate_visitor_id String
  document_url      String
  document_name     String
  document_size     String
  inmate_visitor    inmate_visitors @relation(fields: [inmate_visitor_id], references: [id])
}

model rooms {
  id                         String                       @id @default(dbgenerated("gen_random_uuid()"))
  name                       String
  active                     Boolean
  section_id                 String
  is_building_default        Boolean?
  deleted_at                 DateTime?
  status_id                  Int                          @default(1)
  inmates                    inmates[]
  overrides                  Override[]
  room_availability_settings room_availability_settings[]
  room_slots                 room_slots[]
  section                    sections                     @relation(fields: [section_id], references: [id])
  status                     room_status                  @relation(fields: [status_id], references: [id])
  visit_requests             visit_requests[]
}

model room_status {
  id    Int     @id @default(autoincrement())
  name  String  @unique
  rooms rooms[]
}

model room_availability_settings {
  id           String  @id @default(dbgenerated("gen_random_uuid()"))
  day          days
  room_id      String
  is_available Boolean @default(true)
  room         rooms   @relation(fields: [room_id], references: [id])
}

model inmate_availability_settings {
  id        String  @id @default(dbgenerated("gen_random_uuid()"))
  day       days
  inmate_id String
  inmate    inmates @relation(fields: [inmate_id], references: [id])
}

model meeting_records {
  id               String         @id @default(dbgenerated("gen_random_uuid()"))
  session_id       String?        @default(dbgenerated("gen_random_uuid()"))
  recording_url    String?
  record_size      Int?
  duration         Int?
  created_at       DateTime?
  deleted_at       DateTime?
  archived         Boolean?       @default(false)
  visit_request_id String
  visit_request    visit_requests @relation(fields: [visit_request_id], references: [id])
}

model meeting_feedbacks {
  id               String         @id @default(dbgenerated("gen_random_uuid()"))
  visitor_id       String
  comment          String?
  rate             Int
  visit_request_id String
  visit_request    visit_requests @relation(fields: [visit_request_id], references: [id])
  visitor          visitors       @relation(fields: [visitor_id], references: [id])
}

model room_slots {
  id               String         @id @default(dbgenerated("gen_random_uuid()"))
  room_id          String
  slot_start       DateTime
  slot_end         DateTime
  created_at       DateTime       @default(now())
  updated_at       DateTime       @updatedAt
  visit_request_id String
  room             rooms          @relation(fields: [room_id], references: [id])
  visit_request    visit_requests @relation(fields: [visit_request_id], references: [id])
}

model inmate_settings {
  id        String  @id @default(dbgenerated("gen_random_uuid()"))
  inmate_id String
  key       String
  value     String
  inmate    inmates @relation(fields: [inmate_id], references: [id])

  @@unique([inmate_id, key])
}

model visit_requests_status {
  id             Int              @id @default(autoincrement())
  name           String           @unique
  visit_requests visit_requests[]
}

model inmates_status {
  id      Int       @id @default(autoincrement())
  name    String    @unique
  inmates inmates[]
}

model settings {
  id     String @id @default(dbgenerated("gen_random_uuid()"))
  key    String
  value  String
  module String
}

model permissions_categories {
  id          Int           @id @default(autoincrement())
  name        String
  permissions permissions[]
}

model activity_logs {
  id           String   @id @default(dbgenerated("gen_random_uuid()")) @map("id") @db.Uuid
  created_at   DateTime @default(now()) @db.Timestamptz(6)
  user_id      String?  @map("user_id") @db.Uuid
  user_email   String?
  entity_type  String
  entity_id    String
  action       String
  action_group String?
  old_data     Json?
  new_data     Json?
  metadata     Json?
  ip_address   String?
  user_agent   String?
  users        users?   @relation(fields: [user_id], references: [user_auth_id], onDelete: NoAction, onUpdate: NoAction)

  @@index([action])
  @@index([created_at(sort: Desc)])
  @@index([entity_type, entity_id])
  @@index([entity_type])
  @@index([user_id])
  @@map("activity_logs")
}

model notification_titles {
  id                  Int                   @id
  name                String
  users_notifications users_notifications[]
}

model users_notifications {
  id         String              @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  visitor_id String
  visit_id   String
  title_id   Int
  created_at DateTime            @default(now()) @db.Timestamp(6)
  seen       DateTime?
  user_id    String?             @db.Uuid
  user       users?              @relation(fields: [user_id], references: [user_auth_id], onDelete: NoAction, map: "public_users_notifications_user_id_fkey")
  title      notification_titles @relation(fields: [title_id], references: [id])
  visit      visit_requests      @relation(fields: [visit_id], references: [id])
  visitor    visitors            @relation(fields: [visitor_id], references: [id])

  @@index([visitor_id])
  @@index([visit_id])
  @@index([seen])
  @@index([title_id])
  @@index([visitor_id, visit_id])
}

model Server {
  id       String  @id @default(uuid()) @db.Uuid
  code     Int     @unique
  link     String  @unique
  isactive Boolean @default(true)

  @@map("servers")
}

model Override {
  id        String             @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  room_id   String
  room      rooms              @relation(fields: [room_id], references: [id], onUpdate: NoAction, map: "fk_room")
  dates     OverrideDate[]     @relation("OverrideToDates")
  timeslots OverrideTimeslot[] @relation("OverrideToTimeslot")

  @@index([room_id], map: "idx_override_room")
  @@map("overrides")
}

model OverrideDate {
  id          String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  override_id String   @db.Uuid
  date        DateTime @db.Date
  override    Override @relation("OverrideToDates", fields: [override_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "fk_override_date")

  @@unique([override_id, date], map: "unique_override_date")
  @@index([override_id], map: "idx_override_date_override_id")
  @@index([date], map: "idx_override_date_date")
  @@index([override_id, date], map: "idx_override_date_composite")
  @@map("override_dates")
}

model OverrideTimeslot {
  id          String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  override_id String   @db.Uuid
  starttime   DateTime @db.Time(6)
  endtime     DateTime @db.Time(6)
  override    Override @relation("OverrideToTimeslot", fields: [override_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "fk_override")

  @@map("overrides_timeslots")
}

model SectionOverride {
  id         String                    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  section_id String
  section    sections                  @relation(fields: [section_id], references: [id], onUpdate: NoAction, map: "fk_section")
  dates      SectionOverrideDate[]     @relation("SectionOverrideToDates")
  timeslots  SectionOverrideTimeslot[] @relation("SectionOverrideTimeslot")

  @@index([section_id], map: "idx_section_override_section")
  @@map("section_overrides")
}

model SectionOverrideDate {
  id          String          @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  override_id String          @db.Uuid
  date        DateTime        @db.Date
  override    SectionOverride @relation("SectionOverrideToDates", fields: [override_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "fk_section_override_date")

  @@unique([override_id, date], map: "unique_section_override_date")
  @@index([override_id], map: "idx_section_override_date_override_id")
  @@index([date], map: "idx_section_override_date_date")
  @@index([override_id, date], map: "idx_section_override_date_composite")
  @@map("section_override_dates")
}

model SectionOverrideTimeslot {
  id          String          @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  override_id String          @db.Uuid
  starttime   DateTime        @db.Time(6)
  endtime     DateTime        @db.Time(6)
  override    SectionOverride @relation("SectionOverrideTimeslot", fields: [override_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "fk_section_override")

  @@map("section_overrides_timeslots")
}

model MeetingsTransacriptions {
  id            String         @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  meeting_id    String
  timestamp     DateTime       @default(now()) @db.Timestamp(6)
  transcription String
  visit         visit_requests @relation("VisitToTranscription", fields: [meeting_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@unique([meeting_id], map: "unique_meeting_transcription_meeting_id")
  @@index([meeting_id], map: "idx_meeting_transcription_meeting_id")
  @@map("meetings_transacriptions")
}

model function_logs {
  id            String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  function_name String?
  message       String?
  timestamp     DateTime? @default(now()) @db.Timestamp(6)
}

model schema_migrations {
  version     BigInt    @id
  inserted_at DateTime? @db.Timestamp(0)
}

model tenants {
  id                      String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  external_id             String
  name                    String
  jwt_secret              String
  postgres_cdc_default    Boolean?  @default(true)
  inserted_at             DateTime? @default(now()) @db.Timestamp(6)
  updated_at              DateTime? @default(now()) @db.Timestamp(6)
  max_concurrent_users    Int?      @default(200)
  max_events_per_second   Int?      @default(100)
  max_bytes_per_second    Int?      @default(100000)
  max_channels_per_client Int?      @default(100)
  max_joins_per_second    Int?      @default(100)
  suspend                 Boolean?  @default(false)
}

enum availability_type {
  room
  section
  inmate
}

enum RoleName {
  SUPERADMIN
  ADMIN
}

enum InmateVitorRelationDegree {
  FIRST
  SECOND
  OTHERS
  THIRD
}

enum days {
  sunday
  monday
  tuesday
  wednesday
  thursday
  friday
  saturday
}

enum inmate_visitor_relation {
  MOTHER
  FATHER
  BROTHER
  SISTER
  SON
  DAUGHTER
  GRANDFATHER
  GRANDMOTHER
  GRANDSON
  GRANDDAUGHTER
  UNCLE
  AUNT
  NEPHEW
  NIECE
  COUSIN
  GREAT_UNCLE
  GREAT_AUNT
  GREAT_NEPHEW
  GREAT_NIECE
  HUSBAND
  WIFE
  LAWYER
  LEGAL_REPRESENTATIVE
  GOVERNMENT_OFFICIAL
  SOCIAL_WORKER
  OTHER
}

enum inmate_status {
  AVAILABLE
  UNAVAILABLE
  SUSPENDED
}
