import {serve} from "https://deno.land/std@0.168.0/http/server.ts";
import {createClient} from "npm:@supabase/supabase-js@2.39.3";
import hasUserPermission from "../src/utils/hasPermission.ts";
import { withLogging } from '../src/utils/logger.ts';  // Change from default import to named import

  const supabase = createClient(
      Deno.env.get("SUPABASE_URL")!,
      Deno.env.get("SUPABASE_ANON_KEY")!,
  );

  const corsHeaders = {
    "Access-Control-Allow-Origin": "*", // You can replace "*" with a specific domain for security
    "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
    "Access-Control-Allow-Headers": "Content-Type, Authorization",
  };

  serve(withLogging(async (req) => {
    // Handle preflight CORS request
    if (req.method === "OPTIONS") {
      return new Response(null, { status: 204, headers: corsHeaders });
    }

    // Handle other requests
    if (req.method !== "POST") {
      return new Response(JSON.stringify({ error: "Method not allowed" }), {
        status: 405,
        headers: corsHeaders,
      });
    }

    const { role, permissions, user_id } = await req.json();

    if (!role || !Array.isArray(permissions) || permissions.length === 0) {
      return new Response(JSON.stringify({ error: "Invalid input" }), {
        status: 400,
        headers: corsHeaders,
      });
    }

    try {
      // Check if the role already exists and is not marked as deleted
      const { data: existingRole, error: existingRoleError } = await supabase
        .from("roles")
        .select("id")
        .eq("name", role)
        .eq("deleted_at", null)
        .single();

      if (existingRoleError && existingRoleError.code !== "PGRST116") {
        throw existingRoleError;
      }

      if (existingRole) {
        return new Response(
          JSON.stringify({ error: "Role already exists" }),
          { status: 400, headers: corsHeaders }
        );
      }

      // Upsert the role
      const { data: roleData, error: roleError } = await supabase
        .from("roles")
        .upsert({ name: role }, { onConflict: "name" })
        .select()
        .single();

      if (roleError) throw roleError;

      const roleId = roleData.id;

      const { data: user_roles, error: userError } = await supabase
          .from('users')
          .select('* , role_id(name,role_permissions(permission_id(name)))')
          .eq('user_auth_id', user_id)
          .single();

      if (userError || !user_roles) {
        return new Response(
            JSON.stringify({ error: `User with ID ${user_id} does not exist.` }),
            { status: 404, headers: corsHeaders }
        );
      }
      console.log('user_roles:', user_roles.role_id.name);
      console.log('user:', user_roles);
      const permission = await hasUserPermission(user_roles,"Add New Role");
      console.log('permission:', permission);
      if (!permission) {
        return new Response(
            JSON.stringify({ error: `doesn’t have the required permission`,
              statusError:455,
              permission: user_roles.role_id.name}),
            { status: 404, headers: corsHeaders }
        );
      }
      // Check if permissions exist, add missing ones
      const permissionIds = [];
      const permissionsToAdd = [];
      for (const permission of permissions) {
        const { data: permissionData, error: permissionError } = await supabase
          .from("permissions")
          .select("id")
          .eq("name", permission)
          .single();

        if (permissionError && permissionError.code !== "PGRST116") {
          throw permissionError;
        }

        if (permissionData) {
          permissionIds.push(permissionData.id);
        } else {
          permissionsToAdd.push(permission);
        }
      }

      // Add new permissions if any are missing
      if (permissionsToAdd.length > 0) {
        const { data: newPermissions, error: newPermissionError } = await supabase
          .from("permissions")
          .upsert(
            permissionsToAdd.map((permission) => ({ name: permission })),
            { onConflict: "name" }
          )
          .select("id");

        if (newPermissionError) throw newPermissionError;

        newPermissions.forEach((permission) => permissionIds.push(permission.id));
      }
      const second_permission = await hasUserPermission(user_roles, "Add Permission to the Role");
      if (!second_permission) {
        return new Response(
            JSON.stringify({ error: `doesn’t have the required permission`,
              statusError:455,
              permission: user_roles.role_id.name}),
            { status: 404, headers: corsHeaders }
        );
      }
      // Link role with permissions
      const rolePermissionInsertions = permissionIds.map((permissionId) => ({
        role_id: roleId,
        permission_id: permissionId,
      }));

      const { error: linkError } = await supabase
        .from("role_permissions")
        .upsert(rolePermissionInsertions, { onConflict: ["role_id", "permission_id"] });

      if (linkError) throw linkError;

      // Fetch all permissions of the role
      const { data: assignedPermissions, error: fetchError } = await supabase
        .from("permissions")
        .select("name")
        .in("id", permissionIds);

      if (fetchError) throw fetchError;

      return new Response(
        JSON.stringify({
          message: "Role and permissions created/updated successfully.",
          role: roleData.name,
          permissions: assignedPermissions.map((p) => p.name),
        }),
        { status: 200, headers: corsHeaders }
      );
    } catch (error) {
      console.error("Error creating/updating role and permissions:", error);
      return new Response(JSON.stringify({ error: error.message }), {
        status: 500,
        headers: corsHeaders,
      });
    }
  }, "create_role"));
