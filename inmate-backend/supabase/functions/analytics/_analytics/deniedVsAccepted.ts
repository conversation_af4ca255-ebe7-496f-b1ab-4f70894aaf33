import { supabase } from "../../src/lib/SupabaseClient.ts";

const deniedVsAccepted = async (
  fromDateStr: string,
  toDateStr: string,
  csv: boolean = false
): Promise<any | null> => {
  try {
    // Run both queries in parallel with Promise.all
    const [acceptedResponse, deniedResponse] = await Promise.all([
      // First query - get accepted requests
      supabase
        .from("visit_requests")
        .select("datetime", { count: "exact" })
        .eq("status_id", Deno.env.get("VISIT_REQUEST_STATUS_APPROVED") || "6")
        .eq("test", false)
        .gte("datetime", fromDateStr)
        .lte("datetime", toDateStr),

      // Second query - get denied requests
      supabase
        .from("visit_requests")
        .select("datetime, reason", { count: "exact" })
        .eq("status_id", Deno.env.get("VISIT_REQUEST_STATUS_DENIED") || "10")
        .eq("test", false)
        .gte("datetime", fromDateStr)
        .lte("datetime", toDateStr),
    ]);

    // Check for errors in either query
    if (acceptedResponse.error) {
      console.error(
        "Error fetching accepted visit requests:",
        acceptedResponse.error
      );
      return null;
    }

    if (deniedResponse.error) {
      console.error(
        "Error fetching denied visit requests:",
        deniedResponse.error
      );
      return null;
    }

    // Process the data for monthly breakdown
    const acceptedByMonth = Array(12).fill(0);
    const deniedByMonth = Array(12).fill(0);
    const reasons = {
      "Document provided is not clear": 0,
      "Inmate is not available at the moment": 0,
      "Id is not valid": 0,
    };
    let other = 0;
    // Process accepted requests
    acceptedResponse.data?.forEach((item) => {
      const month = new Date(item.datetime).getMonth();
      acceptedByMonth[month]++;
    });

    // Process denied requests
    deniedResponse.data?.forEach((item) => {
      const month = new Date(item.datetime).getMonth();
      if (item.reason in reasons) {
        reasons[item.reason]++;
      } else {
        other++;
      }
      deniedByMonth[month]++;
    });
    const totalAccepted = acceptedResponse.count || 0;
    const totalDenied = deniedResponse.count || 0;
    const total = totalAccepted + totalDenied;
    reasons["other"] = other;
    let largest = 0;
    let largestKey = "";
    for (const key in reasons) {
      if (reasons[key] > 0) {
        reasons[key] = (reasons[key] / total) * 100;
      }
      if (reasons[key] > largest) {
        largest = reasons[key];
        largestKey = key;
      }
    }
    // Return formatted result
    if (csv === false) {
      return {
        accepted: {
          total: totalAccepted,
          byMonth: acceptedByMonth,
        },
        denied: {
          total: totalDenied,
          byMonth: deniedByMonth,
        },
        percentage: totalAccepted > 0 ? (totalAccepted / total) * 100 : 0,
        reasons: { name: largestKey, percentage: largest },
      };
    } else {
      return {
        accepted: {
          total: totalAccepted,
          byMonth: acceptedByMonth,
        },
        denied: {
          total: totalDenied,
          byMonth: deniedByMonth,
        },
        percentage: totalAccepted > 0 ? (totalAccepted / total) * 100 : 0,
        reasons: {
          ...reasons,
          largest: largestKey,
        },
      };
    }
  } catch (e) {
    console.error("Unexpected error in deniedVsAccepted:", e);
    return null;
  }
};

const deniedVsAcceptedCsvParser = (data: any) => {
  const csvData: string[] = [];
  csvData.push("Denied vs Accepted and Accepted Requests Rate");
  const headers = ["Month", "Accepted", "Denied", "Percentage Accepted", "Percentage Denied"];
  csvData.push(headers.join(","));
  const months = [
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "Jun",
    "Jul",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec",
  ];
  for (let i = 0; i < 12; i++) {
    const month = months[i];
    const accepted = data.accepted.byMonth[i] || 0;
    const denied = data.denied.byMonth[i] || 0;
    const percentageAccepted =
      data.accepted.byMonth[i] > 0
        ? parseFloat(
            ((data.accepted.byMonth[i] /
              (data.accepted.byMonth[i] + data.denied.byMonth[i])) *
              100).toFixed(2)
          )
        : 0;

    const percentageDenied = 
      data.denied.byMonth[i] > 0
        ? parseFloat(
            ((data.denied.byMonth[i] /
              (data.accepted.byMonth[i] + data.denied.byMonth[i])) *
              100).toFixed(2)
          )
        : 0;
    csvData.push([month, accepted, denied, percentageAccepted, percentageDenied].join(","));
  }
  csvData.push(
    "Total Accepted, Total Denied, Total, Percentage Accepted, Percentage Denied"
  );
  csvData.push(
    [
      data.accepted.total + data.denied.total,
      data.denied.total,
      data.accepted.total,
      data.percentage,
      100 - data.percentage,
    ].join(",")
  );
  csvData.push(["Reasons", "Percentage"].join(","));
  for (const reason in data.reasons) {
    if (reason !== "largest") {
      csvData.push([reason, data.reasons[reason]].join(","));
    }
  }
  csvData.push("Largest Reason, Percentage");
  csvData.push(
    [data.reasons.largest, data.reasons[data.reasons.largest]].join(",")
  );
  csvData.push("****End of Denied vs Accepted and Accepted Requests Rate****");
  return csvData.join("\n");
};

const deniedVsAcceptedModule = {
  main: deniedVsAccepted,
  csvParser: deniedVsAcceptedCsvParser,
};

export default deniedVsAcceptedModule;
