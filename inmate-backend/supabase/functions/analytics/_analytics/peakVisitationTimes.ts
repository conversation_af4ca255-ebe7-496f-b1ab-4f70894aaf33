import { supabase } from "../../src/lib/SupabaseClient.ts";

// Define the interface for peak time result
interface PeakTimeResult {
  xAxis: Number[];
  yAxis: Number[];
}

const peakVisitationTimes = async (
  fromDateStr: string,
  toDateStr: string
): Promise<PeakTimeResult | null> => {
  try {
    const xAxis: Number[] = [];
    const yAxis: Number[] = [];

    // 1. Get all visits in the date range
    const { data, error } = await supabase
      .from("visit_requests")
      .select("datetime") // Properly formatted select
      .eq("status_id", Deno.env.get("VISIT_REQUEST_STATUS_APPROVED") || "6")
      .eq("test", false)
      .gte("datetime", fromDateStr)
      .lte("datetime", toDateStr);

    if (error) {
      console.error("Error fetching visit requests:", error);
      return null;
    }

    if (!data || data.length === 0) {
      console.log("No visits found in the specified date range");
      return { xAxis, yAxis };
    }

    // 2. Process the data to get counts by hour
    const hourCounts: Record<number, number> = {};

    data.forEach((visit) => {
      // Extract hour from datetime
      const visitDate = new Date(visit.datetime);
      const hour = visitDate.getHours();

      // Increment count for this hour
      hourCounts[hour] = (hourCounts[hour] || 0) + 1;
    });

    for (const [key, value] of Object.entries(hourCounts)) {
      xAxis.push(Number(key));
      yAxis.push(value);
    }

    return { xAxis, yAxis };
  } catch (e) {
    console.error("Unexpected error in peakVisitationTimes:", e);
    return null;
  }
};

const peakVisitationTimesCsv = async (data) => {
  try {

    let csvData: string[] = [];
    for (let i = 0; i < data.xAxis.length; i++) {
      csvData.push(`${data.xAxis[i]},${data.yAxis[i]}`);
    }

    const csvString = csvData.join("\n");
    return (
      "\n" +
      "Peak Visitation Times\n" +
      "Hour,Number of visits" +
      "\n" +
      csvString +
      "\n****End of Peak Visitation Times****\n"
    );
  } catch (e) {
    console.error("Error converting data to CSV:", e);
    return null;
  }
};
const peakVisitationTimesModule = {
  main: peakVisitationTimes,
  csvParser: peakVisitationTimesCsv,
};

export default peakVisitationTimesModule;
