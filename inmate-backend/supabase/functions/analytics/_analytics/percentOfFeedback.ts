import { supabase } from "../../src/lib/SupabaseClient.ts";

const percentOfFeedback = async (
  fromDateStr: string,
  toDateStr: string
): Promise<any | null> => {
  try {
    // Format the dates properly for PostgreSQL timestamp with timezone

    const [visitResponse, feedbackResponse] = await Promise.all([
      supabase
        .from("visit_requests")
        .select("*", { count: "exact" })
        .eq("status_id", Deno.env.get("VISIT_REQUEST_STATUS_ENDED") || "6")
        .eq("test", false)
        .gte("datetime", fromDateStr)
        .lte("datetime", toDateStr),
      supabase.from("meeting_feedbacks")
      .select("visit_request:visit_request_id(datetime, status_id)", { count: "exact" })
      .eq("visit_request.status_id", Deno.env.get("VISIT_REQUEST_STATUS_ENDED") || "6")
      .gte("visit_request.datetime", fromDateStr)
      .lte("visit_request.datetime", toDateStr)
    ]);

    return {
      visitsCount: visitResponse.count,
      feedbacksCount: feedbackResponse.count,
      percentage:
        visitResponse.count > 0
          ? (feedbackResponse.count / visitResponse.count) * 100
          : 0,
    };
  } catch (e) {
    console.error("Unexpected error in totalVists:", e);
    return null;
  }
};

const percentOfFeedbackCsvParser = async (data) => {
  const csvData: string[] = [];
  const { visitsCount, feedbacksCount, percentage } = data;
  csvData.push("Visits Count,Feedbacks Count,Percentage");
  csvData.push(`${visitsCount},${feedbacksCount},${percentage}`);
  const csvString = csvData.join("\n");
  return (
    "Percent of Feedback\n" +
    csvString +
    "\n" +
    "****End of Percent of Feedback****\n"
  );
};

const percentOfFeedbackModule = {
  main: percentOfFeedback,
  csvParser: percentOfFeedbackCsvParser,
};

export default percentOfFeedbackModule;
