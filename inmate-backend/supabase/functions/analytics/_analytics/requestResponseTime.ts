import { supabase } from "../../src/lib/SupabaseClient.ts";

const requestResponseTime = async (
  fromDateStr: string,
  toDateStr: string,
  csv: boolean = false
): Promise<any | null> => {
  try {
    // Format the dates properly for PostgreSQL timestamp with timezone
    const fromDate = new Date(fromDateStr);
    const toDate = new Date(toDateStr);
    // Your original calculation
    const beforeDateMs =
      fromDate.getTime() - (toDate.getTime() - fromDate.getTime());

    const beforeDate = new Date(beforeDateMs);

    const [currentData, beforeData] = await Promise.all([
      // First query - get accepted requests
      supabase
        .from("visit_requests")
        .select("first_response_time, created_at")
        .eq("test", false)
        .not("first_response_time", "is", null)
        .gte("datetime", fromDateStr)
        .lte("datetime", toDateStr),

      // Second query - get denied requests
      supabase
        .from("visit_requests")
        .select("first_response_time, created_at")
        .eq("test", false)
        .not("first_response_time", "is", null)
        .gte("datetime", beforeDate.toISOString())
        .lte("datetime", fromDateStr),
    ]);

    // Check for errors in either query
    if (currentData.error) {
      console.error(
        "Error fetching current visit requests:",
        currentData.error
      );
      return null;
    }
    if (beforeData.error) {
      console.error("Error fetching before visit requests:", beforeData.error);
      return null;
    }
    // Calculate the average response time for current data
    const [currentAverage, beforeAverage] = await Promise.all([
      calculateAverage(currentData.data),
      calculateAverage(beforeData.data),
    ]);
    const percentageChange =
      beforeAverage === 0
        ? 0
        : ((currentAverage - beforeAverage) / beforeAverage) * 100;
    if (csv === false) {
      return {
        currentAverage,
        percentageChange,
        duration: Math.round(
          (toDate.getTime() - fromDate.getTime()) / (1000 * 60 * 60 * 24)
        ),
      };
    } else {
      return {
        currentAverage,
        beforeAverage,
        percentageChange,
        duration: Math.round(
          (toDate.getTime() - fromDate.getTime()) / (1000 * 60 * 60 * 24)
        ),
      };
    }
  } catch (e) {
    console.error("Unexpected error in totalVists:", e);
    return null;
  }
};

async function calculateAverage(data: any[]) {
  let totalResponseTime = 0;
  let count = 0;

  for (const request of data) {
    totalResponseTime +=
      new Date(request.first_response_time).getTime() -
      new Date(request.created_at).getTime();
    count++;
  }

  return count === 0 ? 0 : totalResponseTime / count;
}

const requestResponseTimeCsvParser = async (data) => {
  const csvData: string[] = [];
  const headers = [
    "currentAverage",
    "beforeAverage",
    "percentageChange",
    "duration",
  ];
  csvData.push("Request Response Time");
  csvData.push(headers.join(","));
  const row = [
    data.currentAverage,
    data.beforeAverage,
    data.percentageChange,
    data.duration,
  ];
  csvData.push(row.join(","));
  return csvData.join("\n") + "\n" + "****End of Request Response Time****\n";
};

const requestResponseTimeModule = {
  main: requestResponseTime,
  csvParser: requestResponseTimeCsvParser,
};

export default requestResponseTimeModule;
