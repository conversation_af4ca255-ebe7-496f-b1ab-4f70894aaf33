import { supabase } from "../../src/lib/SupabaseClient.ts";

const totalRequests = async (
  fromDateStr: string,
  toDateStr: string
): Promise<any | null> => {
  try {
    // Format the dates properly for PostgreSQL timestamp with timezone

    const { data, error } = await supabase
      .from("visit_requests")
      .select("datetime")
      .eq("test", false)
      .gte("datetime", fromDateStr)
      .lte("datetime", toDateStr);

    if (error) {
      console.error("Error fetching visit requests:", error);
      return null;
    }
    const responseArray = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];
    for (const dataUnit of data) {
      responseArray[new Date(dataUnit.datetime).getMonth()]++;
    }

    return responseArray;
  } catch (e) {
    console.error("Unexpected error in totalVists:", e);
    return null;
  }
};

const totalRequestsCsvParser = async (data) => {
  const csvData: string[] = [];
  const headers = ["Month", "Total Requests"];
  csvData.push("Total Requests");
  csvData.push(headers.join(","));
  const months = [
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
    "August",
    "September",
    "October",
    "November",
    "December",
  ];
  for (let i = 0; i < data.length; i++) {
    csvData.push(`${months[i]},${data[i]}`);
  }
  return csvData.join("\n") + "\n" + "****End of Total Requests****" + "\n";
};

const totalRequestsModule = {
  main: totalRequests,
  csvParser: totalRequestsCsvParser,
};

export default totalRequestsModule;
