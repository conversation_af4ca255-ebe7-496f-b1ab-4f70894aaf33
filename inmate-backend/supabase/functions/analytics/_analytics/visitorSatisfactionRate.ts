import { supabase } from "../../src/lib/SupabaseClient.ts";

const visitorSatisfactionRate = async (
  fromDateStr: string,
  toDateStr: string
): Promise<any | null> => {
  try {
    // Format the dates properly for PostgreSQL timestamp with timezone

    const { data, error } = await supabase
      .from("meeting_feedbacks")
      .select("rate");

    if (error) {
      console.error("Error fetching meeting feedbacks:", error);
      return null;
    }

    let totalFeedbacks = 0;
    let totalRatings = 0;

    for (const feedback of data) {
      if (feedback.rate) {
        totalFeedbacks++;
        totalRatings += feedback.rate;
      }
    }
    return {
      totalFeedbacks,
      totalRatings,
      satisfactionRate: totalFeedbacks > 0 ? totalRatings / totalFeedbacks : 0,
    };
  } catch (e) {
    console.error("Unexpected error in totalVists:", e);
    return null;
  }
};

const visitorSatisfactionRateCsvParser = async (data) => {
  const { totalFeedbacks, totalRatings, satisfactionRate } = data;
  const csvData: string[] = [];
  const header = ["Total Feedbacks", "Total Ratings", "Satisfaction Rate"];
  csvData.push(header.join(","));
  const row = [totalFeedbacks, totalRatings, satisfactionRate];
  csvData.push(row.join(","));
  return (
    "Visitor Satisfaction Rate\n" +
    csvData.join("\n") +
    "\n" +
    "****End of Visitor Satisfaction Rate****\n"
  );
};

const visitorSatisfactionRateModule = {
  main: visitorSatisfactionRate,
  csvParser: visitorSatisfactionRateCsvParser,
};

export default visitorSatisfactionRateModule;
