import { supabase } from "../../src/lib/SupabaseClient.ts";

// Define the interface for the data items
interface InmateVisitData {
  InmateID: string;
  inmateCode: string;
  inmateName: string;
  numberOfVisits: number;
}

// Define the interface for the return value
interface VisitsPerInmateResult {
  totalPages: number;
  data: InmateVisitData[] | null;
  count: number | null;
  page: number;
  itemsPerPage: number;
}

const visitsPerInmate = async (
  fromDateStr: string,
  toDateStr: string,
  page: number = 1,
  itemsPerPage: number = 10,
  csv: boolean = false
): Promise<VisitsPerInmateResult | null> => {
  try {
    // Use a different approach - get counts directly from visit_requests
    const {
      data: visitData,
      error: countError,
    } = await supabase
      .from("visit_requests")
      .select(
        `
        inmate_id,
        inmates:inmate_id (
          id,
          full_name,
          inmate_code
        )
      `,
        { count: "exact" }
      )
      .eq("test", false)
      .gte("datetime", fromDateStr)
      .lte("datetime", toDateStr);

    if (countError) {
      console.error("Error fetching visit count:", countError);
      return null;
    }

    // Process the data to get counts per inmate
    const visitCounts: Record<
      string,
      {
        inmateId: string;
        inmateCode: string;
        inmateName: string;
        count: number;
      }
    > = {};

    visitData?.forEach((visit) => {
      const inmateId = visit.inmate_id;
      const inmateName = visit.inmates?.full_name || "Unknown";
      const inmateCode = visit.inmates?.inmate_code || "Unknown";

      if (!visitCounts[inmateId]) {
        visitCounts[inmateId] = { inmateId, inmateCode, inmateName, count: 0 };
      }

      visitCounts[inmateId].count++;
    });
    let visitCountsArray = Object.values(visitCounts).sort(
      (a, b) => b.count - a.count
    );
    // Convert to array and sort
    if (csv === false) {
      if (page > 0 && itemsPerPage >= 0) {
        visitCountsArray = visitCountsArray.slice(
          (page - 1) * itemsPerPage,
          page * itemsPerPage
        );
      }
    }

    // Format for return
    const formattedData = visitCountsArray.map((item) => ({
      InmateID: item.inmateId,
      inmateCode: item.inmateCode,
      inmateName: item.inmateName,
      numberOfVisits: item.count,
    }));

    // Total pages calculation
    const totalPages = Math.ceil(
      (Object.keys(visitCounts).length || 0) / itemsPerPage
    );

    return {
      totalPages,
      data: formattedData,
      count: Object.keys(visitCounts).length,
      page,
      itemsPerPage,
    };
  } catch (e) {
    console.error("Unexpected error in visitsPerInmate:", e);
    return null;
  }
};

const visitsPerInmateCsvParser = (data) => {
  const csvData: string[] = [];
  const headers = ["Inmate ID", "Inmate Name", "Inmate Code", "Number of visits"];
  csvData.push("Visits Per Inmate");
  csvData.push(headers.join(","));
  data.data.forEach((item: InmateVisitData) => {
    csvData.push(
      [
        item.InmateID,
        item.inmateName,
        item.inmateCode,
        item.numberOfVisits,
      ].join(",")
    );
  });
  return csvData.join("\n") + "\n" + "***End of Visits Per Inmate***\n";
};

const pageVisitsPerInmateModule = {
  main: visitsPerInmate,
  csvParser: visitsPerInmateCsvParser,
};

export default pageVisitsPerInmateModule;
