import { supabase } from "../../src/lib/SupabaseClient.ts";

// Define the interface for the data items
interface visitorVisitData {
  visitorId: string;
  visitorName: string;
  nationalId: string;
  numberOfVisits: number;
}

// Define the interface for the return value
interface VisitsPerVisitorResult {
  totalPages: number;
  data: visitorVisitData[] | null;
  count: number | null;
  page: number;
  itemsPerPage: number;
}

const visitsPerVisitor = async (
  fromDateStr: string,
  toDateStr: string,
  page: number = 1,
  itemsPerPage: number = 10,
  csv: boolean = false
): Promise<VisitsPerVisitorResult | null> => {
  try {
    // Use a different approach - get counts directly from visit_requests
    const {
      data: visitData,
      error: countError,
    } = await supabase
      .from("visit_requests")
      .select(
        `
        visitor_id,
        visitors:visitor_id (
          id,
          full_name,
          national_id
        )
      `,
        { count: "exact" }
      )
      .eq("test", false)
      .gte("datetime", fromDateStr)
      .lte("datetime", toDateStr);

    if (countError) {
      console.error("Error fetching visit count:", countError);
      return null;
    }

    // Process the data to get counts per inmate
    const visitCounts: Record<
      string,
      { visitorId: string; visitorName: string; nationalId; count: number }
    > = {};

    visitData?.forEach((visit) => {
      const visitorId = visit.visitor_id;
      const visitorName = visit.visitors?.full_name || "Unknown";
      const nationalId = visit.visitors?.national_id || "Unknown";

      if (!visitCounts[visitorId]) {
        visitCounts[visitorId] = {
          visitorId,
          visitorName,
          nationalId,
          count: 0,
        };
      }

      visitCounts[visitorId].count++;
    });

    let visitCountsArray = Object.values(visitCounts).sort(
      (a, b) => b.count - a.count
    );
    // Convert to array and sort
    if (csv === false) {
      if (page > 0 && itemsPerPage > 0) {
        visitCountsArray = visitCountsArray.slice(
          (page - 1) * itemsPerPage,
          page * itemsPerPage
        );
      }
    }

    // Format for return
    const formattedData = visitCountsArray.map((item) => ({
      visitorId: item.visitorId,
      visitorName: item.visitorName,
      nationalId: item.nationalId,
      numberOfVisits: item.count,
    }));

    // Total pages calculation
    const totalPages = Math.ceil(
      (Object.keys(visitCounts).length || 0) / itemsPerPage
    );

    return {
      totalPages,
      data: formattedData,
      count: Object.keys(visitCounts).length,
      page,
      itemsPerPage,
    };
  } catch (e) {
    console.error("Unexpected error in visitsPerInmate:", e);
    return null;
  }
};

const visitsPerVisitorCsvParser = (data) => {
  const csvData: string[] = [];
  const headers = [
    "Visitor ID",
    "Visitor Name",
    "National ID",
    "Number of visits",
  ];
  csvData.push("Visits Per Visitor");
  csvData.push(headers.join(","));
  data.data.forEach((item: visitorVisitData) => {
    csvData.push(
      [
        item.visitorId,
        item.visitorName,
        item.nationalId,
        item.numberOfVisits,
      ].join(",")
    );
  });
  return csvData.join("\n") + "\n" + "***End of Visits Per Visitor***\n";
};

const visitsPerVisitorModule = {
  main: visitsPerVisitor,
  csvParser: visitsPerVisitorCsvParser,
};

export default visitsPerVisitorModule;
