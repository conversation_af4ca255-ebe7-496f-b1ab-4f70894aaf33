import totalVisits from "./_analytics/totalVisits.ts";
import visitsPerInmate from "./_analytics/visitsPerInmate.ts";
import peakVisitationTimes from "./_analytics/peakVisitationTimes.ts";
import { isValidISODate } from "../src/utils/validateTime.ts";
import { isValidInteger } from "../src/utils/validateInteger.ts";
import totalRequests from "./_analytics/totalRequests.ts";
import  deniedVsAccepted  from "./_analytics/deniedVsAccepted.ts";
import visitsPerVisitor from "./_analytics/visitsPerVisitor.ts";
import percentOfFeedback from "./_analytics/percentOfFeedback.ts";
import visitorSatisfactionRate from "./_analytics/visitorSatisfactionRate.ts";
import requestResponseTime from "./_analytics/requestResponseTime.ts";
import { colors } from "../src/utils/logColors.ts";
import { withLogging } from "../src/utils/logger.ts"; // Change from default import to named import

// Follow this setup guide to integrate the Deno language server with your editor:
// https://deno.land/manual/getting_started/setup_your_environment
// This enables autocomplete, go to definition, etc.

// Setup type definitions for built-in Supabase Runtime APIs
// import "jsr:@supabase/functions-js/edge-runtime.d.ts"
const analytics_filters = {
  totalVisits: totalVisits,
  visitsPerInmate: visitsPerInmate,
  peakVisitationTimes: peakVisitationTimes,
  totalRequests: totalRequests,
  deniedVsAccepted: deniedVsAccepted,
  visitsPerVisitor: visitsPerVisitor,
  percentOfFeedback: percentOfFeedback,
  visitorSatisfactionRate: visitorSatisfactionRate,
  requestResponseTime: requestResponseTime,
};

// Add CORS headers helper
const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers":
    "authorization, x-client-info, apikey, content-type",
  "Access-Control-Allow-Methods": "GET, OPTIONS",
};

Deno.serve(
  withLogging(async (req) => {
    // Handle OPTIONS request
    if (req.method === "OPTIONS") {
      return new Response("ok", { headers: corsHeaders });
    }

    if (req.method !== "GET") {
      return new Response(JSON.stringify({ error: "Method not allowed" }), {
        status: 405,
        headers: {
          ...corsHeaders,
          "Content-Type": "application/json",
        },
      });
    }
    const tasks: Promise<any>[] = [];
    const responseBody = {};

    const url = new URL(req.url);
    const queryParameters = new URLSearchParams(url.searchParams.toString());

    const fromDataStr: string | null = queryParameters.get("fromDate");
    const toDataStr: string | null = queryParameters.get("toDate");

    if (!fromDataStr || !toDataStr) {
      return new Response(
        JSON.stringify({ error: "Missing fromDate or toDate" }),
        {
          status: 400,
          headers: {
            ...corsHeaders,
            "Content-Type": "application/json",
          },
        }
      );
    }

    try {
      const fromDate = new Date(fromDataStr);
      const toDate = new Date(toDataStr);

      if (
        !isValidISODate(fromDate, fromDataStr) ||
        !isValidISODate(toDate, toDataStr)
      ) {
        return new Response(
          JSON.stringify({
            error:
              "Invalid date format \n Date should follow ISO 8601 format YYYY-MM-DDTHH:mm:ss.sssZ",
          }),
          {
            status: 400,
            headers: {
              ...corsHeaders,
              "Content-Type": "application/json",
            },
          }
        );
      }

      if (fromDate > toDate) {
        return new Response(
          JSON.stringify({ error: "fromDate must be before toDate" }),
          {
            status: 400,
            headers: {
              ...corsHeaders,
              "Content-Type": "application/json",
            },
          }
        );
      }
    } catch (e) {
      console.error("Error parsing date:", e);
      return new Response(JSON.stringify({ error: "Error parsing date" }), {
        status: 400,
        headers: {
          ...corsHeaders,
          "Content-Type": "application/json",
        },
      });
    }
    let csv = false;
    if (queryParameters.get('csv')) {
      csv = true;
    }
    const responseKeys: string[] = [];
    const queryKeys: string[] = Array.from(queryParameters.keys());
    for (const key of queryKeys) {
      if (analytics_filters[key]) {
        responseKeys.push(key);

        if (key === "visitsPerInmate") {
          const page = queryParameters.get("pageVisitsPerInmate") || "1";
          const itemsPerPage =
            queryParameters.get("itemsPerPageVisitsPerInmate") || "10";

          // Validate page and itemsPerPage are valid integers
          if (!isValidInteger(page) || !isValidInteger(itemsPerPage)) {
            return new Response(
              JSON.stringify({
                error:
                  "Page and itemsPerPage must be valid non-negative integers",
              }),
              {
                status: 400,
                headers: {
                  ...corsHeaders,
                  "Content-Type": "application/json",
                },
              }
            );
          }

          tasks.push(
            analytics_filters[key].main(
              fromDataStr,
              toDataStr,
              Number(page),
              Number(itemsPerPage),
              csv
            )
          );
          continue;
        }
        if (key === "visitsPerVisitor") {
          const page = queryParameters.get("pageVisitsPerVisitor") || "1";
          const itemsPerPage =
            queryParameters.get("itemsPerPageVisitsPerVisitor") || "10";

          // Validate page and itemsPerPage are valid integers
          if (!isValidInteger(page) || !isValidInteger(itemsPerPage)) {
            return new Response(
              JSON.stringify({
                error:
                  "Page and itemsPerPage must be valid non-negative integers",
              }),
              {
                status: 400,
                headers: {
                  ...corsHeaders,
                  "Content-Type": "application/json",
                },
              }
            );
          }

          tasks.push(
            analytics_filters[key].main(
              fromDataStr,
              toDataStr,
              Number(page),
              Number(itemsPerPage),
              csv
            )
          );
          continue;
        }

        if (key === "deniedVsAccepted" || key === "requestResponseTime") {
          tasks.push(
            analytics_filters[key].main(
              fromDataStr,
              toDataStr,
              csv
            )
          );
          continue;
        }

        tasks.push(analytics_filters[key].main(fromDataStr, toDataStr));
      }
    }

    const results = await Promise.allSettled(tasks);
    for (let i = 0; i < results.length; i++) {
      if (results[i].status === "rejected") {
        const error = results[i] as PromiseRejectedResult;
        responseBody[responseKeys[i]] = { error: error.reason };
        continue;
      }
      if (results[i].status === "fulfilled") {
        const fulfilledResult = results[i] as PromiseFulfilledResult<any>;
        responseBody[responseKeys[i]] = fulfilledResult.value;
        continue;
      }

      responseBody[Object.keys(analytics_filters)[i]] = results[i];
    }

    if (csv === true) {
      const csvData: string[] = [];
      for (const key of Object.keys(responseBody)) {
        const csvResult = await analytics_filters[key].csvParser(responseBody[key]);
        console.log(csvResult);
        csvData.push(csvResult);
      }

      // Generate filename with date range
      const filename = `analytics_${fromDataStr.split('T')[0]}_to_${toDataStr.split('T')[0]}.csv`;

      console.log(
        `\n${colors.cyan}${colors.bright}Request Path:${colors.reset} ${colors.green}${url.pathname}${colors.reset}` +
        `\n${colors.cyan}${colors.bright}Response CSV For Analytics: ${Object.keys(responseBody).join(' and ')}${colors.reset}`
      );

      return new Response(csvData.join("\n"), {
        headers: {
          ...corsHeaders,
          "content-type": "text/csv",
          "content-disposition": `attachment; filename="${filename}"`,
        },
      });
    }

    console.log(
      `\n${colors.cyan}${colors.bright}Request Path:${colors.reset} ${colors.green}${url.pathname}${colors.reset}` +
        `\n${colors.cyan}${colors.bright}Response:${colors.reset}`,
      responseBody
    );
    return new Response(JSON.stringify(responseBody), {
      headers: {
        ...corsHeaders,
        "content-type": "application/json",
      },
    });
  }, "analytics")
);
