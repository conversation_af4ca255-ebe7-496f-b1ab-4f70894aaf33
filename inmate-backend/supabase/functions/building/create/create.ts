import 'https://esm.sh/@supabase/functions-js/src/edge-runtime.d.ts';

import { createClient } from 'npm:@supabase/supabase-js@2.39.3';
// Initialize Supabase client
const supabase = createClient(
    Deno.env.get('SUPABASE_URL')!,
    Deno.env.get('SUPABASE_ANON_KEY')!
);

// CORS Headers
const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
    'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
    'Access-Control-Max-Age': '86400',
};
export default async function createBuilding(req: Request) {
    try {
        // Parse the request body
        const { buildingCode, buildingAvailability, filteredDays } = await req.json();
        console.log('Request body parsed:', { buildingCode, buildingAvailability, filteredDays });

        // Input validation
        if (!buildingCode || typeof buildingCode !== 'string') {
            console.error('Invalid buildingCode');
            throw new Error('Invalid buildingCode: must be a non-empty string.');
        }

        if (typeof buildingAvailability !== 'boolean') {
            console.error('Invalid buildingAvailability');
            throw new Error('Invalid buildingAvailability: must be a boolean.');
        }

        if (!Array.isArray(filteredDays) || filteredDays.length === 0) {
            console.error('Invalid filteredDays');
            throw new Error('Invalid filteredDays: must be a non-empty array.');
        }

        for (const { day, times } of filteredDays) {
            if (!day || typeof day !== 'string') {
                console.error('Invalid day:', day);
                throw new Error('Invalid day: must be a non-empty string.');
            }

            if (!Array.isArray(times) || times.length === 0) {
                console.error(`Invalid times for day ${day}:`, times);
                throw new Error(`Invalid times for day ${day}: must be a non-empty array.`);
            }

            for (const { from, to } of times) {
                // if (!from || !to || !isValidTimeFormat(from) || !isValidTimeFormat(to)) {
                //   console.error(`Invalid time format in times for day ${day}:`, { from, to });
                //   throw new Error(`Invalid time format in times for day ${day}: must be in HH:mm:ss format.`);
                // }
            }
        }
        const { data: existitingsection } = await supabase
            .from('sections')
            .select('id')
            .eq('name', buildingCode)
            .is('deleted_at', null)
            .single();

        if (existitingsection) {
            console.log('sections already exists:', existitingsection);
            return new Response(
                JSON.stringify({ message: 'sections already exists',
                    // status : 409
                }),
                {
                    headers: {
                        ...corsHeaders,
                        'Content-Type': 'application/json',
                    },
                    status: 409
                    ,
                }
            );
        }

        // Insert section and get its ID
        console.log('Inserting into sections table:', { buildingCode, buildingAvailability });
        const { data: section, error: sectionError } = await supabase
            .from('sections')
            .insert([{ name: buildingCode, is_available: buildingAvailability }])
            .select('id')
            .single();

        if (sectionError || !section) {
            console.error('Error inserting into sections table:', sectionError);
            throw new Error(`Error inserting into sections table: ${sectionError?.message || 'No data returned'}`);
        }
        console.log('sections inserted successfully:', section);

        // Prepare batch inserts for availability settings
        const availabilitySettingsToInsert = filteredDays.map(({ day }) => ({
            day,
            section_id: section.id,
        }));
        console.log('Batch inserting into sections_Availability_Settings:', availabilitySettingsToInsert);

        const { data: sectionAvailabilities, error: availabilityError } = await supabase
            .from('section_availability_settings')
            .insert(availabilitySettingsToInsert)
            .select('id, day');

        if (availabilityError || !sectionAvailabilities) {
            console.error('Error inserting into section_availability_settings:', availabilityError);
            throw new Error(`Error inserting into section_availability_settings: ${availabilityError?.message || 'No data returned'}`);
        }
        console.log('section_availability_settings inserted successfully:', sectionAvailabilities);

        // Create a map of day to availability setting ID for quick lookup
        const dayToSettingId = sectionAvailabilities.reduce((acc, { id, day }) => {
            acc[day] = id;
            return acc;
        }, {});
        console.log('Day to setting ID mapping:', dayToSettingId);

        // Prepare batch inserts for availability times
        const availabilityTimesToInsert = filteredDays.flatMap(({ day, times }) =>
            times.map(({ from, to }) => ({
                setting_id: dayToSettingId[day],
                type: 'section',
                available_from: from,
                available_to: to,
            }))
        );
        console.log('Batch inserting into AvaliabilityTimes:', availabilityTimesToInsert);

        const { data: availabilityTimes, error: timesError } = await supabase
            .from('availability_times')
            .insert(availabilityTimesToInsert)
            .select('id');

        if (timesError || !availabilityTimes) {
            console.error('Error inserting into AvaliabilityTimes:', timesError);
            throw new Error(`Error inserting into AvaliabilityTimes: ${timesError?.message || 'No data returned'}`);
        }
        console.log('AvaliabilityTimes inserted successfully:', availabilityTimes);

        // Return success response
        console.log('All data inserted successfully');
        return new Response(
            JSON.stringify({
                message: 'All data inserted successfully',
                results: availabilityTimes,
                id:section.id
            }),
            {
                headers: {
                    ...corsHeaders,
                    'Content-Type': 'application/json',
                },
                status: 200,
            }
        );
    } catch (error) {
        console.error('Error occurred:', error.message);
        return new Response(
            JSON.stringify({ error: error.message }),
            {
                headers: {
                    ...corsHeaders,
                    'Content-Type': 'application/json',
                },
                status: 400,
            }
        );
    }
}