import {createClient} from 'npm:@supabase/supabase-js@2.39.3';
import hasUserPermission from "../../src/utils/hasPermission.ts";
const supabase = createClient(
    Deno.env.get('SUPABASE_URL')!,
    Deno.env.get('SUPABASE_ANON_KEY')!
);
const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
    'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
    'Access-Control-Max-Age': '86400',
    'Content-Type': 'application/json'
};

export default async function deleteBuilding(req: Request) {
    try {
        const {buildingId, user_id} = await req.json();
        const headers = {
            'Access-Control-Allow-Origin': '*', // Replace with your frontend URL
            'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
            'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
            'Access-Control-Max-Age': '86400',
            'Content-Type': 'application/json'            // ... other headers
        };
        if (req.method === 'OPTIONS') {
            return new Response(null, {headers});
        }
        if (!buildingId || typeof buildingId !== 'string') {
            return new Response(
                JSON.stringify({error: 'Invalid or missing buildingId.'}),
                {status: 400, headers: corsHeaders}
            );
        }

        console.log('Received buildingId:', buildingId);
        const {data: user_roles, error: userError} = await supabase
            .from('users')
            .select('* , role_id(name,role_permissions(permission_id(name)))')
            .eq('user_auth_id', user_id)
            .single();

        if (userError || !user_roles) {
            return new Response(
                JSON.stringify({error: `User with ID ${user_id} does not exist.`}),
                {status: 404, headers: corsHeaders}
            );
        }
        console.log('user_roles:', user_roles.role_id.name);
        console.log('user:', user_roles);
        const role = user_roles.role_id.name;
        const permission = await hasUserPermission(user_roles, "Delete a Building");
        console.log('permission:', permission);
        if (!permission) {
            return new Response(
                JSON.stringify({
                    error: `doesn’t have the required permission`,
                    statusError: 455,
                    permission: user_roles.role_id.name
                }),
                {status: 404, headers: corsHeaders}
            );
        }
        // Check if the building exists
        const {data: building, error: buildingError} = await supabase
            .from('sections')
            .select('*')
            .eq('id', buildingId)
            .single();

        if (buildingError || !building) {
            return new Response(
                JSON.stringify({error: `Building with ID ${buildingId} does not exist.`}),
                {status: 404, headers: corsHeaders}
            );
        }

        console.log('Building found:', building);
        const deleted_at = building.deleted_at;
        if (deleted_at) {
            return new Response(
                JSON.stringify({error: `Building with ID ${buildingId} is already deleted.`}),
                {status: 404, headers: corsHeaders}
            );
        }
        // Check for linked rooms
        const {data: linkedRooms, error: roomError} = await supabase
            .from('rooms')
            .select('*, status_id(*)')  // Fetch related 'status' table data
            .is("deleted_at",null)
            .eq('section_id', buildingId);

        console.log('Linked rooms:', linkedRooms);
        // const {data: roomSlots, error: slotError} = await supabase
        //     .from('room_slots')
        //     .select('*')
        //     .neq("deleted_at",null)
        //     .in('room_id', linkedRooms.map(room => room.id));

        // console.log('Linked room slots:', roomSlots);

        if (linkedRooms && linkedRooms.length > 0) {
            console.log('Linked rooms found:', linkedRooms);
            return new Response(
                JSON.stringify({
                    statusError: 454,
                    error: `building_linked_rooms`,
                    linkedRooms
                }),
                {status: 400, headers: corsHeaders}
            );
        }
        //
        // if (roomSlots && roomSlots.length > 0) {
        //     console.log('Linked room slots found:', roomSlots);
        //     return new Response(
        //         JSON.stringify({
        //             statusError: 455,
        //             error: `building_linked_visits`,
        //             roomSlots
        //         }),
        //         {status: 400, headers: corsHeaders}
        //     );
        // }
        if (roomError) {
            return new Response(
                JSON.stringify({error: `Error checking linked rooms or slots. ${roomError.message}`}),
                {status: 500, headers: corsHeaders}
            );

        }

        const { data: linkedInmates, error: InmatesError } = await supabase
            .from('inmates')
            .select('full_name')
            .eq('section_id', buildingId);

        if (InmatesError) {
            return new Response(
                JSON.stringify({ error: `Error checking linked inmates.  :${InmatesError.message}` }),
                { status: 500, headers: corsHeaders }
            );
        }
        //
        if (linkedInmates && linkedInmates.length > 0) {
            console.log('Linked Inmates found:', linkedInmates);
            return new Response(
                JSON.stringify({
                    statusError : 457,
                    error: `building_linked_inmates`,
                    linkedInmates
                }),
                { status: 400, headers: corsHeaders }
            );
        }
        // set `deleted_at` for the building
        const {error: updateError} = await supabase
            .from('sections')
            .update({
                deleted_at: new Date().toISOString()
            })
            .eq('id', buildingId);

        if (updateError) {
            return new Response(
                JSON.stringify({error: 'Error updating the deleted_at for the building.'}),
                {status: 500, headers: corsHeaders}
            );
        }

        console.log('Building marked as deleted and timestamp set successfully.');

        console.log('Building marked as deleted successfully.');

        return new Response(
            JSON.stringify({message: `Building with ID ${buildingId} marked as deleted successfully.`}),
            {status: 200, headers: corsHeaders}
        );
    } catch (error) {
        console.error('Error:', error.message);
        return new Response(
            JSON.stringify({error: error.message}),
            {status: 500, headers: corsHeaders}
        );
    }
}
