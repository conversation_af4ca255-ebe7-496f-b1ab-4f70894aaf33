import { createClient } from 'npm:@supabase/supabase-js@2.39.3';
import hasUserPermission from "../../src/utils/hasPermission.ts";

const supabase = createClient(
  Deno.env.get('SUPABASE_URL')!,
  Deno.env.get('SUPABASE_ANON_KEY')!
);

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Max-Age': '86400',
  'Content-Type': 'application/json'
};

export default async function editBuilding(req: Request) {
  const isValidTimeFormat = (time: string): boolean =>
    /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$/.test(time);


  const timeToMinutes = (time: string): number => {
    const [hours, minutes, seconds] = time.split(':').map(Number);
    return hours * 60 + minutes;
  };

// Helper function to check if two time ranges overlap
  // const timeRangesOverlap = (range1: { from: string, to: string }, range2: { from: string, to: string }): boolean => {
  //   const start1 = timeToMinutes(range1.from);
  //   const end1 = timeToMinutes(range1.to);
  //   const start2 = timeToMinutes(range2.from);
  //   const end2 = timeToMinutes(range2.to);
    
  //   return start1 < end2 && end1 > start2;
  // };
  // Helper function to check if two time ranges overlap
  // const timeRangesOverlap = (range1: { from: string, to: string }, range2: { from: string, to: string }): boolean => {
  //   const start1 = timeToMinutes(range1.from);
  //   const end1 = timeToMinutes(range1.to);
  //   const start2 = timeToMinutes(range2.from);
  //   const end2 = timeToMinutes(range2.to);
    
  //   return start1 < end2 && end1 > start2;
  // };
  const timeRangesOverlap = (range1: { from: string, to: string }, range2: { from: string, to: string }): boolean => {
    const [start1, end1] = [range1.from, range1.to].map(timeToMinutes);
    const [start2, end2] = [range2.from, range2.to].map(timeToMinutes);
    
    // Ensure from time is always before to time
    if (start1 >= end1 || start2 >= end2) {
      throw new Error("Invalid time range - from time must be before to time");
    }
  
    // Check if ranges overlap
    return start1 < end2 && end1 > start2;
  };
  
  const validateEditInput = (section_id: any, buildingCode: any, buildingAvailability: any, filteredDays: any[]) => {
    if (!section_id || typeof section_id !== 'string') {
      throw new Error('Invalid section_id: must be a valid string.');
    }
    if (!buildingCode || typeof buildingCode !== 'string') {
      throw new Error('Invalid buildingCode: must be a non-empty string.');
    }
    if (typeof buildingAvailability !== 'boolean') {
      throw new Error('Invalid buildingAvailability: must be a boolean.');
    }
    if (!Array.isArray(filteredDays) || filteredDays.length === 0) {
      throw new Error('Invalid filteredDays: must be a non-empty array.');
    
    // for (const { day, times, is_available } of filteredDays) {
    //   if (!day || typeof day !== 'string') {
    //     throw new Error('Invalid day: must be a non-empty string.');
    //   }
    //   if (typeof is_available !== 'boolean') {
    //     throw new Error(`Invalid is_available for day ${day}: must be a boolean.`);
    //   }
    //   if (!is_available && (!Array.isArray(times) || times.length !== 0)) {
    //     throw new Error(`Invalid times for unavailable day ${day}: must be an empty array.`);
    //   }
    //   if (is_available && (!Array.isArray(times) || times.length === 0)) {
    //     throw new Error(`Invalid times for available day ${day}: must be a non-empty array.`);
    //   }
    //   for (const { from, to } of times || []) {
    //     if (!from || !to || !isValidTimeFormat(from) || !isValidTimeFormat(to)) {
    //       throw new Error(`Invalid time format in times for day ${day}: must be in HH:mm:ss format.`);
    //     }
    //   }
    // }
}

    // for (const { day, times, is_available } of filteredDays) {
    //   if (!day || typeof day !== 'string') {
    //     throw new Error('Invalid day: must be a non-empty string.');
    //   }
    //   if (typeof is_available !== 'boolean') {
    //     throw new Error(`Invalid is_available for day ${day}: must be a boolean.`);
    //   }
    //   if (!is_available && (!Array.isArray(times) || times.length !== 0)) {
    //     throw new Error(`Invalid times for unavailable day ${day}: must be an empty array.`);
    //   }
    //   if (is_available && (!Array.isArray(times) || times.length === 0)) {
    //     throw new Error(`Invalid times for available day ${day}: must be a non-empty array.`);
    //   }
    //   for (const { from, to } of times || []) {
    //     if (!from || !to || !isValidTimeFormat(from) || !isValidTimeFormat(to)) {
    //       throw new Error(`Invalid time format in times for day ${day}: must be in HH:mm:ss format.`);
    //     }
    //   }
    // }
  };
  async function checkTimeConflicts(section_id: string, filteredDays: any[]) {
    // Get only the days that are being made available
    const availableDays = filteredDays
      .filter(day => day.is_available === true)
      .map(day => day.day.toLowerCase());

    if (availableDays.length === 0) {
      return null; // No available days to check
    }

    // Get all active visits for this section
    const { data: linkedVisits, error: visitError } = await supabase
      .from('rooms')
      .select('*, visit_requests(*, status_id(*))')
      .eq('section_id', section_id);

    if (visitError) {
      console.error("Error fetching visits:", visitError);
      throw new Error(`Error checking visits: ${visitError.message}`);
    }

    // Flatten all visit requests and filter active ones
    const activeVisits = linkedVisits
      .flatMap(room => room.visit_requests)
      .filter(visit => 
        !["missed", "completed", "ended", "denied", "admin_cancelled", "visitor_cancelled"].includes(visit.status_id.name)
      );

    // Check each visit against the new availability times
    const conflicts = [];
    
    for (const visit of activeVisits) {
      const visitDate = new Date(visit.datetime);
      // Use UTC to avoid timezone issues
      const visitDay = visitDate.toLocaleDateString('en-US', { 
        weekday: 'long', 
        timeZone: 'UTC' 
      }).toLowerCase();
  
      // Get visit time in UTC
      const visitStart = new Date(visit.datetime).toISOString().split('T')[1].slice(0, 8);
      const visitEnd = new Date(
        visitDate.getTime() + visit.duration * 60000
      ).toISOString().split('T')[1].slice(0, 8);
  
      const visitTime = {
        from: visitStart,
        to: visitEnd
      };
  
      // Find matching day in filteredDays
      const dayConfig = filteredDays.find(d => 
        d.day.toLowerCase() === visitDay && d.is_available
      );
      
      if (dayConfig) {
        for (const timeRange of dayConfig.times) {
          try {
            if (timeRangesOverlap(timeRange, visitTime)) {
              conflicts.push({
                visit_id: visit.id,
                room_id: visit.room_id,
                visit_datetime: visit.datetime,
                visit_duration: visit.duration,
                conflicting_day: dayConfig.day,
                conflicting_time: timeRange,
                visit_time: visitTime // Added for debugging
              });
              break;
            }
          } catch (error) {
            console.error("Invalid time range detected:", error);
          }
        }
      }
    }
  
    return conflicts.length > 0 ? conflicts : null;
  }

  async function checkVisitsOnUnavailableDays(section_id: string, filteredDays: any[]) {
    const unavailableDays = filteredDays
      .filter(day => day.is_available === false)
      .map(day => day.day.toLowerCase());

    if (unavailableDays.length === 0) {
      return null;
    }

    const { data: linkedVisits, error: visitError } = await supabase
      .from('rooms')
      .select('*, status_id(*), visit_requests(*, status_id(*))')
      .eq('section_id', section_id);

    if (visitError) {
      console.error("Error fetching visits:", visitError);
      throw new Error(`Error checking visits: ${visitError.message}`);
    }

    const problematicVisits = linkedVisits
      .flatMap(room => 
        room.visit_requests
          .filter(visit => {
            const visitDay = new Date(visit.datetime).toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase();
            return (
              unavailableDays.includes(visitDay) &&
              !["missed", "completed", "ended", "denied", "admin_cancelled", "visitor_cancelled"].includes(visit.status_id.name)
            );
          })
          .map(visit => ({
            room_id: room.id,
            room_name: room.name,
            ...visit
          }))
      );

    return problematicVisits.length > 0 ? problematicVisits : null;
  }

  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 204,
      headers: {
        ...corsHeaders,
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Authorization, X-Client-Info, apikey, Content-Type'
      }
    });
  }

  try {
    const { section_id, buildingCode, buildingAvailability, filteredDays, user_id } = await req.json();

    // Validate input
    validateEditInput(section_id, buildingCode, buildingAvailability, filteredDays);

    // Ensure the section_id is valid
    const { data: sectionData, error: sectionError } = await supabase
      .from('sections')
      .select('id')
      .eq('id', section_id)
      .single();

    if (sectionError || !sectionData) {
      throw new Error(`Invalid section_id: ${section_id} does not exist.`);
    }

    const { data: user_roles, error: userError } = await supabase
      .from('users')
      .select('* , role_id(name,role_permissions(permission_id(name)))')
      .eq('user_auth_id', user_id)
      .single();

    if (userError || !user_roles) {
      return new Response(
        JSON.stringify({ error: `User with ID ${user_id} does not exist.` }),
        { status: 404, headers: corsHeaders }
      );
    }

    const permission = await hasUserPermission(user_roles, "Edit a Building");
    if (!permission) {
      return new Response(
        JSON.stringify({ 
          error: `doesn't have the required Permission`,
          statusError: 455,
          permission: user_roles.role_id.name
        }),
        { status: 404, headers: corsHeaders }
      );
    }
      // Check if building code already exists for a different building
    const { data: existingBuilding, error: existingBuildingError } = await supabase
    .from('sections')
    .select('id')
    .eq('name', buildingCode)
    .neq('id', section_id) // Exclude the current building
    .is("deleted_at",null)
    .single();

    if (existingBuildingError && existingBuildingError.code !== 'PGRST116') {
      throw new Error(`Error checking building code: ${existingBuildingError.message}`);
    }

    if (existingBuilding) {
      return new Response(
        JSON.stringify({
          error: 'Building code already exists',
          statusError: 459,
        }),
        { status: 409, headers: corsHeaders }
      );
    }

    // Check for visits on days being marked as unavailable
    // const problematicVisits = await checkVisitsOnUnavailableDays(section_id, filteredDays);
    // if (problematicVisits) {
    //   return new Response(
    //     JSON.stringify({
    //       statusError: 456,
    //       error: `visits_on_unavailable_days`,
    //       problematicVisits
    //     }),
    //     { status: 400, headers: corsHeaders }
    //   );
    // }

    // const timeConflicts = await checkTimeConflicts(section_id, filteredDays);
    // if (timeConflicts) {
    //   return new Response(
    //     JSON.stringify({
    //       statusError: 457,
    //       error: `time_conflicts_with_existing_visits`,
    //       timeConflicts
    //     }),
    //     { status: 400, headers: corsHeaders }
    //   );
    // }

    
    if (buildingAvailability === false) {
      const { data: linkedRooms, error: roomError } = await supabase
        .from('rooms')
        .select('*, status_id(*)')
        .eq('section_id', section_id)
        .is("deleted_at",null);

      if (roomError) {
        return new Response(
          JSON.stringify({ error: `Error checking linked rooms. ${roomError.message}` }),
          { status: 500, headers: corsHeaders }
        );
      }

      const { data: linkedVisits, error: visitError } = await supabase
        .from('rooms')
        .select('*, status_id(*), visit_requests(*, status_id(*))')
        .eq('section_id', section_id)
        .is("deleted_at",null);

      if (visitError) {
        console.error("Error fetching visits:", visitError);
      } else {
        const filteredVisits = linkedVisits
          .map(room => ({
            ...room,
            visit_requests: room.visit_requests.filter(visit =>
              !["missed", "completed", "ended", "denied", "admin_cancelled", "visitor_cancelled"].includes(visit.status_id.name)
            )
          }))
          .filter(room => room.visit_requests.length > 0);

        if (filteredVisits && filteredVisits.length > 0) {
          return new Response(
            JSON.stringify({
              statusError: 454,
              error: `building_linked_visits`,
              filteredVisits
            }),
            { status: 400, headers: corsHeaders }
          );
        }
      }

      if (linkedRooms && linkedRooms.length > 0) {
        return new Response(
          JSON.stringify({
            statusError: 454,
            error: `building_linked_rooms`,
            linkedRooms
          }),
          { status: 400, headers: corsHeaders }
        );
      }
    }

    // Update section details
    const { error: updatesectionsError } = await supabase
      .from('sections')
      .update({ name: buildingCode, is_available: buildingAvailability })
      .eq('id', section_id);

    if (updatesectionsError) {
      throw new Error(`Error updating sections table: ${updatesectionsError.message}`);
    }

    // Fetch all relevant setting IDs
    const { data: settingsData, error: fetchSettingsError } = await supabase
      .from('section_availability_settings')
      .select('id')
      .eq('section_id', section_id);

    if (fetchSettingsError) {
      throw new Error(`Error fetching availability settings: ${fetchSettingsError.message}`);
    }

    const settingIds = settingsData.map(({ id }: { id: number }) => id);

    // Delete existing availability times
    if (settingIds.length > 0) {
      const { error: deleteTimesError } = await supabase
        .from('availability_times')
        .delete()
        .in('setting_id', settingIds);

      if (deleteTimesError) {
        throw new Error(`Error deleting availability times: ${deleteTimesError.message}`);
      }
    }

    // Delete old settings
    const { error: deleteSettingsError } = await supabase
      .from('section_availability_settings')
      .delete()
      .eq('section_id', section_id);

    if (deleteSettingsError) {
      throw new Error(`Error deleting old section_availability_settings: ${deleteSettingsError.message}`);
    }

    // Insert new settings
    const availabilitySettingsToInsert = filteredDays.map(({ day }) => ({
      section_id: section_id,
      day,
    }));

    const { data: sectionAvailabilities, error: availabilityError } = await supabase
      .from('section_availability_settings')
      .insert(availabilitySettingsToInsert)
      .select('id, day');

    if (availabilityError || !sectionAvailabilities) {
      throw new Error(`Error inserting into section_availability_settings: ${availabilityError?.message || 'No data returned'}`);
    }

    const dayToSettingId = sectionAvailabilities.reduce((acc, { id, day }) => {
      acc[day] = id;
      return acc;
    }, {} as Record<string, number>);

    // Insert new availability times (only for available days)
    const availabilityTimesToInsert = filteredDays.flatMap(({ day, times }) =>
      times.map(({ from, to }) => ({
        setting_id: dayToSettingId[day],
        type: 'section',
        available_from: from,
        available_to: to,
              }))
    );

    if (availabilityTimesToInsert.length > 0) {
      const { error: timesError } = await supabase
        .from('availability_times')
        .insert(availabilityTimesToInsert);

      if (timesError) {
        throw new Error(`Error inserting into AvailabilityTimes: ${timesError.message}`);
      }
    }

    return new Response(
      JSON.stringify({ message: 'sections and availability updated successfully' }),
      {
        headers: {
          ...corsHeaders,
          'Access-Control-Allow-Origin': '*',
          'Content-Type': 'application/json'
        },
        status: 200,
      }
    );
  } catch (error) {
    const errorMessage = (error as Error).message || 'An unknown error occurred.';
    return new Response(
      JSON.stringify({
        error: 'An error occurred while processing your request.',
        message: errorMessage,
        status_code: 400,
        headers: {
          'content_length': req.headers.get('content-length'),
          'content_type': req.headers.get('content-type'),
          'date': req.headers.get('date'),
          'server': req.headers.get('server'),
        },
        hint: 'Please verify your request body and ensure all required fields are included.',
      }),
      {
        headers: {
          ...corsHeaders,
          'Access-Control-Allow-Origin': '*',
          'Content-Type': 'application/json'
        },
        status: 400,
      }
    );
  }
}