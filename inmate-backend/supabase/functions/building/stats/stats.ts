import { createClient } from "npm:@supabase/supabase-js@2.39.3";

const supabase = createClient(Deno.env.get("SUPABASE_URL")!, Deno.env.get("SUPABASE_ANON_KEY")!);
const corsHeaders = {
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Methods": "POST, GET, OPTIONS",
    "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
    "Access-Control-Max-Age": "86400",
    "Content-Type": "application/json",
};

export default async function getStats(queryParams: Record<string, string | null>) {
    const section_id = queryParams.section_id;

    if (!section_id) {
        console.log("Missing section_id parameter");
        return new Response(
            JSON.stringify({ error: "Missing 'section_id' parameter" }),
            {
                status: 400,
                headers: { ...corsHeaders, "Content-Type": "application/json" },
            }
        );
    }

    try {
        console.log("Fetching data for section_id:", section_id);
        const { data: sectionData, error: sectionError } = await supabase
            .from('sections')
            .select(`
                *,
                inmates(id),
                section_availability_settings(id, day)
            `)
            .eq('id', section_id)
            .eq('is_available', true)
            .single();
        console.log("Section Data:", sectionData);
        if (!sectionData) {
            return new Response(
                JSON.stringify(

                    {
                    message: "No data found for the given section_id",
                    rooms_count: 0,
                    inmates_count: 0,
                    building_available_slots: 0,
                    building_default_room_count: 0,
                    building_default_room_slots: 0,
                    normal_room_count: 0,
                    normal_room_slots: 0,
                    room_available_slots: 0,
                    available_slots: 0,
                    unavailable_slots: 0,
                    total_slots: 0
                }),
                {
                    status: 200,
                    headers: { ...corsHeaders, "Content-Type": "application/json" },
                }
            );
        }

        if (sectionError) {
            throw new Error(`Error fetching Section details: ${sectionError.message}`);
        }

        const roomsData = await supabase
            .from('rooms')
            .select('id, is_building_default')
            .eq('section_id', section_id)
            .eq('active', true);

        const inmatesCount = sectionData.inmates.length;
        const settingsData = sectionData.section_availability_settings;

        const settingIds = settingsData.map(({ id }) => id);

        const { data: timesData, error: timesError } = await supabase
            .from('availability_times')
            .select('setting_id, available_from, available_to')
            .in('setting_id', settingIds);

        if (timesError) {
            throw new Error(`Error fetching availability_times: ${timesError.message}`);
        }

        const { data: roomSettingsData, error: roomSettingsError } = await supabase
            .from('room_availability_settings')
            .select('id, room_id, day, is_available')
            .in('room_id', roomsData.data.map(room => room.id))
            .eq('is_available', true);

        if (roomSettingsError) {
            throw new Error(`Error fetching room_availability_settings: ${roomSettingsError.message}`);
        }

        const roomSettingIds = roomSettingsData.map(({ id }) => id);

        const { data: roomTimesData, error: roomTimesError } = await supabase
            .from('availability_times')
            .select('setting_id, available_from, available_to')
            .in('setting_id', roomSettingIds);

        if (roomTimesError) {
            throw new Error(`Error fetching room availability_times: ${roomTimesError.message}`);
        }

        const totalSlots = settingsData.reduce((total, setting) => {
            const times = timesData.filter(time => time.setting_id === setting.id);
            return total + times.length;
        }, 0);

        const { count: unavailableSlotsCount, error: unavailableSlotsError } = await supabase
            .from('room_availability_settings')
            .select('*', { count: 'exact', head: true })
            .eq('is_available', false)
            .in('room_id', roomsData.data.map(room => room.id))

        if (unavailableSlotsError) {
            throw new Error(`Error fetching unavailable slots: ${unavailableSlotsError.message}`);
        }

        const availableSlots = totalSlots - (unavailableSlotsCount || 0);

        let normalRoomSlots = 0;
        let buildingDefaultRoomCount = 0;
        let normalRoomCount = 0;

        roomsData.data.forEach(room => {
            if (room.is_building_default) {
                buildingDefaultRoomCount++;
            } else {
                normalRoomCount++;
                const roomSlots = roomSettingsData
                    .filter(info => info.room_id === room.id)
                    .reduce((total, setting) => {
                        const times = roomTimesData.filter(time => time.setting_id === setting.id);
                        return total + times.length;
                    }, 0);
                normalRoomSlots += roomSlots;
            }
        });

        const buildingDefaultRoomSlots = buildingDefaultRoomCount * totalSlots;
        const totalRoomSlots = buildingDefaultRoomSlots + normalRoomSlots;

        const unavailableSlots = totalSlots - availableSlots;

        console.log("Building default rooms:", buildingDefaultRoomCount);
        console.log("Normal rooms:", normalRoomCount);
        console.log("Building default room slots:", buildingDefaultRoomSlots);
        console.log("Normal room slots:", normalRoomSlots);
        console.log("Total room slots:", totalRoomSlots);
        console.log("Available slots:", availableSlots);
        console.log("Unavailable slots:", unavailableSlots);

        return new Response(
            JSON.stringify({
                rooms_count: roomsData.data.length,
                inmates_count: inmatesCount,
                building_available_slots: totalSlots,
                building_default_room_count: buildingDefaultRoomCount,
                building_default_room_slots: buildingDefaultRoomSlots,
                normal_room_count: normalRoomCount,
                normal_room_slots: normalRoomSlots,
                room_available_slots: totalRoomSlots,
                available_slots: availableSlots,
                unavailable_slots: unavailableSlots,
                total_slots: totalSlots
            }),
            {
                status: 200,
                headers: { ...corsHeaders, "Content-Type": "application/json" },
            }
        );
    } catch (error) {
        console.error("Error fetching building details:", error.message);
        return new Response(
            JSON.stringify({ error: `Internal Server Error  : \`${error}\`` }),
            {
                status: 500,
                headers: { ...corsHeaders, "Content-Type": "application/json" }
            }
        );
    }
}