// Follow this setup guide to integrate the Deno language server with your editor:
// https://deno.land/manual/getting_started/setup_your_environment
// This enables autocomplete, go to definition, etc.

// Setup type definitions for built-in Supabase Runtime APIs
import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import {createClient} from "npm:@supabase/supabase-js@2.39.3"
// import { PrismaClient } from '@prisma/client';
// //
// // // Initialize Prisma client
// const prisma = new PrismaClient();
const supabase = createClient(
    Deno.env.get("SUPABASE_URL")!,
    Deno.env.get("SUPABASE_ANON_KEY")!,
)
console.log("Hello from SUPABASE_URL!", Deno.env.get("SUPABASE_URL"))
console.log("Hello from aON!", Deno.env.get("SUPABASE_ANON_KEY"))

Deno.serve(async (req) => {
  if (req.method === "POST") {
    try {
      // Parse the incoming request body
      const { id,full_name, national_id, age,number_of_visits,section,unavailable} = await req.json();
      console.log("hgshvdfhjdvfb",full_name, national_id, age,number_of_visits,section,unavailable);
      // Insert data into the "Inmate" table
      const { data, error } = await supabase
          .from("inmates")
          .insert([{id, full_name, national_id, age,number_of_visits,section,unavailable}]);

      if (error) {
        console.error("Error inserting data:", error);
        return new Response(
            JSON.stringify({ error: "Failed to create record" }),
            { status: 500, headers: { "Content-Type": "application/json" } }
        );
      }

      // Return the response with the inserted data
      return new Response(
          JSON.stringify({ message: "Record created successfully", data }),
          { status: 201, headers: { "Content-Type": "application/json" } }
      );
    } catch (error) {
      console.error("Error processing request:", error);
      return new Response(  
          JSON.stringify({ error: "Failed to process request" }),
          { status: 400, headers: { "Content-Type": "application/json" } }
      );
    }
  } else {
    return new Response(
        JSON.stringify({ error: "Method not allowed" }),
        { status: 405, headers: { "Content-Type": "application/json" } }
    );
  }
});

/* To invoke locally:

  1. Run `supabase start` (see: https://supabase.com/docs/reference/cli/supabase-start)
  2. Make an HTTP request:

  curl -i --location --request POST 'http://127.0.0.1:54321/functions/v1/create' \
    --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0' \
    --header 'Content-Type: application/json' \
    --data '{"name":"Functions"}'

*/
