import 'https://esm.sh/@supabase/functions-js/src/edge-runtime.d.ts';
import { createClient } from 'npm:@supabase/supabase-js@2.39.3';
import hasUserPermission from "../src/utils/hasPermission.ts";
const supabase = createClient(Deno.env.get('SUPABASE_URL')!, Deno.env.get('SUPABASE_ANON_KEY')!);
import { withLogging } from '../src/utils/logger.ts';  // Change from default import to named import

export const corsHeaders = {
  'Access-Control-Allow-Origin': '*', // Replace with your frontend URL
  'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Max-Age': '86400',
  'Content-Type': 'application/json',
};

Deno.serve(withLogging(async (req) => {
  try {
    const { role_id , user_id } = await req.json();
    const headers = {
      'Access-Control-Allow-Origin': '*', // Replace with your frontend URL
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
      'Access-Control-Max-Age': '86400',
      'Content-Type': 'application/json'            // ... other headers
      // ... other headers
    };
    if (req.method === 'OPTIONS') {
      return new Response(null, { headers });
    }
    if (!role_id || typeof role_id !== 'string') {
      return new Response(
          JSON.stringify({ error: 'Invalid or missing role_id.' }),
          { status: 400, headers: corsHeaders }
      );
    }

    console.log('Received role_id:', role_id);

    // Check if the role exists
    const { data: role, error: roleError } = await supabase
        .from('roles')
        .select('*')
        .eq('id', role_id)
        .single();
    console.log('role:', role);
    if (roleError || !role) {
      return new Response(
          JSON.stringify({ error: `role with ID ${role_id} does not exist.` }),
          { status: 404, headers: corsHeaders }
      );
    }

    console.log('role found:', role);
    const { data: user_roles, error: user_role_error } = await supabase
        .from('users')
        .select('* , role_id(name,role_permissions(permission_id(name)))')
        .eq('user_auth_id', user_id)
        .single();

    if (user_role_error || !user_roles) {
      return new Response(
          JSON.stringify({ error: `User with ID ${user_id} does not exist.` }),
          { status: 404, headers: corsHeaders }
      );
    }
    console.log('user_roles:', user_roles.role_id.name);
    console.log('user:', user_roles);
    const user_role = user_roles.role_id.name;
    const permsission = await hasUserPermission(user_roles, 'Delete Role');
    console.log("permsission",permsission)
    if (!permsission) {
      return new Response(
        JSON.stringify({ error: `doesn’t have the required permission`,
                        statusError:455,
                        permission: user_roles.role_id.name}),
                        { status: 404, headers: corsHeaders }
    );
    }
    // Check for linked users
    const { data: linkedusers, error: userError } = await supabase
        .from('users')
        .select('role_id(id , name), full_name')
        .eq('role_id', role_id);

    if (userError) {
      return new Response(
          JSON.stringify({ error: 'Error checking linked users.' }),
          { status: 500, headers: corsHeaders }
      );
    }

    const deleted_at = role.deleted_at

    if (deleted_at){
      return new Response(
          JSON.stringify({ error: `role with ID ${role_id} is already deleted.` }),
          { status: 404, headers: corsHeaders }
      );

    }
    if (linkedusers && linkedusers.length > 0) {
      console.log('Linked users found:', linkedusers);
      return new Response(
          JSON.stringify({
            error: `role with ID ${role_id} has linked users. Please resolve linked users before deleting.`,
            linkedusers,
            statusError:459
          }),
          { status: 404, headers: corsHeaders }
        );
    }
    // Update the `is_deleted` field and set `deleted_at` for the role
    const { error: updateError } = await supabase
        .from('roles')
        .update({
          deleted_at: new Date().toISOString()
        })
        .eq('id', role_id);

    if (updateError) {
      return new Response(
          JSON.stringify({ error: 'Error updating the deleted_at for the role.' }),
          { status: 500, headers: corsHeaders }
      );
    }

    console.log('role marked as deleted and timestamp set successfully.');

    console.log('role marked as deleted successfully.');

    return new Response(
        JSON.stringify({ message: `role with ID ${role_id} marked as deleted successfully.` }),
        { status: 200, headers: corsHeaders }
    );
  } catch (error) {
    console.error('Error:', error.message);
    return new Response(
        JSON.stringify({ error: error.message }),
        { status: 500, headers: corsHeaders }
    );
  }
},"delete_role"));
