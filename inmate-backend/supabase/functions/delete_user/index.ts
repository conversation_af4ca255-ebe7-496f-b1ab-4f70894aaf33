import 'https://esm.sh/@supabase/functions-js/src/edge-runtime.d.ts';
import { createClient } from 'npm:@supabase/supabase-js@2.39.3';
import hasUserPermission from "../src/utils/hasPermission.ts";
const supabase = createClient(Deno.env.get('SUPABASE_URL')!, Deno.env.get('SUPABASE_ANON_KEY')!);
import { withLogging } from '../src/utils/logger.ts';  // Change from default import to named import

export const corsHeaders = {
  'Access-Control-Allow-Origin': '*', // Replace with your frontend URL
  'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Max-Age': '86400',
  'Content-Type': 'application/json',
};

Deno.serve(withLogging(async (req) => {
  try {
    const { id , user_id } = await req.json();
    const tbd_user = id;
    const headers = {
      'Access-Control-Allow-Origin': '*', // Replace with your frontend URL
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
      'Access-Control-Max-Age': '86400',
      'Content-Type': 'application/json'            // ... other headers
      // ... other headers
    };
    if (req.method === 'OPTIONS') {
      return new Response(null, { headers });
    }
    if (!tbd_user || typeof tbd_user !== 'string') {
      return new Response(
          JSON.stringify({ error: 'Invalid or missing tbd_user.' }),
          { status: 400, headers: corsHeaders }
      );
    }
    console.log('Received tbd_user:', tbd_user);
    // Check if the user exists

    const { data: user, error: userError } = await supabase
        .from('users')
        .select('*')
        .eq('user_auth_id', tbd_user)
        .single();
    console.log('user:', user);
    if (userError || !user) {
      return new Response(
          JSON.stringify({ error: `user with ID ${tbd_user} does not exist.` }),
          { status: 404, headers: corsHeaders }
      );
    }
    console.log('user found:', user);

    const { data: user_users, error: user_user_error } = await supabase
        .from('users')
        .select('* , role_id(name,role_permissions(permission_id(name)))')
        .eq('user_auth_id', user_id)
        .single();

    console.log('user_users:', user_users);
    if (user_user_error || !user_users) {

      return new Response(
          JSON.stringify({ error: `User with ID ${user_id} does not exist.` }),
          { status: 404, headers: corsHeaders }
      );
    }
    console.log('user_users:', user_users.role_id.name);
    console.log('user:', user_users);
    const user_user = user_users.role_id.name;
    const permsission = await hasUserPermission(user_users, 'Delete Admin');
    console.log("permsission",permsission)
    if (!permsission) {

      return new Response(
                JSON.stringify({ error: `doesn’t have the required permission`,
                                statusError : 455,
                                permission : user_user}),
                                { status: 404, headers: corsHeaders }
            );
    }
    if (tbd_user === user_id) {
      return new Response(
          JSON.stringify({ error: 'You cannot delete yourself.' }),
          { status: 400, headers: corsHeaders }
      );
    }

    const deleted_at = user.deleted_at

    if (deleted_at){
      return new Response(
          JSON.stringify({ error: `user with ID ${tbd_user} is already deleted.` }),
          { status: 404, headers: corsHeaders }
      );

    }
    // const { data: ban_user, error } = await supabase.auth.admin.updateUserById(
    //     'a4c23475-3109-4155-8f3d-206dae13f442',
    //     { banned_until: "2025-06-30T12:00:00.0Z" }
    // );
    // console.log('ban_user:', ban_user);
    // if (error) {
    //   return new Response(
    //       JSON.stringify({ error: `Error banning the user.  ${error.message}` }),
    //       { status: 500, headers: corsHeaders }
    //   );
    // }
    // set `deleted_at` for the user
    const { error: updateError } = await supabase
        .from('users')
        .update({
          deleted_at: new Date().toISOString()
        })
        .eq('user_auth_id', tbd_user);


    if (updateError) {
      console.log(updateError)
      return new Response(
          
          JSON.stringify({ error: `Error updating the deleted_at for the user.  ${updateError.message}` }),
          { status: 500, headers: corsHeaders }
      );
    }

    console.log('user marked as deleted and timestamp set successfully.');

    console.log('user marked as deleted successfully.');

    return new Response(
        JSON.stringify({ message: `user with ID ${tbd_user} marked as deleted successfully.` }),
        { status: 200, headers: corsHeaders }
    );
  } catch (error) {
    console.error('Error:', error.message);
    return new Response(
        JSON.stringify({ error: error.message }),
        { status: 500, headers: corsHeaders }
    );
  }
}, "delete_user"));
