import { adminSupabase } from "../../src/lib/SupabaseClient.ts";
import { supabase } from "../../src/lib/SupabaseClient.ts";
import { NotFoundError } from "../../src/errors/notFoundError.ts";

type AdminAuthUpdate = {
  password?: string | null;
  email?: string | null;
};

type AdminPublicUpdate = {
  email?: string | null;
  full_name?: string | null;
  role_id?: string | null;
};

export async function updateAdminAuthData(
  id: string,
  password: string | null,
  email: string | null
): Promise<void> {
  const updateObj: AdminAuthUpdate = {};
  if (password) {
    updateObj.password = password;
  }
  if (email) {
    updateObj.email = email;
  }
  const { error } = await adminSupabase.auth.admin.updateUserById(
    id,
    updateObj
  );
  if (error && error.code === "user_not_found") {
    throw new NotFoundError(`User with ID ${id} not found`);
  }
  if (error) {
    throw new Error(`Error updating user password: ${error.message}`);
  }
}

export async function updateAdminPublicData(
  id: string,
  email: string | null,
  name: string | null,
  role_id: string | null
): Promise<void> {
  const updateObj: AdminPublicUpdate = {};
  if (email) {
    updateObj.email = email;
  }
  if (name) {
    updateObj.full_name = name;
  }
  if (role_id) {
    updateObj.role_id = role_id;
  }

  const { data, error } = await supabase
    .from("users")
    .update(updateObj)
    .eq("id", id)
    .select()
    .single();

  if (error) {
    if (error.code === "PGRST116") {
      throw new NotFoundError(`User with ID ${id} not found`);
    }
    throw new Error(`Error fetching user by ID: ${error.message}`);
  }
  if (error) {
    throw new Error(`Error fetching user by ID: ${error.message}`);
  }
  return data;
}

export async function getAdminAuthDataById(id: string): Promise<any> {
  const { data, error } = await adminSupabase.auth.admin.getUserById(id);
  if (error) {
    if (error.code === "user_not_found") {
      throw new NotFoundError(`User with ID ${id} not found`);
    }
    throw new Error(`Error fetching user by ID: ${error.message}`);
  }

  return data.user.email;
}
