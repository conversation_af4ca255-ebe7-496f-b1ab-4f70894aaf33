import {
  updateAdminAuthData,
  updateAdminPublicData,
  getAdminAuthDataById,
} from "../_repository/users.ts";
import { OnlyPasswordUpdated } from "../../src/errors/onlyPasswordUpdated.ts";

export async function editAdmin(
  publicId: string,
  authId: string,
  email: string | null,
  role_id: string | null,
  name: string | null,
  newPassword: string | null
): Promise<void> {
  let authEmailInDB: string | null = null;
  if (email) {
    authEmailInDB = await getAdminAuthDataById(authId);
  }
  let updatedAuthData: Boolean = false;
  if (newPassword || email) {
    await updateAdminAuthData(authId, newPassword, email);
    updatedAuthData = true;
  }
  try {
    if (email || name || role_id) {
      await updateAdminPublicData(publicId, email, name, role_id);
    }
  } catch (error) {
    if (email) {
      // If email is being updated, we need to revert the auth email change
      if (updatedAuthData) {
        await updateAdminAuthData(authId, null, authEmailInDB);
      }
      if (newPassword) {
        const selectFields: Record<string, string> = {};
        if (email) {
          selectFields.email = "email";
        }
        if (name) {
          selectFields.full_name = "full_name";
        }
        if (role_id) {
          selectFields.role_id = "role_id";
        }
        throw new OnlyPasswordUpdated(
          `Only password was updated, but ${Object.keys(selectFields).join(
            ", "
          )} change failed`
        );
      }
    } else {
      throw error;
    }
  }
}
