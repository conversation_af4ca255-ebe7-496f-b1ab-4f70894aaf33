import { z } from "npm:zod@latest";

export const passwordSchema = z
  .string()
  .min(8, { message: "Password must be at least 8 characters long" })
  .max(64, { message: "Password must be at most 64 characters long" });

export type PasswordType = z.infer<typeof passwordSchema>;

export const editAdminSchema = z.object({
  publicId: z.string().uuid({ message: "Invalid user ID format" }),
  authId: z.string().uuid({ message: "Invalid auth ID format" }),
  password: passwordSchema.optional(),
  email: z.string().email({ message: "Invalid email format" }).optional(),
  name: z.string().trim().min(1, { message: "Name cannot be empty" }).optional(),
  role_id: 
    z.string()
    .uuid({ message: "Invalid role ID format" })
    .optional(),
}).refine((data => {
  // Ensure at least one of newPassword, email, name, or role_id is provided
  return data.newPassword || data.email || data.name || data.role_id;
}), {
  message: "At least one field (newPassword, email, name, role_id) must be provided",
});

export type EditPasswordType = z.infer<typeof editAdminSchema>;
