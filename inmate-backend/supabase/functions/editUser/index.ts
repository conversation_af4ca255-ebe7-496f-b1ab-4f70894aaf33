import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { ResponseHelper } from "../src/utils/ResponseHelper.ts";
import { withLogging } from "../src/utils/logger.ts"; // Change from default import to named import
import { editAdminSchema } from "./dtos.ts";
import { editAdmin } from "./_services/users.ts";

Deno.serve(
  withLogging(async (req) => {
    if (req.method === "OPTIONS") {
      return ResponseHelper.successResponse(null, "CORS preflight response");
    }

    try {
      if (req.method !== "PATCH") {
        return ResponseHelper.errorResponse(
          "Invalid request method, only PATCH is allowed",
          405
        );
      }
      const body = await req.json();
      const parsedBody = editAdminSchema.safeParse(body);
      if (!parsedBody.success) {
        return new Response(
          JSON.stringify({
            error: "Invalid request body",
            details: parsedBody.error.format(),
          }),
          { status: 400 }
        );
      }
      const { publicId, authId, email, role_id, name, password } = body;
      await editAdmin(publicId, authId, email, role_id, name, password);
      return ResponseHelper.successResponse(
        null,
        "Admin updated successfully"
      );
    } catch (error) {
      let errorMessage = "An unexpected error occurred";
      let statusCode = 500;
      if (error.statusCode) {
        statusCode = error.statusCode;
        errorMessage = error.message;
      }
      return ResponseHelper.errorResponse(errorMessage, statusCode);
    }
  })
);
