import { supabase } from "../../src/lib/SupabaseClient.ts";

export async function getOverrides(
  roomIds: string[],
  date: string
): Promise<any> {
  // Call the get function from the repository
  const { data, error } = await supabase
    .from("overrides")
    .select(
      "id, room_id, override_dates(id, date), overrides_timeslots ( id, starttime, endtime )"
    )
    .in("room_id", roomIds);

  if (error) {
    throw new Error(`Error fetching overrides: ${error.message}`);
  }
  const filteredOverrides = data
    .map((override) => {
      // Filter to only include overrides that have the specific date
      override.override_dates = override.override_dates.filter(
        (overrideDate) => overrideDate.date === date
      );
      return override;
    })
    .filter((override) => override.override_dates.length > 0);
  return filteredOverrides;
}
