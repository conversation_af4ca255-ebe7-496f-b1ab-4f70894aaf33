import { supabase } from "../../src/lib/SupabaseClient.ts";

export const getSettings = async (): Promise<number[]> => {
  try {
    const settings = [
      Deno.env.get("MEETING_DURATION_KEY") || "meeting_duration",
      Deno.env.get("BETWEEN_MEETING_DURATION_KEY") ||
        "interval_between_meetings",
      Deno.env.get("BOOKING_WINDOW_LIMIT") || "booking_window_limit"
    ];
    const { data, error } = await supabase
      .from("settings")
      .select("key, value")
      .in("key", settings); // Ensure this is properly terminated

    if (error) {
      console.error("Error fetching meeting duration:", error);
      console.log("Using default meeting duration: 30 minutes");
      return [25, 5, 30]; // Default to 30 minutes if there's an error
    }

    // Parse the value as a number and ensure it's valid
    if (!data || data.length === 0) {
      console.warn("No meeting duration found in database, using default: 30");
      return [25, 5, 30]; // Default value as fallback
    }
    let duration = 25;
    let interval = 5;
    let bookingWindowLimit = 30;
    for (const setting of data) {
      if (
        setting.key === (Deno.env.get("MEETING_DURATION_KEY") ||
        "meeting_duration")
      ) {
        const meeting_duration = parseInt(setting.value, 10);
        if (isNaN(meeting_duration) || meeting_duration <= 0) {
          console.warn(
            `Invalid meeting duration in database: ${setting.value}, using default: ${duration}`
          );
        } else {
          duration = meeting_duration;
        }
      } else if (
        setting.key === (Deno.env.get("BETWEEN_MEETING_DURATION_KEY") ||
        "interval_between_meetings")
      ) {
        const interval_between_meetings = parseInt(setting.value, 10);
        if (
          isNaN(interval_between_meetings) ||
          interval_between_meetings <= 0
        ) {
          console.warn(
            `Invalid interval between meetings in database: ${setting.value}, using default: ${interval}`
          );
        } else {
          interval = interval_between_meetings;
        }
      } else if (
        setting.key === (Deno.env.get("BOOKING_WINDOW_LIMIT") ||
        "booking_window_limit")
      ) {
        const bookingWindowLimitSetting = parseInt(setting.value, 10);
        if (isNaN(bookingWindowLimitSetting) || bookingWindowLimitSetting <= 0) {
          console.warn(
            `Invalid booking window limit in database: ${setting.value}, using default: ${duration}`
          );
        } else {
          bookingWindowLimit = bookingWindowLimitSetting;
        }
      }
    }
    return [duration, interval, bookingWindowLimit];
  } catch (e) {
    console.error("Exception in getMeetingDuration:", e);
    return [25, 5, 30]; // Default value as fallback
  }
};
