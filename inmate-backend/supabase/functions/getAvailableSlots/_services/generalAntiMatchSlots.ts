export const generalAntiMatchSlots = (slotsA, slotsB) => {
  // If there are no reservations, return all slots
  if (slotsA.length === 0) {
    return slotsB;
  }

  // Filter out slots that overlap with any reservation
  return slotsB.filter((slot) => {
    // Check each reservation for overlap with this slot
    for (const reservation of slotsA) {
      // Check for overlap
      if (
        (reservation.startTime < slot.endTime) && (reservation.endTime > slot.startTime)
      ) {
        return false; // This slot overlaps, exclude it
      }
    }

    return true; // No overlap found, keep this slot
  });
};
