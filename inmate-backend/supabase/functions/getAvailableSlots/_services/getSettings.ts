import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { ResponseHelper } from "../../src/utils/ResponseHelper.ts";
import { getInmateById } from "../../src/models/Inmate.ts";
import { NotAvailableError } from "../../src/errors/notAvailalbeError.ts";
import { ValidationError } from "../../src/errors/validationError.ts";
import { supabase } from "../../src/lib/SupabaseClient.ts";
import { getAvailability as getSectionAvailability } from "../_repositories/section.ts";
import { getAvailability as getRoomAvailability } from "../_repositories/room.ts";

export const getSettings = async (inmateId: string, date: string) => {
  let inmateData = await getInmateById(
    inmateId,
    "room_id, section_id, status_id, is_building_default, is_room_default"
  );
  if (!inmateData) {
    return ResponseHelper.errorResponse("Inmate not found", 404);
  }
  if (
    inmateData.status_id !== (Deno.env.get("INMATE_STATUS_ID_AVAILABLE") || 1)
  ) {
    throw new NotAvailableError(
      `Inmate is unavailable (Status: ${inmateData.status_id}).`
    );
  }

  let settings: any[] = [];
  if (inmateData.is_building_default) {
    settings = await getSectionAvailability(inmateData.section_id, date);
  } else if (inmateData.is_room_default) {
    if (!inmateData.room_id) {
      throw new ValidationError(
        "Inmate is not assigned to a room, but is marked as room default."
      );
    }
    settings = await getRoomAvailability(inmateData.room_id, date);
  } else {
    // If the inmate is not building default, get the inmate_availability_settings
    const { data: inmateSettings, error: inmateError } = await supabase
      .from("inmate_availability_settings")
      .select("id")
      .eq("inmate_id", inmateId)
      .eq("day", new Date(date)
    .toLocaleString("en-US", { weekday: "long" })
    .toLowerCase());
    if (inmateError) {
      throw new Error(
        `Failed to fetch inmate settings: ${inmateError.message}`
      );
    }
    if (!inmateSettings || inmateSettings.length === 0) {
      return {
        settings: [],
        section_id: inmateData.section_id,
        room_id: inmateData.room_id,
        is_room_default: inmateData.is_room_default,
      };
    }
    // Extract the setting IDs
    const settingIds = inmateSettings.map((setting) => setting.id);
    // Now get the availability_times using the setting IDs
    const { data: availabilityTimes, error: timesError } = await supabase
      .from("availability_times")
      .select("setting_id, type, available_from, available_to")
      .eq("type", "inmate")
      .in("setting_id", settingIds);
    if (timesError) {
      throw new Error(
        `Failed to fetch availability times: ${timesError.message}`
      );
    }
    settings = availabilityTimes.map((time) => ({
      setting_id: time.setting_id,
      startTime: new Date(`${date}T${time.available_from}`),
      endTime: new Date(`${date}T${time.available_to}`),
    }));
  }

  return {
    settings,
    section_id: inmateData.section_id,
    room_id: inmateData.room_id,
    is_room_default: inmateData.is_room_default,
  };
};
