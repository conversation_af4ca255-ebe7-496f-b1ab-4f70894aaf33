import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { isValidDateSimple } from "../src/utils/validateTime.ts";
import { ResponseHelper } from "../src/utils/ResponseHelper.ts";
import { withLogging } from "../src/utils/logger.ts"; // Change from default import to named import
import { getSettings } from "./_services/getSettings.ts";
import { filterTimes } from "./_services/filterTimes.ts";
import { AvailabilityTimesService } from "../src/services/AvailabilityTimesService.ts";
import { getMeetingDuration } from "./_repositories/meetingDuration.ts";

Deno.serve(
  withLogging(async (req) => {
    if (req.method === "OPTIONS") {
      return ResponseHelper.successResponse(null, "CORS preflight response");
    }

    try {
      if (req.method !== "GET") {
        return ResponseHelper.errorResponse(
          "Invalid request method, only GET is allowed",
          405
        );
      }

      const url = new URL(req.url);
      const inmateId = url.searchParams.get("inmate_id");
      const date = url.searchParams.get("date");
      const visitorId = url.searchParams.get("visitor_id");

      if (!inmateId || !date || !visitorId) {
        return ResponseHelper.errorResponse("Missing required parameters", 400);
      }

      if (isValidDateSimple(new Date(date), date) === false) {
        return ResponseHelper.errorResponse(
          "Invalid date format, must be in ISO format",
          400
        );
      }

      const [meetingDuration, betweenMeetingInterval, bookingWindowLimit] =
        await getMeetingDuration();
      const today = new Date().toISOString().split("T")[0];
      const todayDate = new Date(`${today}T00:00:00Z`);
      const msDiff = new Date(date).getTime() - todayDate.getTime();
      const bookingWindowLimitMs = bookingWindowLimit * 24 * 60 * 60 * 1000; // Convert days to milliseconds
      if (bookingWindowLimitMs < msDiff) {
        return ResponseHelper.errorResponse(
          `Date must be within the booking window limit of ${bookingWindowLimit} days`,
          400
        );
      }
      const availabilityTimesService = new AvailabilityTimesService(
        meetingDuration,
        betweenMeetingInterval
      );
      const settings = await getSettings(inmateId, date);
      let filteredSettings;
      const fromDate = `${date}T00:00:00.000Z`;
      const toDate = `${date}T23:59:59.999Z`;
      if ("settings" in settings) {
        filteredSettings = await filterTimes(
          settings.section_id,
          settings.room_id,
          settings.settings,
          fromDate,
          toDate,
          date,
          availabilityTimesService,
          inmateId,
          visitorId,
          settings.is_room_default
        );
      } else {
        return ResponseHelper.errorResponse("Invalid settings response", 500);
      }
      if (!settings) {
        return ResponseHelper.errorResponse(
          "No settings found for the given inmate",
          404
        );
      }

      return ResponseHelper.successResponse(
        filteredSettings,
        "Available slots retrieved successfully"
      );
    } catch (error) {
      const status = error.statusCode || 500;
      const errorMessage = error.message || "Internal Server Error";
      if (status === 500) {
        console.error("Internal Server Error:", error);
      }
      return ResponseHelper.errorResponse(errorMessage, status);
    }
  }, "availabilitySlots")
);
