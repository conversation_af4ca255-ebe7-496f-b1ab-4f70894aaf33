import { adminSupabase } from "../../src/lib/SupabaseClient.ts";
import { UnauthorizedError } from "../../src/errors/unathorizedError.ts";

export async function getUserByToken(token: string) {
  try {
    const { data, error } = await adminSupabase.auth.admin.getUser(token);
    
    if (error) {
      throw new UnauthorizedError("Invalid token");
    }
    
    return data.user;
  } catch (error) {
    console.log("Error verifying token:", error);
    throw new UnauthorizedError("Error verifying token");
  }
}
