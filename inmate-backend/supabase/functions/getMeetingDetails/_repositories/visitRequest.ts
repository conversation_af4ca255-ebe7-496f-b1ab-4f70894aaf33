import { supabase } from "../../src/lib/SupabaseClient.ts";

export const getMeetingData = async (meetingId: string, role) => {
  if (role === "inmate") {
    const { data, error } = await supabase
      .from("visit_requests")
      .select("inmate:inmate_id(full_name), duration, datetime")
      .eq("id", meetingId)
      .single();

    if (error) {
      console.error("Error fetching meeting data:", error);
      return null;
    }

    if (!data) {
      return null;
    }

    const meetingDetails = {
      id: data.id,
      startTime: data.datetime,
      duration: data.duration,
      endTime: new Date(new Date(data.datetime).getTime() + data.duration * 60000).toISOString(),
      inmateName: data.inmate.full_name,
      roomId: data.room_id,
    };

    return meetingDetails;
  } else if (role === "visitor") {
    const { data, error } = await supabase
      .from("visit_requests")
      .select("visitor:visitor_id(full_name), duration, datetime")
      .eq("id", meetingId)
      .single();

    if (error) {
      console.error("Error fetching meeting data:", error);
      return null;
    }

    if (!data) {
      return null;
    }

    const meetingDetails = {
      id: data.id,
      startTime: data.datetime,
      duration: data.duration,
      endTime: new Date(new Date(data.datetime).getTime() + data.duration * 60000).toISOString(),
      visitorName: data.visitor.full_name,
    };

    return meetingDetails;
  } else if (role === "admin") {
    const { data, error } = await supabase
      .from("visit_requests")
      .select("user:user_id(full_name), inmate:inmate_id(full_name), visitor:visitor_id(full_name), duration, datetime, room_id")
      .eq("id", meetingId)
      .single();

    if (error) {
      console.error("Error fetching meeting data:", error);
      return null;
    }

    if (!data) {
      return null;
    }

    const meetingDetails = {
      id: data.id,
      startTime: data.datetime,
      duration: data.duration,
      endTime: new Date(new Date(data.datetime).getTime() + data.duration * 60000).toISOString(),
      adminName: data.user? data.user.full_name : "Admin",
      inmateName: data.inmate.full_name,
      visitorName: data.visitor.full_name,
      roomId: data.room_id,
    };

    return meetingDetails;
  }
};
