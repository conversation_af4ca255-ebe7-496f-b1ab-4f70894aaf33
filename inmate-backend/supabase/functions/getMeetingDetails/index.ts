import { getMeetingData } from "./_repositories/visitRequest.ts"; // Adjust the import path as necessary
import { withLogging } from "../src/utils/logger.ts"; // Change from default import to named import

Deno.serve(
  withLogging(async (req) => {
    // Handle OPTIONS request
    try {
      if (req.method === "OPTIONS") {
        return new Response("ok", { headers: { Allow: "GET, OPTIONS" } });
      }

      if (req.method !== "GET") {
        return new Response("Method not allowed", { status: 405 });
      }
      const url = new URL(req.url);
      const meetingId = url.searchParams.get("meeting_id");
      const role = url.searchParams.get("role");

      if (!meetingId) {
        return new Response("Missing meeting_id", { status: 400 });
      }

      if (
        !role ||
        !(role === "inmate" || role === "visitor" || role === "admin")
      ) {
        return new Response("Invalid role", { status: 400 });
      }

      const meetingDetails = await getMeetingData(meetingId, role);
      if (!meetingDetails) {
        return new Response("Meeting not found", { status: 404 });
      }

      return new Response(JSON.stringify(meetingDetails), {
        headers: { "Content-Type": "application/json" },
      });
    } catch (error) {
      const status = error.statusCode || 500;
      const errorMessage = error.message || "Internal Server Error";
      if (status === 500) {
        console.error("Internal Server Error:", error);
      }
      return new Response(JSON.stringify({ error: errorMessage }), { status });
    }
  }), "getMeetingDetails"
);
