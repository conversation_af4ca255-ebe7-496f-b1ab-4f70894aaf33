import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { createClient } from 'jsr:@supabase/supabase-js@2';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

interface TranscriptionResponse {
  success: boolean;
  data?: {
    id: string;
    meeting_id: string;
    timestamp: string;
    transcription: string;
  };
  error?: string;
}

Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
    const supabase = createClient(supabaseUrl, supabaseKey);

    if (req.method === 'GET') {
      // Extract meeting_id from URL path or query parameters
      const url = new URL(req.url);
      const pathSegments = url.pathname.split('/');
      let meetingId = pathSegments[pathSegments.length - 1];
      
      // If not in path, check query parameters
      if (!meetingId || meetingId === 'getMeetingTranscription') {
        meetingId = url.searchParams.get('meeting_id');
      }

      if (!meetingId) {
        const response: TranscriptionResponse = {
          success: false,
          error: 'Meeting ID is required. Provide it in the URL path or as a query parameter (?meeting_id=xxx)'
        };
        return new Response(JSON.stringify(response), {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        });
      }

      // Validate UUID format (basic check)
      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
      if (!uuidRegex.test(meetingId)) {
        const response: TranscriptionResponse = {
          success: false,
          error: 'Invalid meeting ID format. Must be a valid UUID.'
        };
        return new Response(JSON.stringify(response), {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        });
      }

      // Query the transcription from the database
      const { data, error } = await supabase
        .from('meetings_transacriptions')
        .select('id, meeting_id, timestamp, transcription')
        .eq('meeting_id', meetingId)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          // No rows found
          const response: TranscriptionResponse = {
            success: false,
            error: `No transcription found for meeting ID: ${meetingId}`
          };
          return new Response(JSON.stringify(response), {
            status: 404,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          });
        } else {
          // Other database error
          console.error('Database error:', error);
          const response: TranscriptionResponse = {
            success: false,
            error: 'Database error occurred while fetching transcription'
          };
          return new Response(JSON.stringify(response), {
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          });
        }
      }

      // Success response
      const response: TranscriptionResponse = {
        success: true,
        data: {
          id: data.id,
          meeting_id: data.meeting_id,
          timestamp: data.timestamp,
          transcription: data.transcription
        }
      };

      return new Response(JSON.stringify(response), {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });

    } else if (req.method === 'POST') {
      // Handle POST request with meeting_id in body
      const body = await req.json();
      const { meeting_id } = body;

      if (!meeting_id) {
        const response: TranscriptionResponse = {
          success: false,
          error: 'Meeting ID is required in the request body'
        };
        return new Response(JSON.stringify(response), {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        });
      }

      // Validate UUID format
      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
      if (!uuidRegex.test(meeting_id)) {
        const response: TranscriptionResponse = {
          success: false,
          error: 'Invalid meeting ID format. Must be a valid UUID.'
        };
        return new Response(JSON.stringify(response), {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        });
      }

      // Query the transcription from the database
      const { data, error } = await supabase
        .from('meetings_transacriptions')
        .select('id, meeting_id, timestamp, transcription')
        .eq('meeting_id', meeting_id)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          const response: TranscriptionResponse = {
            success: false,
            error: `No transcription found for meeting ID: ${meeting_id}`
          };
          return new Response(JSON.stringify(response), {
            status: 404,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          });
        } else {
          console.error('Database error:', error);
          const response: TranscriptionResponse = {
            success: false,
            error: 'Database error occurred while fetching transcription'
          };
          return new Response(JSON.stringify(response), {
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          });
        }
      }

      // Success response
      const response: TranscriptionResponse = {
        success: true,
        data: {
          id: data.id,
          meeting_id: data.meeting_id,
          timestamp: data.timestamp,
          transcription: data.transcription
        }
      };

      return new Response(JSON.stringify(response), {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });

    } else {
      // Method not allowed
      const response: TranscriptionResponse = {
        success: false,
        error: 'Method not allowed. Use GET or POST.'
      };
      return new Response(JSON.stringify(response), {
        status: 405,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    }

  } catch (error) {
    console.error('Unexpected error:', error);
    const response: TranscriptionResponse = {
      success: false,
      error: 'An unexpected error occurred'
    };
    return new Response(JSON.stringify(response), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
});
