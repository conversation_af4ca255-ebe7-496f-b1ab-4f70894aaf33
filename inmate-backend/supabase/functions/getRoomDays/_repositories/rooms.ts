import { supabase } from "../../src/lib/SupabaseClient.ts";
import { NotFoundError } from "../../src/errors/notFoundError.ts";
import { CustomError } from "../../src/errors/customError.ts";
import { getAvailability as getSectionAvailability } from "./section.ts";

export async function getAvailability(room_id: string) {
  const { data: room, error: roomError } = await supabase
    .from("rooms")
    .select("id, is_building_default, section_id, active")
    .eq("id", room_id)
    .single();
  if (roomError) {
    if (roomError.code === "PGRST116") {
      throw new NotFoundError("The Room Does Not Exist");
    } else {
      throw new Error(`Error Fetching Room: ${roomError.message}`);
    }
  }
  if (!room.active) {
    throw new CustomError("The Room is Inactive", 450);
  }
  if (room.is_building_default) {
    // If the room is a building default, we need to fetch the section settings
    return getSectionAvailability(room.section_id);
  }
  const { data: roomSettings, error } = await supabase
    .from("room_availability_settings")
    .select("id, day")
    .eq("room_id", room_id);

  if (error) {
    if (error.code === "PGRST116") {
      throw new NotFoundError("Room availability settings not found");
    }
    throw new Error(`Error fetching room availability: ${error.message}`);
  }
  if (!roomSettings || roomSettings.length === 0) {
    throw new NotFoundError("Room availability settings not found");
  }
  const days = roomSettings.map((setting) => setting.day);
  const uniqueDays = Array.from(new Set(days));
  return uniqueDays;
}
