import { supabase } from "../../src/lib/SupabaseClient.ts";
import { NotFoundError } from "../../src/errors/notFoundError.ts";

export async function getAvailability(room_id: string) {
  const { data , error } = await supabase
    .from("section_availability_settings")
    .select("id, day")
    .eq("section_id", room_id);

  if (error) {
    if (error.code === "PGRST116") {
      throw new NotFoundError("Section availability settings not found");
    }
    throw new Error(`Error fetching room availability: ${error.message}`);
  }
  if (!data || data.length === 0) {
    throw new NotFoundError("Section availability settings not found");
  }

  const days = data.map((setting) => setting.day);
  const uniqueDays = Array.from(new Set(days));
  return uniqueDays;
}
