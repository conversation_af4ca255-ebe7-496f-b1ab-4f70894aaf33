import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { withLogging } from "../src/utils/logger.ts";
import { getRoomDays } from "./_services/room.ts";
import { uuidSchema } from "./dtos.ts";

Deno.serve(
  withLogging(async (req) => {
    try {
      if (req.method === "GET") {
        const params = new URL(req.url).pathname;
        const pathList = params.split("/");
        if (pathList.length < 3) {
          return new Response(
            JSON.stringify({
              error: "Invalid request parameters",
              details: "Missing room or date parameter",
            }),
            { status: 400 }
          );
        }
        const roomId = pathList[2];
        const parsedRoomId = uuidSchema.safeParse(roomId);
        if (!parsedRoomId.success) {
          return new Response(
            JSON.stringify({
              error: "Invalid room ID",
              details: parsedRoomId.error.format(),
            }),
            { status: 400 }
          );
        }
        const days = await getRoomDays(roomId);
        return new Response(JSON.stringify(days), {
          headers: { "Content-Type": "application/json" },
          status: 200,
        });
      }
      return new Response("Method not allowed", { status: 405 });
    } catch (error) {
          const status = error.statusCode || 500;
          const errorMessage = error.message || "Internal Server Error";
          const data = error.additionalProperties || {};

          return new Response(
            JSON.stringify({ error: errorMessage, data }),
            { status }
          );
        }
  }, "getRoomDays")
);
