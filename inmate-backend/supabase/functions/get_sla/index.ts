// Setup type definitions for built-in Supabase Runtime APIs
import "https://esm.sh/@supabase/functions-js/src/edge-runtime.d.ts";
import {createClient} from "npm:@supabase/supabase-js@2.39.3";
import { withLogging } from '../src/utils/logger.ts';  // Change from default import to named import
// Initialize Supabase client
const supabase = createClient(Deno.env.get("SUPABASE_URL")!, Deno.env.get("SUPABASE_ANON_KEY")!);

// CORS headers
const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Methods": "POST, GET, OPTIONS",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
  "Access-Control-Max-Age": "86400",
  "Content-Type": "application/json",
};

function checkDurationExceedsLimit(createdAt, firstResponseTime, maxDuration) {
  const parsedMaxDuration = parseInt(maxDuration, 10); // Convert maxDuration from text to number

  const createdTime = new Date(createdAt);
  const firstResponse = new Date(firstResponseTime);

  if (isNaN(createdTime) || isNaN(firstResponse)) {
    console.error("Invalid date format", { createdAt, firstResponseTime });
    return { exceeded: false, actualDuration: 0, status: "Invalid Date" };
  }

  // Calculate SLA limit by adding maxDuration to createdTime
  const slaLimit = new Date(createdTime.getTime() + parsedMaxDuration * 60000);

  const differenceInMinutes = (firstResponse - createdTime) / (1000 * 60); // Convert ms to minutes
  const exceeded = firstResponse > slaLimit;

  return {
    exceeded,
    time_line:{
    actualDuration: differenceInMinutes,
    slaLimit: slaLimit.toISOString(),
    request_submission: createdTime.toISOString(),
    user_response: firstResponse.toISOString(),
    },
    status: exceeded ? "OverDue" : "On Time"
  };
}

function determineAction(statusName) {
  const deniedStatuses = ["denied", "admin_cancelled"];
  const approvedStatuses = ["scheduled", "completed", "running","starting_soon"];

  if (deniedStatuses.includes(statusName.toLowerCase())) {
    return "Denied Request";
  } else if (approvedStatuses.includes(statusName.toLowerCase())) {
    return "Approved Request";
  }
  return "Unknown Action";
}

// Helper function to check if a date is within the last week
function isLastWeek(dateStr) {
  const date = new Date(dateStr);
  const today = new Date();
  const lastWeekStart = new Date(today);
  lastWeekStart.setDate(today.getDate() - 7);
  
  return date >= lastWeekStart && date <= today;
}

// Helper function to check if a date is older than a week
function isOlder(dateStr) {
  const date = new Date(dateStr);
  const today = new Date();
  const lastWeekStart = new Date(today);
  lastWeekStart.setDate(today.getDate() - 7);
  
  return date < lastWeekStart;
}
// Helper function to check if a date is today
function isToday(dateStr) {
  const date = new Date(dateStr);
  const today = new Date();
  
  return date.getDate() === today.getDate() && 
         date.getMonth() === today.getMonth() && 
         date.getFullYear() === today.getFullYear();
}
Deno.serve(withLogging(async (req) => {
  if (req.method === "OPTIONS") {
    return withCors(new Response(null, { status: 204 }));
  }

  if (req.method !== "GET") {
    return withCors(new Response("Method Not Allowed", { status: 405 }));
  }

  try {
    const url = new URL(req.url);
    
    // Extract all filter parameters
    const page = parseInt(url.searchParams.get("page") || "1");
    const pageSize = parseInt(url.searchParams.get("pageSize") || "10");
    const dateFrom = url.searchParams.get("dateFrom");
    const dateTo = url.searchParams.get("dateTo");
    const adminName = url.searchParams.get("adminName") || "";
    const status = url.searchParams.get("status") || "";

    console.log("Received query parameters:", { 
      page, pageSize, dateFrom, dateTo, adminName,  status 
    });

    // Fetch all data
    const { data: userData, error: userDataError } = await supabase
      .from('visit_requests')
      .select('* , status_id(name), user_id(* , role_id(name))')
      .not('first_response_time', 'is', null)
      .is('deleted_at', null);

    if (userDataError) {
      return withCors(
        new Response(
          JSON.stringify({ error: `Internal Server Error ${userDataError.message}` }),
          { status: 500 }
        )
      );
    }

    console.log(userData, "<==================");

    const { data: settings, error: settingsError } = await supabase
      .from('settings')
      .select('value')
      .eq('key', 'request_response_due');

    if (settingsError) {
      return withCors(
        new Response(
          JSON.stringify({ error: `Internal Server Error ${settingsError.message}` }),
          { status: 500 }
        )
      );
    }
    
    const max_duration = settings[0].value;
    
    // Process all results
    let results = userData.map(visit => {
      const user = visit.user_id;
      const role = user?.role_id;
    
      return {
        visit_id: visit.id,
        request: visit.visit_number,
        name: user?.full_name ?? "Unknown User",
        name_and_role: `${user?.full_name ?? "Unknown"} (${role?.name ?? "No Role"})`,
        date_and_time: visit.first_response_time,
        status_name: visit.status_id.name,
        action: determineAction(visit.status_id.name),
        ...checkDurationExceedsLimit(visit.created_at, visit.first_response_time, max_duration)
      };
    });
    

    // Apply filters in JavaScript
    if (dateFrom) {
      results = results.filter(item => new Date(item.date_and_time) >= new Date(dateFrom));
    }
    
    if (dateTo) {
      results = results.filter(item => new Date(item.date_and_time) <= new Date(dateTo));
    }
    
    if (adminName) {
      results = results.filter(item => 
        item.name.toLowerCase().includes(adminName.toLowerCase())
      );
    }
    
    
    
    if (status) {
      if (status === 'today') {
        results = results.filter(item => isToday(item.date_and_time));
      } else if (status === 'lastWeek') {
        results = results.filter(item => isLastWeek(item.date_and_time));
      } else if (status === 'overDue' || status === 'On Time') {
        results = results.filter(item => 
          item.status.toLowerCase() === status.toLowerCase()
        );
      }
    }

    // if (keyword) {
    //   results = results.filter(item => 
    //     item.name.toLowerCase().includes(keyword.toLowerCase()) ||
    //     item.request.toString().includes(keyword.toLowerCase())
    //   );
    // }
    if (results.length === 0) {
      return withCors(
        new Response(
          JSON.stringify({
            data: [],
            pagination: {
              page,
              pageSize,
              totalItems: 0,
              totalPages: 0
            },
            message: "No results found matching your criteria"
          }),
          { status: 200, headers: corsHeaders }
        )
      );
    }

    // Get total count after filtering
    const totalItems = results.length;
    
    // Apply pagination
    const paginatedResults = results.slice((page - 1) * pageSize, page * pageSize);
    
    // Calculate total pages
    const totalPages = Math.ceil(totalItems / pageSize);

    return withCors(
      new Response(
        JSON.stringify({
          data: paginatedResults,
          pagination: {
            page,
            pageSize,
            totalItems,
            totalPages
          }
        }),
        { 
          status: 200,
          headers: corsHeaders
        }
      )
    );
  } catch (error) {
    console.error("Unexpected error:", error);
    return withCors(
      new Response(
        JSON.stringify({ error: "Internal Server Error" }),
        { status: 500 }
      )
    );
  }
},"get_sla"));

// Helper to add CORS headers
function withCors(response) {
  const newResponse = new Response(response.body, response);
  newResponse.headers.set("Access-Control-Allow-Origin", "*");
  newResponse.headers.set("Access-Control-Allow-Methods", "GET, OPTIONS");
  newResponse.headers.set("Access-Control-Allow-Headers", "Content-Type, Authorization");

  // Ensure Content-Type is set for JSON responses
  if (!newResponse.headers.has("Content-Type")) {
    newResponse.headers.set("Content-Type", "application/json");
  }

  return newResponse;
}