// Follow this setup guide to integrate the Deno language server with your editor:
// https://deno.land/manual/getting_started/setup_your_environment
// This enables autocomplete, go to definition, etc.

// Setup type definitions for built-in Supabase Runtime APIs
import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { createClient } from 'npm:@supabase/supabase-js@2.39.3';
import { withLogging } from '../src/utils/logger.ts';  // Change from default import to named import

const supabase = createClient(Deno.env.get('SUPABASE_URL')!, Deno.env.get('SUPABASE_ANON_KEY')!);

// Helper to add CORS headers
function withCors(response: Response): Response {
  response.headers.set("Access-Control-Allow-Origin", "*");
  response.headers.set("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
  response.headers.set("Access-Control-Allow-Headers", "Content-Type, Authorization");
  return response;
}

Deno.serve(withLogging(async (req) => {
  if (req.method === "OPTIONS") {
    return withCors(new Response(null, { status: 204 }));
  }

  if (req.method !== "POST") {
    return withCors(new Response("Method Not Allowed", { status: 405 }));
  }

  try {
    const contentType = req.headers.get("content-type") || "";
    if (!contentType.startsWith("multipart/form-data")) {
      return withCors(
          new Response(JSON.stringify({ error: "Invalid content type, expected form-data" }), {
            status: 400,
          })
      );
    }

    // Parse the form-data
    const formData = await req.formData();
    const inmate_name = formData.get("inmate_name") as string;
    const visitor_id = formData.get("visitor_id") as string;
    const inmate_code = formData.get("inmate_code") as string;
    const relation = formData.get("relation") as string | null; // Allow null
    const other = formData.get("other") as string | null; // Optional field
    const files = formData.getAll("document") as File[]; // Expecting the file as 'document'
    // Validate required fields
    const errors: Record<string, string> = {};

    if (!inmate_name) errors.inmate_name = "Inmate name is required";
    if (!visitor_id) errors.visitor_id = "Visitor ID is required";
    if (!inmate_code) errors.inmate_code = "Inmate code is required";
    if (!relation) errors.relation = "Relation is required";
    if (relation === "OTHER" && !other) {
      errors.other = "Please specify the other relation";
    }

    // if (!files || files.length === 0) errors.document = "Document is required";
    const validFiles = files.filter(file => file instanceof File && file.size > 0);
    if (!validFiles || validFiles.length === 0) errors.document = "Document is required";
    console.log("validFiles", validFiles);
    console.log("validFiles length", validFiles.length);
    console.log("files",    files);
    console.log("files length", files.length);
    // If there are any errors, return them
    if (Object.keys(errors).length > 0) {
      return withCors(
          new Response(
              JSON.stringify(errors),
              { status: 400
                ,headers: { "Content-Type": "application/json" } // Explicitly setting Content-Type
              }
          )
      );
    }

    console.log("Received form-data:", { inmate_name, visitor_id, inmate_code, relation });
    const { data: existingInmate, error: findError } = await supabase
        .from("inmates")
        .select("id")
        .eq("inmate_code", inmate_code)
        .single();

    if (!existingInmate) {
      return withCors(
          new Response(
              JSON.stringify({ inmate_code: "Inmate not found" }),
              { status: 404,
                headers: { "Content-Type": "application/json" } // Explicitly setting Content-Type
              }
          )
      );
    }

    console.log("inmate found:", existingInmate.id);

    const { data: InmateVisitor } = await supabase
        .from('inmate_visitors')
        .select("*", { count: 'exact' })
        .eq("inmate_id",existingInmate.id)
        .eq("visitor_id",visitor_id)
        .single();

    console.log("inmate_visitors: ", InmateVisitor);
    if (InmateVisitor == 0){
      console.log("No duplicate found");
    }
    if (InmateVisitor) {
      console.error("Duplicate relation detected:", InmateVisitor);
      return withCors(
          new Response(
              JSON.stringify({ Inmate_Visitor: "this inmate and visitor has an existing relation" }),
              { status: 409,
                headers: { "Content-Type": "application/json" } // Explicitly setting Content-Type
              }
          )
      );
    }

    // Insert into the Inmate_Visitor table
    const { data: newInmateVisitor, error: insertVisitorError } = await supabase
        .from("inmate_visitors")
        .insert({
          inmate_id: existingInmate.id,
          visitor_id,
          relation,
          requested_inmate_name: inmate_name,
          ...(other && { other }), // Include `other` only if provided
        })
        .select("id , inmate_id")
        .single();

    if (insertVisitorError) {
      console.error("Error inserting inmate visitor:", insertVisitorError);
      return withCors(
          new Response(
              JSON.stringify({ Inmate_Visitor: `Error inserting inmate visitor: ${insertVisitorError.message}` }),
              { status: 500 }
          )
      );
    }

    console.log("New inmate visitor created:", newInmateVisitor);

    // Upload each file to Supabase storage
    const bucketName = "inmate_documents/documents";
    const uploadResults = await Promise.all(files.map(async (file) => {
      const fileName = `${visitor_id}_${Date.now()}_${file.name}`;
      const fileBuffer = await file.arrayBuffer();
      console.log("File buffer:", fileBuffer);
      console.log("File name:", fileName);
      console.log("File type:", file.type);
      const uploadResult = await supabase.storage
          .from(bucketName)
          .upload(fileName, new Uint8Array(fileBuffer), {
            contentType: file.type,
            upsert: false,
          });

      if (uploadResult.error) {
        console.error("Error uploading document:", uploadResult.error);
        throw new Error("Error uploading document");
      }

      const documentUrl = supabase.storage
          .from(bucketName)
          .getPublicUrl(fileName).data?.publicUrl;

      if (!documentUrl) {
        console.error("Failed to retrieve document URL.");
        throw new Error("Failed to retrieve document URL");
      }

      const documentSize = file.size;
      const documentName = file.name;
      const updatedDocumentUrl = documentUrl?.replace("kong",Deno.env.get('GOTRUE_SITE_URL')||"138.199.226.230");
      console.log("Document uploaded:", { documentUrl, documentName, documentSize , updatedDocumentUrl });

      // Insert into the Inmate_Visitor_Documents table
      const { data: documentInsert, error: documentError } = await supabase
          .from("inmate_visitor_documents")
          .insert({
            inmate_visitor_id: newInmateVisitor.id,
            document_url: updatedDocumentUrl,
            document_name: documentName,
            document_size: documentSize,
          });

      if (documentError) {
        console.error("Error inserting inmate visitor document:", documentError);
        throw new Error("Error inserting inmate visitor document");
      }

      console.log("Inmate visitor document created:", documentInsert);
      return documentInsert;
    }));

    // Retrieve all documents for the new inmate visitor as a list
    const { data: documentsList, error: documentsError } = await supabase
        .from("inmate_visitor_documents")
        .select("*")
        .eq("inmate_visitor_id", newInmateVisitor.id);

    if (documentsError) {
      console.error("Error retrieving documents:", documentsError);
      return withCors(
          new Response(
              JSON.stringify({ Inmate_Visitor_Documents: "Error retrieving documents" }),
              { status: 500,
                headers: { "Content-Type": "application/json" } // Explicitly setting Content-Type
              }
          )
      );
    }

    console.log("Documents retrieved as a list:", documentsList);
    return withCors(
        new Response(
            JSON.stringify({
              message: "Inmate visitor and document added successfully",
              inmateVisitor: newInmateVisitor,
              document: documentsList,
            }),
            { status: 200 ,
              headers: { "Content-Type": "application/json" } // Explicitly setting Content-Type
            }
        )
    );
  } catch (error) {
    console.error("Unexpected error:", error);
    return withCors(
        new Response(
            JSON.stringify({ error: "Internal Server Error" }),
            { status: 500 ,
              headers: { "Content-Type": "application/json" } // Explicitly setting Content-Type
            }
        )
    );
  }
},"Inmate Visitor Table Function"));