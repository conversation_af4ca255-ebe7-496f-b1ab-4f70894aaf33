import "https://esm.sh/@supabase/functions-js/src/edge-runtime.d.ts";
import { supabase } from "../src/lib/SupabaseClient.ts";
import { withLogging } from "../src/utils/logger.ts"; // Use relative import path
import { ResponseHelper } from "../src/utils/ResponseHelper.ts";
import { updateDashBoardEndMeeting, updateDashBoardStartMeeting } from "./services/visitRequest.ts";


// CORS headers
const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Methods": "POST, GET, OPTIONS",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
  "Access-Control-Max-Age": "86400",
};

Deno.serve(withLogging(async (req) => {
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  try {
  const body = await req.json();  

  const event = body?.event;
  const visit_request_id = body.room?.name || body.egressInfo?.roomName;
  const filename = body.egressInfo?.roomComposite?.fileOutputs[0].filepath;
  const egressInfo = body.egressInfo; 

  // if (!visit_request_id) {
  //   return new Response(JSON.stringify({ error: "Missing visit_request_id" }), {
  //     status: 400,
  //     headers: corsHeaders,
  //   });
  // }


  switch (event) {
    case "room_started": {
      const status = "running";

      await updateDashBoardStartMeeting(visit_request_id);

      return new Response(JSON.stringify({ success: true }), {
        status: 200,
        headers: corsHeaders,
      });
    }

    case "room_finished": {
      const status = "completed";

      await updateDashBoardEndMeeting(visit_request_id);

      return new Response(JSON.stringify({ success: true }), {
        status: 200,
        headers: corsHeaders,
      });
    }

    case "egress_ended": {
      const url = "https://pub-8d1ea513eb604f36a72b719fb8f006f2.r2.dev/" + filename;
      const session_id = body.egressInfo?.roomId;
      // const visit_request_id = body.room[0].roomName;
      // console.log("Egress_Visit request ID:", visit_request_id);
      
      const { data: newRecord, error: recordError } = await supabase
        .from("meeting_records")
        .insert({
          visit_request_id: visit_request_id,
          session_id: session_id,
          recording_url: url, 
          // record_size: null, // Insert the size value from input
          // duration: null, // Insert the duration value from input
          // created_at: null, // Use only the time portion (HH:MM:SS)
          // deleted_at: null,
        })
        .single();

      if (recordError) {
        return new Response(JSON.stringify({ error: recordError.message }), {
          status: 500,
          headers: corsHeaders,
        });
      }

      return new Response(JSON.stringify({ success: true, newRecord }), {
        status: 200,
        headers: corsHeaders,
      });
    }

    default: {
      return new Response(JSON.stringify({ message: "Unhandled event type" }), {
        status: 200,
        headers: corsHeaders,
      });
    }
  }
  } catch (error) {
        const status = error.statusCode || 500;
        const errorMessage = error.message || "Internal Server Error";
        if (status === 500) {
          console.error("Internal Server Error:", error);
        }
        return ResponseHelper.errorResponse(errorMessage, status);
      }
}, "hook_test"));
