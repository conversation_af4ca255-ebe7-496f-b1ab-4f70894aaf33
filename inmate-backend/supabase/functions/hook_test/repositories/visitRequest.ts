import { supabase } from "../../src/lib/SupabaseClient.ts";
import { NotFoundError } from "../../src/errors/notFoundError.ts";

export const update = async (meetingId: string, updates: Record<string, any>): Promise<any> => {
  const { data, error } = await supabase
    .from("visit_requests")
    .update(updates)
    .eq("id", meetingId)
    .select() // Return all updated fields
    .single();

  if (error) {
    if (error.code === "PGRST116") {
      throw new NotFoundError("Meeting not found");
    } else {
      throw new Error(`Error updating meeting: ${error.message}`);
    }
  }

}

export const isTest = async (meetingId: string): Promise<boolean> => {
  const { data, error } = await supabase
    .from("visit_requests")
    .select("test")
    .eq("id", meetingId)
    .single();

  if (error) {
    if (error.code === "PGRST116") {
      throw new NotFoundError("Meeting not found");
    } else {
      throw new Error(`Error checking if meeting is test: ${error.message}`);
    }
  }

  if (!data) {
    throw new NotFoundError("Meeting not found");
  }

  return data.test;
}

export const deleteMeeting = async (meetingId: string): Promise<void> => {
  const { data, error } = await supabase
    .from("visit_requests")
    .delete()
    .eq("id", meetingId);

  if (error) {
    if (error.code === "PGRST116") {
      throw new NotFoundError("Meeting not found");
    } else {
      throw new Error(`Error deleting meeting: ${error.message}`);
    }
  }
}
