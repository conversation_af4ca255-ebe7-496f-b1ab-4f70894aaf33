import { update, isTest, deleteMeeting } from "../repositories/visitRequest.ts";

export async function updateDashBoardEndMeeting(meetingId: string) {
  if ((await isTest(meetingId))) {
    await deleteMeeting(meetingId);
    return;
  }
  const endTime = new Date();
  endTime.setSeconds(0, 0); // Set seconds and milliseconds to 0
  await update(meetingId, { status_id: Deno.env.VISIT_REQUEST_STATUS_COMPLETED || 6 , ended_at: endTime.toISOString() });
}

export async function updateDashBoardStartMeeting(meetingId: string) {
  const startTime = new Date();
  startTime.setSeconds(0, 0); // Set seconds and milliseconds to 0
  await update(meetingId, { status_id: Deno.env.VISIT_REQUEST_STATUS_RUNNING || 5 , started_at: startTime.toISOString() });
}
