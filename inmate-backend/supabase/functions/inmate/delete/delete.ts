import { createClient } from 'npm:@supabase/supabase-js@2.39.3';
import hasUserPermission from "../../src/utils/hasPermission.ts";
const supabase = createClient(
    Deno.env.get('SUPABASE_URL')!,
    Deno.env.get('SUPABASE_ANON_KEY')!
);
const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
    'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
    'Access-Control-Max-Age': '86400',
    'Content-Type': 'application/json'
};
export function filterCompletedvisits(data) {
    return data.filter(visit => !["missed", "completed", "ended", "denied","admin_cancelled","visitor_cancelled"].includes(visit.status_id.name));}

export default async function deleteinmate(req: Request) {
    try {
        const { inmate_id , user_id} = await req.json();
        const headers = {
            'Access-Control-Allow-Origin': '*', // Replace with your frontend URL
            'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
            'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
            'Access-Control-Max-Age': '86400',
            'Content-Type': 'application/json'            // ... other headers
        };
        if (req.method === 'OPTIONS') {
            return new Response(null, { headers });
        }
        if (!inmate_id || typeof inmate_id !== 'string') {
            return new Response(
                JSON.stringify({ error: 'Invalid or missing inmate_id.' }),
                { status: 400, headers: corsHeaders  }
            );
        }

        console.log('Received inmate_id:', inmate_id);
        const { data: user_roles, error: userError } = await supabase
            .from('users')
            .select('* , role_id(name,role_permissions(permission_id(name)))')
            .eq('user_auth_id', user_id)
            .single();

        if (userError || !user_roles) {
            return new Response(
                JSON.stringify({ error: `User with ID ${user_id} does not exist.` }),
                { status: 404, headers: corsHeaders }
            );
        }
        console.log('user_roles:', user_roles.role_id.name);
        console.log('user:', user_roles);
        const role = user_roles.role_id.name;
        const permission =await hasUserPermission(user_roles,"Suspend Inmate");
        console.log('permission:', permission);
        if (!permission) {
            return new Response(
                JSON.stringify({ error: `doesn’t have the required role`,
                                statusError:455,
                                permission: user_roles.role_id.name}),
                                { status: 404, headers: corsHeaders }
            );
        }
        // Check if the inmate exists
        const { data: inmate, error: inmateError } = await supabase
            .from('inmates')
            .select('*')
            .eq('id', inmate_id)
            .single();

        if (inmateError || !inmate) {
            return new Response(
                JSON.stringify({ error: `inmate with ID ${inmate_id} does not exist.` }),
                { status: 404, headers: corsHeaders }
            );
        }

        console.log('inmate found:', inmate);
        const deleted_at = inmate.deleted_at;
        if (deleted_at) {
            return new Response(
                JSON.stringify({ error: `inmate with ID ${inmate_id} is already deleted.` }),
                { status: 404, headers: corsHeaders }
            );
        }
        // Check for linked visits
        const { data:   linkedvisits, error: visitError } = await supabase
            .from('visit_requests')
            .select('*, status_id(name)')  // Fetch related 'status' table data
            .eq('inmate_id', inmate_id);
        console.log('linkedvisits:', linkedvisits);
        if (visitError) {
            return new Response(
                JSON.stringify({ error: `Error checking linked visits.  ${visitError.message}` }),
                { status: 500, headers: corsHeaders }
            );
        }

        const {data : suspeneded , error : statusError} = await supabase
            .from('inmates_status')
            .select('*')
            .eq('name','suspended')
            .single();


        console.log('statuses:', suspeneded);
        const filteredVisits = filterCompletedvisits(linkedvisits)
        if (filteredVisits && filteredVisits.length > 0) {
            console.log('Linked visits found:', filteredVisits);
            return new Response(
                JSON.stringify({
                    statusError : 452,
                    error: `inmate_linked_visits`,
                    filteredVisits
                }),
                { status: 400, headers: corsHeaders }
            );
        }
        // set `deleted_at` for the inmate
        const { error: updateError } = await supabase
            .from('inmates')
            .update({
                status_id: suspeneded.id
            })
            .eq('id', inmate_id);

        if (updateError) {
            return new Response(
                JSON.stringify({ error: 'Error updating the deleted_at for the inmate.' }),
                { status: 500, headers: corsHeaders }
            );
        }

        console.log('inmate marked as deleted and timestamp set successfully.');

        console.log('inmate marked as deleted successfully.');

        return new Response(
            JSON.stringify({ message: `inmate with ID ${inmate_id} marked as deleted successfully.` }),
            { status: 200, headers: corsHeaders }
        );
    } catch (error) {
        console.error('Error:', error.message);
        return new Response(
            JSON.stringify({ error: error.message }),
            { status: 500, headers: corsHeaders }
        );
    }
}
