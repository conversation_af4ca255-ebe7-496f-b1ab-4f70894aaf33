import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { createClient } from 'npm:@supabase/supabase-js@2.39.3';
import hasUserPermission from "../../src/utils/hasPermission.ts";
const corsHeaders = {
    'Access-Control-Allow-Origin': '*', // Replace with your frontend URL
    'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
    'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
    'Access-Control-Max-Age': '86400',
    'Content-Type': 'application/json',
};

const supabase = createClient(Deno.env.get('SUPABASE_URL')!, Deno.env.get('SUPABASE_ANON_KEY')!);


// Helper to add CORS headers
function withCors(response) {
    response.headers.set("Access-Control-Allow-Origin", "*");
    response.headers.set("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
    response.headers.set("Access-Control-Allow-Headers", "Content-Type, Authorization");
    response.headers.set("Access-Control-Max-Age", "86400");
    response.headers.set("Content-Type", "application/json");
    return response;
}
export default async function edit(req:Request){


        try {
            const {
                id, // Added ID to identify the inmate
                full_name,
                national_id,
                nationality,
                assigned_building,
                assigned_room,
                // status_id,
                unavailable,
                is_suspended,
                number_of_visits,
                availability,
                // To Be Reviewed*************************************************
                // is_building_default,
                is_default,
                // **********************************************
                inmate_code,
                user_id

            } = await req.json();

            console.log("Received request data:", {
                id,
                full_name,
                national_id,
                assigned_building,
                assigned_room,
                // status_id,
                number_of_visits,
                availability,
                // To Be Reviewed*************************************************
                // is_building_default,
                is_default,
                // **********************************************
            });

            // Validate required fields
            if (!id) {
                return withCors(
                    new Response(
                        JSON.stringify({ error: "Inmate ID is required" }),
                        { status: 400 }
                    )
                );
            }

            // Check if inmate exists
            const { data: existingInmate, error: findError } = await supabase
                .from("inmates")
                .select("*")
                .eq("id", id)
                .single();

            if (findError || !existingInmate) {
                console.error("Inmate not found:", findError);
                return withCors(
                    new Response(
                        JSON.stringify({ error: "Inmate not found" }),
                        { status: 404 }
                    )
                );
            }

            console.log("Existing inmate found:", existingInmate);

            // Check if inmate code already exists for a different inmate
            const { data: existingInmateWithCode, error: duplicateCheckError } = await supabase
                .from("inmates")
                .select("id")
                .eq("inmate_code", inmate_code)
                .neq("id", id)  // Exclude the current inmate
                .single();

            if (duplicateCheckError && duplicateCheckError.code !== 'PGRST116') {
                console.error("Error checking duplicate inmate code:", duplicateCheckError);
                return withCors(
                    new Response(
                        JSON.stringify({ error: "Error checking duplicate inmate code" }),
                        { status: 500 }
                    )
                );
            }

            if (existingInmateWithCode) {
                return withCors(
                    new Response(
                        JSON.stringify({
                            error: "Inmate code already exists",
                            statusError: 459
                        }),
                        { status: 409 }
                    )
                );
            }

            // Determine default flags
            // To Be Reviewed*************************************************
            //********************************************
            // *******************************************
            // ******************************************* */
            let is_building_default = false;
            let is_room_default = false;

            if (is_default) {
                is_building_default = true;
                if (assigned_room) {
                    is_room_default = true;
                    is_building_default = false;
                }
            }
            // *******************************************
            // *******************************************
            // ******************************************* */
            // Determine status_id based on flags
            let status_id = 1; // Default to available
            if (unavailable) {
              status_id = 2; // Set to unavailable
            }
            if (is_suspended) {
              status_id = 3; // Set to suspended (takes precedence over unavailable)
            }
            const { data: user_roles, error: userError } = await supabase
                .from('users')
                .select('* , role_id(name,role_permissions(permission_id(name)))')
                .eq('user_auth_id', user_id)
                .single();

            if (userError || !user_roles) {
                return new Response(
                    JSON.stringify({ error: `User with ID ${user_id} does not exist.` }),
                    { status: 404, headers: corsHeaders }
                );
            }
            console.log('user_roles:', user_roles.role_id.name);
            console.log('user:', user_roles);
            const permission = await hasUserPermission(user_roles,"Edit Inmate info");
            console.log('permission:', permission);
            if (!permission) {
                return new Response(
                    JSON.stringify({ error: `doesn’t have the required permission`,
                        statusError:455,
                        permission: user_roles.role_id.name}),
                    { status: 404, headers: corsHeaders }
                );
            }
            // Update inmate details
            const { error: updateError } = await supabase
                .from("inmates")
                .update({
                    full_name,
                    national_id,
                    nationality,
                    number_of_visits,
                    section_id: assigned_building,
                    room_id: assigned_room || null,
                    status_id,
                    // To Be Reviewed*************************************************
                    is_building_default,
                    is_room_default,
                    // ********************************************
                    inmate_code,

                },)
                .eq("id", id);

            if (updateError) {
                console.error("Error updating inmate details:", updateError);
                return withCors(
                    new Response(
                        JSON.stringify({ error: "Error updating inmate details" }),
                        { status: 500 }
                    )
                );
            }
            // Delete existing availability settings for this inmate
            const { error: deleteError } = await supabase
                .from("inmate_availability_settings")
                .delete()
                .eq("inmate_id", id);

            if (deleteError) {
                console.error("Error deleting old availability settings:", deleteError);
                return withCors(
                    new Response(
                        JSON.stringify({ error: "Error updating availability settings" }),
                        { status: 500 }
                    )
                );
            }

            console.log("Old availability settings deleted for inmate:", id);
            console.log("Inmate updated successfully:", id);

            // Skip availability if is_default is true
            if (!is_default && availability && availability.length > 0) {
                console.log("Updating availability settings for inmate:", id);
                console.log("=============================================");
                console.log(is_default, availability,"availability");


                for (const item of availability) {
                    const { day, times } = item;

                    // Insert new availability settings
                    const { data: availabilitySetting, error: availabilityError } = await supabase
                        .from("inmate_availability_settings")
                        .insert([
                            {
                                day,
                                inmate_id: id,
                            },
                        ])
                        .select("*")
                        .single();

                    if (availabilityError) {
                        console.error("Error adding availability settings:", availabilityError);
                        return withCors(
                            new Response(
                                JSON.stringify({ error: "Error adding availability settings" }),
                                { status: 500 }
                            )
                        );
                    }

                    console.log("Availability setting added:", availabilitySetting);

                    const settingId = availabilitySetting.id;

                    // Insert times into AvaliabilityTimes
                    if (times && times.length > 0) {
                        const timesData = times.map((time) => ({
                            setting_id: settingId,
                            type: "inmate",
                            available_from: time.from,
                            available_to: time.to,
                        }));

                        const { error: timesError } = await supabase
                            .from("availability_times")
                            .insert(timesData);

                        if (timesError) {
                            console.error("Error adding availability times:", timesError);
                            return withCors(
                                new Response(
                                    JSON.stringify({ error: "Error adding availability times" }),
                                    { status: 500 }
                                )
                            );
                        }

                        console.log("Availability times added for setting:", settingId);
                    }
                }
            }

            console.log("Inmate update process completed successfully");
            return withCors(
                new Response(
                    JSON.stringify({ message: "Inmate updated successfully" }),
                    { status: 200 }
                )
            );
        } catch (error) {
            console.error("Unexpected error:", error);
            return withCors(
                new Response(
                    JSON.stringify({ error: "Internal Server Error" }),
                    { status: 500 }
                )
            );
        }
    }