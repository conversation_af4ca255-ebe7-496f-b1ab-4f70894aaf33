import { supabase } from "./inmate_days.ts";

export const getBookingWindowLimit = async (): Promise<number> => {
  try {
    const settings =
      Deno.env.get("BOOKING_WINDOW_LIMIT") || "booking_window_limit";
    const { data, error } = await supabase
      .from("settings")
      .select("key, value")
      .eq("key", settings); // Ensure this is properly terminated

    if (error) {
      console.error("Error fetching booking window limit:", error);
      console.log("Using default booking window limit: 30 days");
      return 30; // Default to 30 minutes if there's an error
    }

    // Parse the value as a number and ensure it's valid
    if (!data || data.length === 0) {
      console.warn("No booking window limit found in database, using default: 30");
      return 30; // Default value as fallback
    }
    let bookingWindowLimit = 30;
    if (data[0].value) {
      const limit = parseInt(data[0].value, 10);
      if (isNaN(limit) || limit <= 0) {
        console.warn(
          `Invalid booking window limit in database: ${data[0].value}, using default: ${duration}`
        );
      } else {
        bookingWindowLimit = limit;
      }
    }
    return bookingWindowLimit;
  } catch (e) {
    console.error("Exception in getBookingWindowLimit:", e);
    return 30; // Default value as fallback
  }
};
