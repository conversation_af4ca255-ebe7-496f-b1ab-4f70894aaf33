import "https://esm.sh/@supabase/functions-js/src/edge-runtime.d.ts";
import { createClient } from "npm:@supabase/supabase-js@2.39.3";
import { getBookingWindowLimit } from "./getBookingWindowLimit.ts";
// Initialize Supabase client
export const supabase = createClient(
  Deno.env.get("SUPABASE_URL")!,
  Deno.env.get("SUPABASE_ANON_KEY")!
);

// CORS Headers
const corsHeaders = {
  "Access-Control-Allow-Origin": "*", // Replace * with your frontend URL for tighter security
  "Access-Control-Allow-Methods": "POST, GET, OPTIONS",
  "Access-Control-Allow-Headers":
    "authorization, x-client-info, apikey, content-type",
  "Access-Control-Max-Age": "86400", // Cache preflight response for 24 hours
};

export default async function getInmateDays(
  queryParams: Record<string, string | null>
) {
  try {
    // Handle CORS preflight request

    // Parse URL parameters

    const inmateId = queryParams.inmateId;
    if (!inmateId) throw new Error("inmateId is required");
    // Fetch inmate details
    const nowDate = new Date();
    const sevenDaysLaterDate = new Date(
      nowDate.getTime() + 7 * 24 * 60 * 60 * 1000
    );

    const [
      { data: inmateData, error: inmateError },
      { data: visitsData, count: visitsCount, error: visitError },
    ] = await Promise.all([
      supabase
        .from("inmates")
        .select(
          `
          id, full_name, national_id, number_of_visits, section_id(id), room_id(id, is_building_default, section_id), 
          deleted_at, is_building_default, is_room_default, inmate_code, nationality, status_id, inmate_availability_settings(*)
        `
        )
        .eq("id", inmateId)
        .single(),

      supabase
        .from("visit_requests")
        .select("*", { count: "exact" })
        .eq("inmate_id", inmateId) // You're likely missing this filter
        .in("status_id", [
          Deno.env.get("VISIT_REQUEST_STATUS_APPROVED") || "2",
          Deno.env.get("VISIT_REQUEST_STATUS_PENDING") || "1",
        ])
        .gte("datetime", nowDate.toISOString())
        .lte("datetime", sevenDaysLaterDate.toISOString()),
    ]);

    console.log("cout", visitsCount);
    console.log("Visits Data:", visitsData);
    if (inmateError)
      throw new Error(`Error fetching Inmate details: ${inmateError.message}`);

    console.log("Inmate Data:", inmateData);
    if (
      inmateData.status_id !== (Deno.env.get("INMATE_STATUS_ID_AVAILABLE") || 1)
    ) {
      throw new Error(
        `Inmate is unavailable (Status: ${inmateData.status_id}).`
      );
    }

    if (inmateData.number_of_visits <= visitsCount) {
      throw new Error("Maximum number of vists is reached for this inamte");
    }
    // Fetch availability settings based on the inmate's attributes
    let settingsTable = "inmate_availability_settings";
    let filterColumn = "inmate_id";
    let filterValue = inmateId;

    if (inmateData.is_building_default) {
      settingsTable = "section_availability_settings";
      filterColumn = "section_id";
      filterValue = inmateData.section_id.id;
    } else if (inmateData.is_room_default) {
      if (inmateData.room_id?.is_building_default) {
        settingsTable = "section_availability_settings";
        filterColumn = "section_id";
        filterValue = inmateData.room_id.section_id;
      } else {
        settingsTable = "room_availability_settings";
        filterColumn = "room_id";
        filterValue = inmateData.room_id.id;
      }
    }

    console.log("Settings Table:", settingsTable);
    console.log("Filter Column:", filterColumn);
    console.log("Filter value:", filterValue);
    // Fetch settings data

    // const { data: settingsData, error: settingsError } = await supabase
    //   .from(settingsTable)
    //   .select("id, day")
    //   .eq(filterColumn, filterValue);
    // console.log(settingsData, "<=====================");
    // if (settingsError)
    //   throw new Error(
    //     `Error fetching availability settings: ${settingsError.message}`
    //   );
    const [settingsDataWithDays, bookingWindowLimit] = await Promise.all([await supabase
      .from(settingsTable)
      .select("id, day")
      .eq(filterColumn, filterValue), getBookingWindowLimit()]);
    // Extract setting IDs
    console.log(settingsDataWithDays.data, "<=====================");
    if (settingsDataWithDays.error)
      throw new Error(
        `Error fetching availability settings: ${settingsDataWithDays.error.message}`
      );
    const settingIds = settingsDataWithDays.data.map(({ id }) => id);
    console.log("Setting IDs:", settingIds);
    // Fetch availability times
    // const { data: timesData, error: timesError } = await supabase
    //   .from("availability_times")
    //   .select("setting_id, available_from, available_to")
    //   .in("setting_id", settingIds);
    // console.log("Times Data:", timesData);
    // if (timesError)
    //   throw new Error(
    //     `Error fetching AvaliabilityTimes: ${timesError.message}`
    //   );

    // Structure response data
    const availabilityInfo = settingsDataWithDays.data.map((setting) => ({
      day: setting.day,
    }));

    // Return final response
    return new Response(JSON.stringify({availabilityInfo, bookingWindowLimit}), {
      headers: { ...corsHeaders, "Content-Type": "application/json" },
      status: 200,
    });
  } catch (error) {
    console.log(error);
    return new Response(JSON.stringify({ error: (error as Error).message }), {
      headers: { ...corsHeaders, "Content-Type": "application/json" },
      status: 400,
    });
  }
}
