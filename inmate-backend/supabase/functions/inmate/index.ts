import 'https://esm.sh/@supabase/functions-js/src/edge-runtime.d.ts';

import create from "./create/create.ts";
import edit from "./edit/edit.ts";
import getInfo from "./get_inmate_info/get_inmate_info.ts";
import getInmateDays from "./get_inmate_days/inmate_days.ts";
import deleteinmate from "./delete/delete.ts";
import { withLogging } from '../src/utils/logger.ts';  // Change from default import to named import

// CORS Headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Max-Age': '86400',
};

// Helper to add CORS headers
function withCors(response: Response) {
  const newHeaders = new Headers(response.headers);
  Object.entries(corsHeaders).forEach(([key, value]) => newHeaders.set(key, value));

  return new Response(response.body, {
    status: response.status,
    statusText: response.statusText,
    headers: newHeaders,
  });
}

// Function Entry Point
Deno.serve(withLogging(async (req) => {
  if (req.method === "OPTIONS") {
    return withCors(new Response(null, { status: 204 }));
  }

  const url = new URL(req.url);
  const pathSegments = url.pathname.split("/").filter(Boolean); // Extract path parts
  const action = pathSegments[1]; // Endpoint name (e.g., "get-rooms")

  // Extract query parameters dynamically
  const queryParams: Record<string, string | null> = {};
  url.searchParams.forEach((value, key) => {
    queryParams[key] = value;
  });

  try {
    switch (action) {
      case "get_info":
        return await getInfo(queryParams);
      //   // return await handleGetRooms(queryParams);
      case "create":
        if (req.method !== "POST") return withCors(new Response("Method Not Allowed", { status: 405 }));
        return await create(req);

      case "delete":
        if (req.method !== "DELETE") return withCors(new Response("Method Not Allowed", { status: 405 }));
        return await deleteinmate(req);
      case "edit":
        if (req.method !== "PUT") return withCors(new Response("Method Not Allowed", { status: 405 }));
        return await edit(req);
      case "get_days":
        if (req.method !== "GET") return withCors(new Response("Method Not Allowed", { status: 405 }));
        return await getInmateDays(queryParams);

      default:
        return withCors(new Response(JSON.stringify({ error: "Invalid endpoint" }), { status: 400 }));
    }
  } catch (error) {
    console.error("Unexpected error:", error);
    return withCors(new Response(JSON.stringify({ error: "Internal Server Error" }), { status: 500 }));
  }
},"inmate"));



