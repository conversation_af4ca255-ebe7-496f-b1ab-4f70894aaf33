// Setup type definitions for built-in Supabase Runtime APIs
import "https://esm.sh/@supabase/functions-js/src/edge-runtime.d.ts";
import {createClient} from "npm:@supabase/supabase-js@2.39.3";
import { withLogging } from '../src/utils/logger.ts';  // Change from default import to named import

// Initialize Supabase client
const supabase = createClient(Deno.env.get("SUPABASE_URL")!, Deno.env.get("SUPABASE_ANON_KEY")!);

// CORS headers
const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Methods": "POST, GET, OPTIONS",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
  "Access-Control-Max-Age": "86400",
  "Content-Type": "application/json",
};

Deno.serve(withLogging(async (req) => {
  if (req.method === "OPTIONS") {
    return withCors(new Response(null, { status: 204 }));
  }

  if (req.method !== "GET") {
    return withCors(new Response("Method Not Allowed", { status: 405 }));
  }

  try {
    const url = new URL(req.url);
    const visitorid = url.searchParams.get("visitorId");
    const keyword = url.searchParams.get("keyword") || "";
    const page = parseInt(url.searchParams.get("page") || "1");
    const pageSize = parseInt(url.searchParams.get("pageSize") || "10"); // Default to 10 per page

    console.log("Received query parameters:", { visitorid, keyword, page, pageSize });

    if (!visitorid) {
      return withCors(
        new Response(
          JSON.stringify({ error: "visitorid is required" }),
            {
              status: 400,
              headers: corsHeaders
            }// Explicitly setting Content-Type
        )
      );
    }
    const { data: inmate_visitor_count } = await supabase
      .from("inmate_visitors")
      .select("*")
      .eq("visitor_id", visitorid)

    const { data: inmate_visitor, error, count } = await supabase
      .from("inmate_visitors")
      .select("id, inmate_id(id, full_name ,inmate_code), visitor_id, relation")
      // .select("*")
      .eq("visitor_id", visitorid)
      .range((page - 1) * pageSize, page * pageSize - 1); // Implement pagination

    if (error) {
      console.error("Error fetching inmate_visitor data:", error);
      return withCors(
        new Response(
          JSON.stringify({ error: "Error fetching inmate_visitor data" }),
          { status: 500 ,
            headers: corsHeaders
          }
        )
      );
    }

    if (!inmate_visitor || inmate_visitor.length === 0) {
      console.log("No visitors found for the given inmate ID");
      return withCors(
        new Response(
          JSON.stringify({ message: "No inmate visitors for this visitor found" }),
          { status: 404 ,
            headers: corsHeaders
          }
        )
      );
    }

    // Fetch completed visits and documents for each visitor
    const visitorDataPromises = inmate_visitor.map(async (inmate) => {
      const { count: completedVisitsCount, error: visitError } = await supabase
        .from("visit_requests")
        .select("*", { count: "exact", head: true })
        .eq("visitor_id", visitorid)
        .eq("inmate_id", inmate.inmate_id.id)
        .eq("status_id", "6");

      if (visitError) {
        console.error(
          `Error fetching completed visits for visitor_id ${inmate.inmate_id.id}:`,
          visitError
        );
      }
      console.log("data", inmate);

      const { data: inmateVisitorDocuments, error: documentError } = await supabase
        .from("inmate_visitor_documents")
        .select("*")
        .eq("inmate_visitor_id", inmate.id);

      if (documentError) {
        console.error(
          `Error fetching documents for inmate_visitor_id ${inmate.id}:`,
          documentError
        );
      }
      console.log("asdffafdf",inmateVisitorDocuments)
      const inmateName = inmate.inmate_id?.full_name || "";
      return {
        inmate_name : inmateName,
        inmate_code: inmate.inmate_id.inmate_code,
        relation : inmate.relation,

        completedVisitsCount: completedVisitsCount || 0,
        documents: inmateVisitorDocuments,
      };
    });

    const visitorDataWithDetails = await Promise.all(visitorDataPromises);

    console.log("Fetched visitor data with details:", visitorDataWithDetails);

    // Apply keyword filter if provided (filtering by visitor full name)
    const filteredData = keyword
      ? visitorDataWithDetails.filter((item) =>
        item.inmate_name.toLowerCase().includes(keyword.toLowerCase())
      )
      : visitorDataWithDetails;

    // Calculate total pages
    // Calculate total pages based on filtered data count
    const totalCount = keyword 
    ? filteredData.length 
    : (count !== null && count !== undefined) 
      ? count 
      : visitorDataWithDetails.length;
    const datacount = inmate_visitor_count.length;
    console.log("datacount", inmate_visitor_count.length);
  const totalPages = Math.ceil(totalCount / pageSize);
    return withCors(
      new Response(
        JSON.stringify({
          data: filteredData,
          page,
          pageSize,
          total: datacount,
        }),
        { status: 200,
          headers: corsHeaders
          // Explicitly setting Content-Type
        }
      )
    );
  } catch (error) {
    console.error("Unexpected error:", error);
    return withCors(
      new Response(
        JSON.stringify({ error: "Internal Server Error" }),
        { status: 500 }
      )
    );
  }
},"Inmate Visitor Table Function"));

// Helper to add CORS headers
function withCors(response) {
  const newResponse = new Response(response.body, response);
  newResponse.headers.set("Access-Control-Allow-Origin", "*");
  newResponse.headers.set("Access-Control-Allow-Methods", "GET, OPTIONS");
  newResponse.headers.set("Access-Control-Allow-Headers", "Content-Type, Authorization");

  // Ensure Content-Type is set for JSON responses
  if (!newResponse.headers.has("Content-Type")) {
    newResponse.headers.set("Content-Type", "application/json");
  }

  return newResponse;
}



