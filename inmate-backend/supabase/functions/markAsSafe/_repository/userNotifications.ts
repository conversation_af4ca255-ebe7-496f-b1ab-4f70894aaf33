import { supabase } from "../../src/lib/SupabaseClient.ts";
import { NotFoundError } from "../../src/errors/notFoundError.ts";

export async function update(
  visit_id: string
) {

  const { data: conflictingVisits, error: checkError } = await supabase
    .from("users_notifications")
    .update({ title_id: 31 })
    .eq("visit_id", visit_id)
    .eq("title_id", 30);

  if (checkError) {
    if (checkError.code === "PGRST116") {
          throw new NotFoundError(
            `Notification not found for visit_id: ${visit_id}`
          );
      } else {
          throw new Error(`Error updating Notification title`);
        }
  }
}