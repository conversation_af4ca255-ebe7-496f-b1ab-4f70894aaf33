import { supabase } from "../../src/lib/SupabaseClient.ts";
import { NotFoundError } from "../../src/errors/notFoundError.ts";


export async function update(
  visit_id: string
) {

  const { data: conflictingVisits, error: checkError } = await supabase
    .from("visit_requests")
    .update({ is_flagged_word: false })
    .eq("id", visit_id)

  if (checkError) {
    if (checkError.code === "PGRST116") {
          throw new NotFoundError(
            `Visit request not found for visit_id: ${visit_id}`
          );
      } else {
          throw new Error(`Error updating Vist Request`);
        }
  }
}
