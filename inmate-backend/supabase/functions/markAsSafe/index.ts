import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { withLogging } from "../src/utils/logger.ts";
import {
  markVisitAsSafeSchema,
} from "./_dtos/dtos.ts";
import { markVisitAsSafe } from "./_services/markAsSafe.ts";

Deno.serve(
  withLogging(async (req) => {
    try {
      if (req.method === "PATCH") {
        const body = await req.json();

        const parsedBody = markVisitAsSafeSchema.safeParse(body);
        if (!parsedBody.success) {
          return new Response(
            JSON.stringify({
              error: "Invalid request body",
              details: parsedBody.error.format(),
            }),
            { status: 400 }
          );
        }

        const { visit_id } = parsedBody.data;
        await markVisitAsSafe(visit_id);
        return new Response(JSON.stringify({status: "success"}), {
          status: 200,
        });
      } 

      return new Response("Method not allowed", { status: 405 });
    } catch (error) {
          const status = error.statusCode || 500;
          const errorMessage = error.message || "Internal Server Error";
          const data = error.additionalProperties || {};

          return new Response(
            JSON.stringify({ error: errorMessage, data }),
            { status }
          );
        }
  }, "Overrides")
);
