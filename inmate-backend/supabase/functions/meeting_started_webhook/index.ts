// Setup type definitions for built-in Supabase Runtime APIs
import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import {createClient} from "npm:@supabase/supabase-js@2.39.3";

// Initialize Supabase client
const supabase = createClient(
    Deno.env.get("SUPABASE_URL")!,
    Deno.env.get("SUPABASE_ANON_KEY")!
);

// Define CORS headers
const headers = new Headers();
headers.set("Access-Control-Allow-Origin", "*"); // Allow all origins
headers.set("Access-Control-Allow-Methods", "GET, POST, OPTIONS"); // Allow methods
headers.set("Access-Control-Allow-Headers", "Content-Type, Authorization"); // Allow headers
headers.set("Access-Control-Allow-Credentials", "true");

// Define the interface for the incoming event
interface Room {
  room_info: {
    room_id: string; // The room_id to match with the meeting ID
  };
}

interface CommonNotifyEvent {
  event: 'room_started'; // We are interested in this event type
  room: Room;
}

// Serve function for webhook
Deno.serve(async (req) => {

  console.log("xxxxxxxxxxxxxxxxxxxxxxxx get in func")
  if (req.method === "OPTIONS") {
    // Handle CORS preflight requests
    return new Response(null, { headers });
  }

  if (req.method === "POST") {
    try {
      // Parse the incoming request body as JSON
      const body = await req.json() as CommonNotifyEvent;

      // Check if the event is 'room_started'
      if (body.event !== 'room_started') {
        return new Response(JSON.stringify({ error: "Invalid event type" }), { headers, status: 400 });
      }


      const roomId = body.room.room_info.room_id;
      

      console.log("room id >>> " , roomId )
      console.log("body . room  >>> "  , body.room)

      // Ensure room_id is present
      if (!roomId) {
        return new Response(JSON.stringify({ error: "Missing room_id" }), { headers, status: 400 });
      }

      // Update the meeting where id matches the room_id and set started_at to the current time
      const { data, error } = await supabase
        .from('visit_requests') // Your Meeting table
        .update({
          status: "5",               // Set status to STARTED
          started_at: new Date().toISOString(),  // Set started_at to the current time
        })
        .eq('id', roomId); // Update where the id matches room_id

      // Check for any errors during the update
      if (error) {
        return new Response(JSON.stringify({ error: error.message }), { headers, status: 400 });
      }

      // Return success response
      return new Response(JSON.stringify({ message: "Meeting updated successfully", data }), { headers, status: 200 });

    } catch (err) {
      // Handle unexpected errors
      return new Response(JSON.stringify({ error: "Internal Server Error" }), { headers, status: 500 });
    }
  }

  // Handle any other methods (e.g., GET, etc.)
  return new Response("Method not allowed", { status: 405, headers });
});

/* To invoke locally:

  1. Run `supabase start` (see: https://supabase.com/docs/reference/cli/supabase-start)
  2. Make an HTTP request:

  curl -i --location --request POST 'http://127.0.0.1:54321/functions/v1/meeting_started_webhook' \
    --header 'Authorization: Bearer <your_auth_token>' \
    --header 'Content-Type: application/json' \
    --data '{"event":"room_started", "room": { "room_info": { "room_id": "some-room-id" }}}'

*/

