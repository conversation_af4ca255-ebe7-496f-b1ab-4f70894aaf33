import { supabase } from "../../src/lib/SupabaseClient.ts";
import { NotFoundError } from "../../src/errors/notFoundError.ts";
import { CustomError } from "../../src/errors/customError.ts";
import { getAvailability as getSectionAvailability } from "./section.ts";
export async function getAvailability(room_id: string, date: string) {

  const { data: room, error: roomError } = await supabase
    .from("rooms")
    .select("id, is_building_default, section_id, active")
    .eq("id", room_id)
    .single();
  if (roomError) {
    if (roomError.code === "PGRST116") {
      throw new NotFoundError("The Room Does Not Exist");
    } else {
      throw new Error(`Error Fetching Room: ${roomError.message}`);
    }
  }
  if (!room.active) {
    throw new CustomError("The Room is Inactive", 450);
  }
  // if (room.is_building_default) {
  //   // If the room is a building default, we need to fetch the section settings
  //   return getSectionAvailability(room.section_id, date);
  // }
  // const { data: roomSettings, error: sectionError } = await supabase
  //   .from("room_availability_settings")
  //   .select("id")
  //   .eq("room_id", room_id)
  //   .eq(
  //     "day",
  //     new Date(date).toLocaleString("en-US", { weekday: "long" }).toLowerCase()
  //   );

  // if (sectionError) {
  //   console.error("Error fetching room settings:", sectionError);
  //   throw new Error(
  //     "Server Error"
  //   );
  // }

  // let settings: any[] = [];
  // if (roomSettings && roomSettings.length !== 0) {
  //   const settingIds = roomSettings.map((setting) => setting.id);

  //   // Now get the availability_times using the setting IDs
  //   const { data: availabilityTimes, error: timesError } = await supabase
  //     .from("availability_times")
  //     .select("setting_id, type, available_from, available_to")
  //     .eq("type", "room")
  //     .in("setting_id", settingIds);

  //   if (timesError) {
  //     throw new Error(
  //       "Server Error"
  //     );
  //   }
  //   if (!availabilityTimes || availabilityTimes.length === 0) {
  //     throw new CustomError("Please Set Availability Times for the Room", 451);
  //   }
  //   settings = availabilityTimes.map((time) => ({
  //     setting_id: time.setting_id,
  //     startTime: new Date(`${date}T${time.available_from}`),
  //     endTime: new Date(`${date}T${time.available_to}`),
  //   }));
  // }
  // return settings;
}
