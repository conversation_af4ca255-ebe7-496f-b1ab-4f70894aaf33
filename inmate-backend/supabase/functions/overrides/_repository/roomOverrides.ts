import { supabase } from "../../src/lib/SupabaseClient.ts";
import { CustomError } from "../../src/errors/customError.ts";/**
 * Validate if there are any section overrides for a given section on a specific date.
 * If there are, it throws a ValidationError.
 *
 * @param {string} section_id - The ID of the section to check for overrides.
 * @param {string} date - The date to check for overrides in 'YYYY-MM-DD' format.
 * @returns {Promise<any>} - Returns the filtered overrides if no validation error is thrown.
 * @throws {ValidationError} - Throws an error if there are overrides for the specified date.
 */
export async function validateOverrideDate(
  roomId: string,
  date: string
): Promise<any> {
  // Call the get function from the repository
  const { data, error } = await supabase
    .from("overrides")
    .select(
      "id, room_id, override_dates(id, date), overrides_timeslots ( id, starttime, endtime )"
    )
    .eq("room_id", roomId);

  if (error) {
    throw new Error("Server Error");
  }
  const filteredOverrides = data
    .map((override) => {
      // Filter to only include overrides that have the specific date
      override.override_dates = override.override_dates.filter(
        (overrideDate) => overrideDate.date === date
      );
      return override;
    })
    .filter((override) => override.override_dates.length > 0);
  if (filteredOverrides.length > 0) {
    throw new CustomError(
      `There are room overrides for room ${roomId} on date ${date}`, 454
    );
  }
  return filteredOverrides;
}
