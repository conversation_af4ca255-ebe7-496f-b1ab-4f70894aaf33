import { supabase } from "../../src/lib/SupabaseClient.ts";
import { CustomError } from "../../src/errors/customError.ts";

export async function getAvailability(section_id: string, date: string) {
  const { data: sectionSettings, error: sectionError } = await supabase
    .from("section_availability_settings")
    .select("id")
    .eq("section_id", section_id)
    .eq(
      "day",
      new Date(date).toLocaleString("en-US", { weekday: "long" }).toLowerCase()
    );

  if (sectionError) {
    console.error("Error fetching section settings:", sectionError);
    throw new Error("Server Error");
  }

  let settings: any[] = [];
  if (sectionSettings && sectionSettings.length !== 0) {
    const settingIds = sectionSettings.map((setting) => setting.id);

    // Now get the availability_times using the setting IDs
    const { data: availabilityTimes, error: timesError } = await supabase
      .from("availability_times")
      .select("setting_id, type, available_from, available_to")
      .eq("type", "section")
      .in("setting_id", settingIds);

    if (timesError) {
      throw new Error("Server Error");
    }

    if (!availabilityTimes || availabilityTimes.length === 0) {
      throw new CustomError(
        "Please set availability settings for the building that the room is in",
        452
      );
    }

    settings = availabilityTimes.map((time) => ({
      setting_id: time.setting_id,
      startTime: new Date(`${date}T${time.available_from}`),
      endTime: new Date(`${date}T${time.available_to}`),
    }));
  }
  return settings;
}
