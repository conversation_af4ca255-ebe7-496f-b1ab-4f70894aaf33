import { supabase } from "../../src/lib/SupabaseClient.ts";


export async function getVisits(
  date: string,
  room_id: string
) {
  // First, check if there are any conflicting visit requests
  const allowedStatusIds = [
    Number(Deno.env.get("VISIT_REQUEST_STATUS_COMPLETED")) || 6,
    Number(Deno.env.get("VISIT_REQUEST_STATUS_APPROVED")) || 2,
    Number(Deno.env.get("VISIT_REQUEST_STATUS_PENDING")) || 1,
    Number(Deno.env.get("VISIT_REQUEST_STATUS_STARTING_SOON")) || 3,
    Number(Deno.env.get("VISIT_REQUEST_STATUS_AWAITING_ADMIN")) || 4,
  ];

  const { data: conflictingVisits, error: checkError } = await supabase
    .from("visit_requests")
    .select("id, datetime, room_id, visit_number, duration")
    .eq("room_id", room_id)
    .eq("test", false)
    .gte("datetime", `${date}T00:00:00.000Z`)
    .lte("datetime", `${date}T23:59:59.999Z`)
    .in("status_id", allowedStatusIds);

  if (checkError) {
    if (checkError.code === "PGRST116") {
          // Do nothing for this specific error code
      } else {
          throw new Error(`Error updating override date: ${checkError.message}`);
        }
  }
  return conflictingVisits;
}
