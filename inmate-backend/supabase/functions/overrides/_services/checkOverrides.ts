import { CustomError } from "../../src/errors/customError.ts";
export async function checkOverridesInAvailableTimes(settings, slot, date) {

  // Check if the slot is within the available times
  const isAvailable = settings.some(
    (setting) =>
      new Date(`${date}T${slot.startTime}`) >= setting.startTime &&
      new Date(`${date}T${slot.endTime}`) <= setting.endTime
  );
  if (!isAvailable) {
    throw new CustomError(
      `Cannot create slot ${slot.startTime}-${slot.endTime}: not within available times`,
      453,
      {
        slotStart: slot.startTime,
        slotEnd: slot.endTime,
      }

    );
  }
}
