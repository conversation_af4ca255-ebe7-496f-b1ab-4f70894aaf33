import {
  create as createOverrideRepository,
  getOverride as getOverrideRepository,
  deleteOverride as deleteOverrideRepository,
  createSlots as createSlotsRepository,
  deleteSlots as deleteSlotsRepository,
  deleteOverrideDate as deleteOverrideDateRepository,
  createDates as createDatesRepository,
} from "../_repository/overrides.ts";
import { validateOverrideDate } from "../_repository/roomOverrides.ts";
import { getVisits } from "../_repository/visitRequests.ts";
import { getAvailability } from "../_repository/room.ts";
import { checkOverridesInAvailableTimes } from "./checkOverrides.ts";
import { convertVisitTime } from "./convertVisit.ts";
import { generalAntiMatchSlots } from "./generalAntiMatchSlots.ts";
import { Slots, DateType, createOverrideSchema } from "../_dtos/dtos.ts";
export const deleteOverride = async (id: string) => {
  await deleteOverrideRepository(id);
};

export const getOverride = async ({
  id,
  room_id,
}: {
  id: string | null;
  room_id: string | null;
}) => {
  const override = await getOverrideRepository({ id, room_id });

  return override;
};

export async function createOverride(
  room_id: string,
  dates: DateType[],
  slots: Slots[]
) {
  await Promise.all(
    dates.map((singleDate) => checkBySingleDate(room_id, singleDate, slots))
  );

  const result = await createOverrideRepository(room_id, dates, slots);
  return result;
}

export async function updateOverride(
  override_id: string,
  dates: DateType[] | null | undefined,
  updateSlots: Slots[] | null | undefined
) {
  let override: any = {};
  if (dates && updateSlots) {
    override = await getOverrideRepository({ id: override_id, room_id: null });
    await Promise.all(
      dates.map((singleDate) =>
        checkBySingleDateUpdate(override.room_id, singleDate, updateSlots)
      )
    );
    createOverrideSchema.safeParse({
      room_id: override.room_id,
      dates,
      slots: updateSlots,
    });
    await deleteOverrideDateRepository(override_id);
    override.dates = await createDatesRepository(override_id, dates);
    await deleteSlotsRepository(override_id);
    override.timeslots = await createSlotsRepository(override_id, updateSlots);
  } else if (dates) {
    override = await getOverrideRepository({ id: override_id, room_id: null });
    const slots = override.overrides_timeslots.map((slot) => ({
      startTime: slot.starttime,
      endTime: slot.endtime,
    }));
    await Promise.all(
      dates.map((singleDate) =>
        checkBySingleDateUpdate(override.room_id, singleDate, slots)
      )
    );
    createOverrideSchema.safeParse({
      room_id: override.room_id,
      dates,
      slots,
    });
    await deleteOverrideDateRepository(override_id);
    override.dates = await createDatesRepository(override_id, dates);
  } else if (updateSlots) {
    override = await getOverrideRepository({ id: override_id, room_id: null });

    Promise.all(
      override.override_dates.map((singleDate) =>
        checkBySingleDateUpdate(override.room_id, singleDate.date, updateSlots)
      )
    );
    createOverrideSchema.safeParse({
      room_id: override.room_id,
      dates: override.override_dates.map((date) => date.date),
      slots: updateSlots,
    });
    await deleteSlotsRepository(override_id);
    override.overrides_timeslots = await createSlotsRepository(
      override_id,
      updateSlots
    );
  }
  return override;
}

async function checkBySingleDate(
  room_id: string,
  date: DateType,
  slots: Slots[]
) {
  await getAvailability(room_id, date);
  // await Promise.all(
  //   slots.map((slot) =>
  //     checkOverridesInAvailableTimes(availableTimes, slot, [date])
  //   )
  // );
  await validateOverrideDate(room_id, date);
  const visits = await getVisits(date, room_id);
  const visitTimes = visits.map((visit) => convertVisitTime(visit));
  generalAntiMatchSlots(visitTimes, slots, date);
}

async function checkBySingleDateUpdate(
  room_id: string,
  date: DateType,
  slots: Slots[]
) {
  await getAvailability(room_id, date);
  // await Promise.all(
  //   slots.map((slot) =>
  //     checkOverridesInAvailableTimes(availableTimes, slot, [date])
  //   )
  // );
  const visits = await getVisits(date, room_id);
  const visitTimes = visits.map((visit) => convertVisitTime(visit));
  generalAntiMatchSlots(visitTimes, slots, date);
}
