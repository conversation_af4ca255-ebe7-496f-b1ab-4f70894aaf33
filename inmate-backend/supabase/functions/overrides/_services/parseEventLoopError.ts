// Add this function to parse event loop errors
export function parseEventLoopError(
  errorMessage: string
): { type: string; message: string } | null {
  try {
    // Look for ValidationError in the message
    const validationErrorMatch = errorMessage.match(
      /ValidationError: (.+?)(?:\n|$)/
    );
    if (validationErrorMatch) {
      return {
        type: "ValidationError",
        message: validationErrorMatch[1],
      };
    }

    // Look for NotFoundError in the message
    const notFoundErrorMatch = errorMessage.match(
      /NotFoundError: (.+?)(?:\n|$)/
    );
    if (notFoundErrorMatch) {
      return {
        type: "NotFoundError",
        message: notFoundErrorMatch[1],
      };
    }

    // Look for other known error types
    const genericErrorMatch = errorMessage.match(/Error: (.+?)(?:\n|$)/);
    if (genericErrorMatch) {
      return {
        type: "Error",
        message: genericErrorMatch[1],
      };
    }

    return null;
  } catch (e) {
    return null;
  }
}
