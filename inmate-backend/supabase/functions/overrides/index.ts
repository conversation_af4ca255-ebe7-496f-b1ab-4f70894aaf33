import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { withLogging } from "../src/utils/logger.ts";
import { ValidationError} from "../src/errors/validationError.ts";
import { NotFoundError } from "../src/errors/notFoundError.ts";
import {
  getOverride,
  deleteOverride,
  createOverride,
  updateOverride,
} from "./_services/overrides.ts";
import {
  createOverrideSchema,
  patchOverrideSchema,
  uuidSchema,
} from "./_dtos/dtos.ts";
import { parseEventLoopError } from "./_services/parseEventLoopError.ts";

Deno.serve(
  withLogging(async (req) => {
    try {
      if (req.method === "POST") {
        const body = await req.json();

        const parsedBody = createOverrideSchema.safeParse(body);
        if (!parsedBody.success) {
          return new Response(
            JSON.stringify({
              error: "Invalid request body",
              details: parsedBody.error.format(),
            }),
            { status: 400 }
          );
        }

        const { room_id, dates, slots } = parsedBody.data;
        const override = await createOverride(room_id, dates, slots);
        return new Response(JSON.stringify(override), {
          status: 201,
        });
      } else if (req.method === "PATCH") {
        const body = await req.json();

        const parsedBody = patchOverrideSchema.safeParse(body);
        if (!parsedBody.success) {
          return new Response(
            JSON.stringify({
              error: "Invalid request body",
              details: parsedBody.error.format(),
            }),
            { status: 400 }
          );
        }
        const { id, dates, slots } = parsedBody.data;
        const override = await updateOverride(id, dates, slots);
        return new Response(JSON.stringify(override), {
          status: 200,
        });
      } else if (req.method === "DELETE") {
        const params = new URL(req.url).pathname;
        const deleteId = params.split("/")[2];
        const parsedParams = uuidSchema.safeParse(deleteId);
        if (!parsedParams.success) {
          return new Response(
            JSON.stringify({
              error: "Invalid request body",
              details: parsedParams.error.format(),
            }),
            { status: 400 }
          );
        }
        await deleteOverride(parsedParams.data);
        return new Response("OK", { status: 200 });
      } else if (req.method === "GET") {
        const params = new URL(req.url).pathname;
        const pathList = params.split("/");
        if (pathList.length < 3) {
          return new Response(
            JSON.stringify({
              error: "Invalid request parameters",
              details: "Missing room or date parameter",
            }),
            { status: 400 }
          );
        }
        if (pathList[2] === "room") {
          if (pathList.length < 4) {
            return new Response(
              JSON.stringify({
                error: "Invalid request parameters",
                details: "Missing room ID parameter",
              }),
              { status: 400 }
            );
          }
          const roomId = pathList[3];
          const parsedParams = uuidSchema.safeParse(roomId);
          if (!parsedParams.success) {
            return new Response(
              JSON.stringify({
                error: "Invalid request parameters",
                details: parsedParams.error.format(),
              }),
              { status: 400 }
            );
          }
          const overrides = await getOverride({
            id: null,
            room_id: parsedParams.data,
          });
          return new Response(JSON.stringify(overrides), {
            status: 200,
            headers: {
              "Content-Type": "application/json",
            },
          });
        }
        const overrideId = pathList[2];
        const parsedParams = uuidSchema.safeParse(overrideId);
        if (!parsedParams.success) {
          return new Response(
            JSON.stringify({
              error: "Invalid request parameters",
              details: parsedParams.error.format(),
            }),
            { status: 400 }
          );
        }
        const override = await getOverride({
          id: overrideId,
          room_id: null,
        });
        return new Response(JSON.stringify(override), {
          status: 200,
          headers: {
            "Content-Type": "application/json",
          },
        });
      }
      return new Response("Method not allowed", { status: 405 });
    } catch (error) {
          const status = error.statusCode || 500;
          const errorMessage = error.message || "Internal Server Error";
          const data = error.additionalProperties || {};

          return new Response(
            JSON.stringify({ error: errorMessage, data }),
            { status }
          );
        }
  }, "Overrides")
);
