import { supabase } from "../../src/lib/SupabaseClient.ts";

export const getMeetingBetweenInterval = async (): Promise<number> => {
  try {
    const setting = Deno.env.get("BETWEEN_MEETING_DURATION_KEY") || "interval_between_meetings";
    const { data, error } = await supabase
      .from("settings")
      .select("value")
      .eq("key", setting)
      .single(); // Ensure this is properly terminated

    if (error) {
      console.error("Error fetching meeting duration:", error);
      console.log("Using default meeting duration: 30 minutes");
      return 5; // Default to 30 minutes if there's an error
    }

    let interval = 5;
    const interval_between_meetings = parseInt(data.value, 10);
    if (
          isNaN(interval_between_meetings) ||
          interval_between_meetings <= 0
        ) {
          console.warn(
            `Invalid interval between meetings in database: ${data.value}, using default: ${interval}`
          );
        } else {
          interval = interval_between_meetings;
        }
  
    return interval;
  } catch (e) {
    console.error("Exception in getMeetingDuration:", e);
    return 5; // Default value as fallback
  }
};
