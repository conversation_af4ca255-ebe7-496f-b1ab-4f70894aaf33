import { supabase } from "../../src/lib/SupabaseClient.ts";

export async function getOverrides(
  roomIds: string[],
  date: string
): Promise<any> {
  // Call the get function from the repository
  const { data, error } = await supabase
    .from("overrides")
    .select("id, room_id, date, overrides_timeslots ( id, starttime, endtime )")
    .in("room_id", roomIds)
    .eq("date", date);

  if (error) {
    throw new Error(`Error fetching overrides: ${error.message}`);
  }
  return data;
}
