import { supabase } from "../../src/lib/SupabaseClient.ts";

export const getSectionRooms = async (roomId: string) => {
    const { data: roomData } = await supabase
        .from("rooms")
        .select(
          `section:section_id(rooms(id, active))
        )
      `
        )
        .eq("id", roomId)
        .eq("section.rooms.active", true)
        .neq("section.rooms.id", roomId)
        .single();
    return roomData.section.rooms;
}
