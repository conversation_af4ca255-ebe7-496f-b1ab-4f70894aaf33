import { supabase } from "../../src/lib/SupabaseClient.ts";

export const getRooms = async (sectionId: string) => {
    const { data: sectionData } = await supabase
        .from("sections")
        .select(
          `    rooms:rooms(
          id,
          active
        )
      `
        )
        .eq("id", sectionId)
        .eq("rooms.active", true)
        .single();
    return sectionData.rooms;
}
