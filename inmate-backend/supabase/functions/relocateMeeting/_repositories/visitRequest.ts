import { supabase } from "../../src/lib/SupabaseClient.ts";
import { ValidationError } from "../../src/errors/validationError.ts";

export const updateVisitRequest = async (
  visitRequestId: string,
  roomId: string) => {
    const { data, error } = await supabase
        .from("visit_requests")
        .update({
        room_id: roomId})
        .eq("id", visitRequestId)
        .select("*");
    if (error) {
      console.error("Error updating visit request:", error);
      throw new Error("Error updating visit request");
    }
    return data;
  };

export const checkConsistentVisitRequest = async (
  visitRequestId: string,
) => {
  const { data, error } = await supabase
    .from("visit_requests")
    .select("datetime, duration, room_id")
    .eq("id", visitRequestId)
    .single();

  if (error) {
    throw new ValidationError("Wrong Visit ID Or Visit request and room ID do not match");
  }
  return [data.datetime, data.duration, data.room_id];
}
