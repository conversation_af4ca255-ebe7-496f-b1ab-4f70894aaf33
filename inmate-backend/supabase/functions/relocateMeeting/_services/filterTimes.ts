import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { supabase } from "../../src/lib/SupabaseClient.ts";
import { convertVisitTime } from "../../src/utils/convertVisit.ts";
import { NotAvailableError } from "../../src/errors/notAvailalbeError.ts";
import { getRoomSettings } from "./getRoomSetting.ts";
import { matchSlots } from "./matchSlots.ts";
import { antiMatchSlots } from "./antiMatchSlots.ts";
import { getSectionRooms } from "../_repositories/room.ts";
import { getOverridesAsRoomsSlots } from "./overrides.ts";

const pipe =
  (...fns: Function[]) =>
  (initialValue: any) =>
    fns.reduce((value, fn) => fn(value), initialValue);

export const filterTimes = async (
  visitRequestId: string,
  roomId: string,
  settings: any,
  fromDateStr,
  toDateStr,
  date,
  availabilityTimesService
) => {
  const roomsIds = (await getSectionRooms(roomId)).map((room: any) => room.id);
  if (roomsIds.length === 0) {
    throw new NotAvailableError("No active rooms found in the section.");
  }

  const roomsettings = roomsIds.map((roomId) => {
    return getRoomSettings(roomId, date, availabilityTimesService);
  });
  const roomSettings = await Promise.all(roomsettings);
  const defaultSlots = roomSettings.flatMap((room) =>
    room.settings
      ? room.settings.map((setting) => ({
          startTime: setting.startTime,
          endTime: setting.endTime,
          room_id: room.room_id,
        }))
      : []
  );
  const allowedStatusIds = [
    Number(Deno.env.get("VISIT_REQUEST_STATUS_COMPLETED")) || 6,
    Number(Deno.env.get("VISIT_REQUEST_STATUS_APPROVED")) || 2,
    Number(Deno.env.get("VISIT_REQUEST_STATUS_PENDING")) || 1,
    Number(Deno.env.get("VISIT_REQUEST_STATUS_STARTING_SOON")) || 3,
    Number(Deno.env.get("VISIT_REQUEST_STATUS_AWAITING_ADMIN")) || 4,
  ];
  const [{ data: roomSlotsData }, overrides] = await Promise.all([supabase
    .from("visit_requests")
    .select(
      `room_id,
      datetime,
      from,
      to`
    )
    .gte("datetime", fromDateStr)
    .lte("datetime", toDateStr)
    .in("status_id", allowedStatusIds)
    .in("room_id", roomsIds)
    .neq("id", visitRequestId),
    getOverridesAsRoomsSlots(roomsIds, date)]);

  const roomSlotsDataFormatted = roomSlotsData.map((slot: any) =>
    convertVisitTime(slot)
  );
  const filterPipeline = pipe(
    (slots) => antiMatchSlots(roomSlotsDataFormatted, slots),
    (slots) => antiMatchSlots(overrides, slots),
  );
  const filteredSlots = filterPipeline(defaultSlots);
  if (filteredSlots.length === 0) {
    throw new NotAvailableError("No Available slots found in the section.");
  }
  const matchsettings = matchSlots(filteredSlots, settings);
  if (matchsettings.length === 0) {
    throw new NotAvailableError("No Available slots found in the section.");
  }
  return matchsettings[0];
};
