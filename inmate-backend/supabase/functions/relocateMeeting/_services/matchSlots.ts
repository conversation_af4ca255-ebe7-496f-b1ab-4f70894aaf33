import { NotAvailableError } from "../../src/errors/notAvailalbeError.ts";

export const matchSlots = (slotsA, slotsB) => {
  if (!slotsA || !slotsB) {
    throw new NotAvailableError("No Available slots found in the section.");
  }
  return slotsA.filter((slotA) => {
    for (let i = 0; i < slotsB.length; i++) {
      const slotB = slotsB[i];
      if (
        slotB.startTime >= slotA.startTime &&
        slotB.endTime <= slotA.endTime
      ) {
        return true;
      }
    }
    return false;
  });
};
