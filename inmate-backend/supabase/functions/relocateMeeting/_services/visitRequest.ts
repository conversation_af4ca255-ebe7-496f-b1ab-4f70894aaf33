import { updateVisitRequest } from "../_repositories/visitRequest.ts";
import { checkConsistentVisitRequest as checkVisitRequest } from "../_repositories/visitRequest.ts";

export const relocateVisitRequest = async (
  visitRequestId: string,
  roomId: string,
) => {
  return await updateVisitRequest(
    visitRequestId,
    roomId,
  );
}

export const checkConsistentVisitRequest = async (
  visitRequestId: string) => {
    return await checkVisitRequest(
        visitRequestId,
    );
  }