import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { ResponseHelper } from "../src/utils/ResponseHelper.ts";
import { withLogging } from "../src/utils/logger.ts"; // Change from default import to named import
import { NotAvailableError } from "../src/errors/notAvailalbeError.ts";
import { filterTimes } from "./_services/filterTimes.ts";
import { AvailabilityTimesService } from "../src/services/AvailabilityTimesService.ts";
import { UUID } from "../src/utils/uuidValidator.ts";
import { updateVisitRequest } from "./_repositories/visitRequest.ts";
import { ValidationError } from "../src/errors/validationError.ts";
import { checkConsistentVisitRequest } from "./_services/visitRequest.ts";
import { getMeetingBetweenInterval } from "./_repositories/meetingBetweenInterval.ts";

Deno.serve(
  withLogging(async (req) => {
    if (req.method === "OPTIONS") {
      return ResponseHelper.successResponse(null, "CORS preflight response");
    }

    try {
      if (req.method !== "POST") {
        return ResponseHelper.errorResponse(
          "Invalid request method, only POST is allowed",
          405
        );
      }

      // Parse and validate request body
      let requestBody;
      try {
        requestBody = await req.json();
      } catch (e) {
        return ResponseHelper.errorResponse("Invalid JSON body", 400);
      }
      const { visitRequestId } = requestBody;
      if ( !visitRequestId ) {
        return ResponseHelper.errorResponse("Missing required fields", 400);
      }
      const visitRequestIdUUID = UUID(visitRequestId);

      const [datetime, duration, roomId] = await checkConsistentVisitRequest(visitRequestIdUUID);

      const durationInt = parseInt(duration, 10);
      const intervalBetweenMeetings =
        await getMeetingBetweenInterval();
      const availabilityTimesService = new AvailabilityTimesService(
        durationInt, intervalBetweenMeetings
      );
      const fromDate = new Date(datetime);
      const toDate = new Date(fromDate.getTime() + durationInt * 60 * 1000);
      const updatedSetting = await filterTimes(
        visitRequestIdUUID,
        roomId,
        [
          {
            startTime: new Date(datetime),
            endTime: new Date(
              new Date(datetime).getTime() + durationInt * 60 * 1000
            ),
          },
        ],
        datetime,
        toDate.toISOString(),
        datetime.split("T")[0],
        availabilityTimesService,
      );
      const updatedVisitRequest = await updateVisitRequest(
        visitRequestIdUUID,
        updatedSetting.room_id,
      );
      return ResponseHelper.successResponse(
        updatedVisitRequest,
        `Visit request relocated successfully to  ${updatedSetting.room_id}`
      );
    } catch (error) {
      if (error instanceof ValidationError) {
        return ResponseHelper.errorResponse(error.message, 400);
      }
      if (error instanceof NotAvailableError) {
        return ResponseHelper.errorResponse(error.message, 423);
      }
      console.error("Error in Edge Function:", error);
      return ResponseHelper.errorResponse("Internal Server Error", 500);
    }
  }, "availabilitySlots")
);
