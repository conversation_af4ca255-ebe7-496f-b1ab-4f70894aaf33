# Reserve Slot Endpoint Documentation

## Overview
The `/reserveSlot` endpoint allows visitors to reserve a time slot for visiting an inmate. It validates the request, checks slot availability, and creates a visit reservation.

## Endpoint Details

- **URL**: `/reserveSlot`
- **Method**: `POST`
- **Content-Type**: `application/json`

## Request Body Schema

```typescript
{
  inmateId: string;    // UUID format - ID of the inmate to visit
  visitorId: string;   // UUID format - ID of the visitor making the reservation
  date: string;        // Date format (YYYY-MM-DD) - Must be today or in the future
  slot: {
    startTime: string; // ISO 8601 datetime - Must be in the future
    endTime: string;   // ISO 8601 datetime - Must be after startTime and in the future
    room_id: string;   // UUID format - ID of the room for the visit
  }
}
```

## Validation Rules

### Date Validation
- **Date**: Must be today or in the future
- **StartTime**: Must be a valid ISO 8601 datetime in the future
- **EndTime**: Must be a valid ISO 8601 datetime in the future and after startTime

### UUID Validation
- **inmateId**: Must be a valid UUID
- **visitorId**: Must be a valid UUID  
- **room_id**: Must be a valid UUID

### Business Logic Validation
- The requested time slot must be available
- The inmate must have valid settings configured
- The room must be available for the specified time

## Request Examples

### Valid Request
```json
{
  "inmateId": "123e4567-e89b-12d3-a456-************",
  "visitorId": "987fcdeb-51d2-43a1-b123-456789abcdef",
  "date": "2025-06-24",
  "slot": {
    "startTime": "2025-06-24T14:00:00.000Z",
    "endTime": "2025-06-24T15:00:00.000Z",
    "room_id": "456e7890-e89b-12d3-a456-************"
  }
}
```

### Invalid Request Examples

#### Invalid UUID
```json
{
  "inmateId": "invalid-uuid",
  "visitorId": "987fcdeb-51d2-43a1-b123-456789abcdef",
  "date": "2025-06-24",
  "slot": {
    "startTime": "2025-06-24T14:00:00.000Z",
    "endTime": "2025-06-24T15:00:00.000Z",
    "room_id": "456e7890-e89b-12d3-a456-************"
  }
}
```

#### Past Date
```json
{
  "inmateId": "123e4567-e89b-12d3-a456-************",
  "visitorId": "987fcdeb-51d2-43a1-b123-456789abcdef",
  "date": "2025-06-22", // Past date
  "slot": {
    "startTime": "2025-06-22T14:00:00.000Z",
    "endTime": "2025-06-22T15:00:00.000Z",
    "room_id": "456e7890-e89b-12d3-a456-************"
  }
}
```

#### End Time Before Start Time
```json
{
  "inmateId": "123e4567-e89b-12d3-a456-************",
  "visitorId": "987fcdeb-51d2-43a1-b123-456789abcdef",
  "date": "2025-06-24",
  "slot": {
    "startTime": "2025-06-24T15:00:00.000Z",
    "endTime": "2025-06-24T14:00:00.000Z", // Before start time
    "room_id": "456e7890-e89b-12d3-a456-************"
  }
}
```

## Response Formats

### Success Response (200)
```json
{
  "success": true,
  "data": {
    "message": "Successfully reserved the slot 2025-06-24T14:00:00.000Z - 2025-06-24T15:00:00.000Z room ID: 456e7890-e89b-12d3-a456-************",
    "reservation": {
      "startTime": "2025-06-24T14:00:00.000Z",
      "endTime": "2025-06-24T15:00:00.000Z",
      "roomId": "456e7890-e89b-12d3-a456-************",
      "inmateId": "123e4567-e89b-12d3-a456-************",
      "visitorId": "987fcdeb-51d2-43a1-b123-456789abcdef",
      "reservationDate": "2025-06-24"
    }
  }
}
```

### Error Responses

#### 400 - Bad Request (Invalid Input)
```json
{
  "error": "Invalid request body",
  "details": {
    "inmateId": {
      "_errors": ["Invalid uuid"]
    }
  }
}
```

#### 404 - Not Found (Inmate Not Found)
```json
{
  "success": false,
  "error": "No settings found for the given inmate"
}
```

#### 405 - Method Not Allowed
```json
{
  "success": false,
  "error": "Invalid request method, only POST is allowed"
}
```

#### 450 - Slot Not Available
```json
{
  "success": false,
  "error": "The selected time slot is not available"
}
```

#### 500 - Internal Server Error
```json
{
  "success": false,
  "error": "Internal Server Error"
}
```

## HTTP Status Codes

| Status Code | Description |
|-------------|-------------|
| 200 | Success - Slot reserved successfully |
| 400 | Bad Request - Invalid request body or validation failed |
| 404 | Not Found - Inmate not found or no settings available |
| 405 | Method Not Allowed - Only POST method is supported |
| 450 | Custom - Time slot not available |
| 500 | Internal Server Error - Server-side error occurred |

## Headers

### Required Headers
```
Content-Type: application/json
```

### Optional Headers
```
Authorization: Bearer <token>  // If authentication is implemented
```

## Business Logic Flow

1. **Method Validation**: Ensures only POST requests are accepted
2. **Schema Validation**: Validates request body against Zod schema
3. **Availability Check**: 
   - Retrieves inmate settings
   - Gets meeting duration and intervals
   - Filters available time slots
   - Checks if requested slot matches available slots
4. **Reservation**: Creates the visit reservation if slot is available
5. **Response**: Returns success message with reservation details

## Dependencies

- **AvailabilityTimesService**: Manages time slot availability logic
- **getSettings**: Retrieves inmate configuration and availability settings
- **filterTimes**: Filters available time slots based on various criteria
- **getMeetingDuration**: Gets meeting duration and interval settings
- **reserveVisit**: Creates the actual visit reservation in the database

## Error Handling

The endpoint implements comprehensive error handling:
- Input validation errors return detailed field-level error messages
- Business logic errors return descriptive error messages
- Server errors are logged and return generic error messages to clients
- All errors include appropriate HTTP status codes

## Rate Limiting

*Note: Rate limiting is not currently implemented but should be considered for production use.*

## Authentication

*Note: Authentication is not currently implemented in this endpoint but should be added for security.*

## Examples with cURL

### Successful Reservation
```bash
curl -X POST https://your-domain.supabase.co/functions/v1/reserveSlot \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "inmateId": "123e4567-e89b-12d3-a456-************",
    "visitorId": "987fcdeb-51d2-43a1-b123-456789abcdef", 
    "date": "2025-06-24",
    "slot": {
      "startTime": "2025-06-24T14:00:00.000Z",
      "endTime": "2025-06-24T15:00:00.000Z",
      "room_id": "456e7890-e89b-12d3-a456-************"
    }
  }'
```

### Validation Error Example
```bash
curl -X POST https://your-domain.supabase.co/functions/v1/reserveSlot \
  -H "Content-Type: application/json" \
  -d '{
    "inmateId": "invalid-uuid",
    "visitorId": "987fcdeb-51d2-43a1-b123-456789abcdef",
    "date": "2025-06-24", 
    "slot": {
      "startTime": "2025-06-24T14:00:00.000Z",
      "endTime": "2025-06-24T15:00:00.000Z",
      "room_id": "456e7890-e89b-12d3-a456-************"
    }
  }'
```

## Security Considerations

1. **Input Validation**: All inputs are validated using Zod schemas
2. **Authentication**: Should implement JWT token validation
3. **Authorization**: Should verify visitor permissions for specific inmate
4. **Rate Limiting**: Should implement to prevent abuse
5. **Logging**: All requests and errors are logged for monitoring
6. **Data Sanitization**: UUID validation prevents injection attacks

## Performance Considerations

- Time slot availability checking involves multiple database queries
- Consider caching frequently accessed inmate settings
- Database indexing on inmate_id, visitor_id, and time fields recommended
- Monitor response times for the availability checking logic
