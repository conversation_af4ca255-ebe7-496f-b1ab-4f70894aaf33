import { supabase } from "../../src/lib/SupabaseClient.ts";
import { NotFoundError } from "../../src/errors/notFoundError.ts";
import { getAvailability as getSectionAvailability } from "./section.ts";

export async function getAvailability(room_id: string, date: string) {

  const { data: room, error: roomError } = await supabase
    .from("rooms")
    .select("id, is_building_default, section_id")
    .eq("id", room_id)
    .single();
  if (roomError) {
    if (roomError.code === "PGRST116") {
      throw new NotFoundError("The Room Does Not Exist");
    } else {
      throw new Error(`Error Fetching Room: ${roomError.message}`);
    }
  }
  if (room.is_building_default) {
    // If the room is a building default, we need to fetch the section settings
    return getSectionAvailability(room.section_id, date);
  }
  const { data: sectionSettings, error: sectionError } = await supabase
    .from("room_availability_settings")
    .select("id")
    .eq("room_id", room_id)
    .eq(
      "day",
      new Date(date).toLocaleString("en-US", { weekday: "long" }).toLowerCase()
    );

  if (sectionError) {
    console.error("Error fetching section settings:", sectionError);
    throw new Error(
      `Failed to fetch section settings: ${sectionError.message}`
    );
  }

  let settings: any[] = [];
  if (sectionSettings && sectionSettings.length !== 0) {
    const settingIds = sectionSettings.map((setting) => setting.id);

    // Now get the availability_times using the setting IDs
    const { data: availabilityTimes, error: timesError } = await supabase
      .from("availability_times")
      .select("setting_id, type, available_from, available_to")
      .eq("type", "room")
      .in("setting_id", settingIds);

    if (timesError) {
      throw new Error(
        `Failed to fetch availability times: ${timesError.message}`
      );
    }

    settings = availabilityTimes.map((time) => ({
      setting_id: time.setting_id,
      startTime: new Date(`${date}T${time.available_from}`),
      endTime: new Date(`${date}T${time.available_to}`),
    }));
  }
  return settings;
}

export async function getSectionRooms(section_id: string) {
  const { data: rooms, error: roomError } = await supabase
    .from("rooms")
    .select("id, section_id")
    .eq("section_id", section_id);
  if (roomError) {
    if (roomError.code === "PGRST116") {
      throw new NotFoundError("The Section Has No Rooms");
    } else {
      throw new Error(`Error Fetching Section's Rooms: ${roomError.message}`);
    }
  }
  const roomIds = rooms.map((room) => room.id);
  return roomIds;
}
