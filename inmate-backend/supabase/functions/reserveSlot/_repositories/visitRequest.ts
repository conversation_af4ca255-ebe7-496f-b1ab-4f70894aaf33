import { supabase } from "../../src/lib/SupabaseClient.ts";

export const getVisits = async (
  filterationObject: { key: string; value: string[] },
  fromDateStr: string,
  toDateStr: string,
  allowedStatusIds: Number[],
  selections: string[]
) => {
  const { data } = await supabase
    .from("visit_requests")
    .select(
      selections.join(", ")
    )
    .eq("test", false)
    .gte("datetime", fromDateStr)
    .lte("datetime", toDateStr)
    .in("status_id", allowedStatusIds)
    .in(filterationObject.key, filterationObject.value);

    return data;
};
