import { supabase } from "../../src/lib/SupabaseClient.ts";
import { Slot } from "../dtos.ts";
import { calculateDurationInMinutes } from "../../src/utils/calculateDuration.ts";

export async function create(
  slot: Slot,
  visitorId: string,
  inmateId: string,
): Promise<any> {
  const { data, error } = await supabase.from("visit_requests").insert({
    visitor_id: visitorId,
    inmate_id: inmateId,
    datetime: slot.startTime,
    duration: calculateDurationInMinutes(slot.startTime, slot.endTime),
    status_id: Number(Deno.env.get("VISIT_REQUEST_STATUS_PENDING")) || 1,
    archived: false,
    room_id: slot.room_id,
  });

  if (error) {
    throw new Error(`Error fetching visit request: ${error.message}`);
  }

  return data;
}
