import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { getRooms } from "../_repositories/section.ts";
import { convertVisitTime } from "../../src/utils/convertVisit.ts";
import { generalConvertVisit } from "../../src/utils/generalConvertVisit.ts";
import { NotAvailableError } from "../../src/errors/notAvailalbeError.ts";
import { getRoomSettings } from "./getRoomSetting.ts";
import { matchSlots } from "./matchSlots.ts";
import { roomAntiMatchSlots } from "./roomAntiMatchSlots.ts";
import { generalAntiMatchSlots } from "./generalAntiMatchSlots.ts";
import { NotFoundError } from "../../src/errors/notFoundError.ts";
import { getVisits } from "../_repositories/visitRequest.ts";
import { uniqueSlots } from "./uniqueSlots.ts";
import { getOverridesAsRoomsSlots } from "./overrides.ts";
import { getSectionOveridesAsSlots } from "./sectionOverrides.ts";

const pipe =
  (...fns: Function[]) =>
  (initialValue: any) =>
    fns.reduce((value, fn) => fn(value), initialValue);

export const filterTimes = async (
  sectionId: string,
  roomId: string | null,
  settings: any,
  fromDateStr,
  toDateStr,
  date,
  availabilityTimesService,
  inmateId: string,
  visitorId: string,
  is_room_default: boolean
) => {
  let roomsIds: string[] = [];
  if (roomId === null || roomId === undefined || roomId === "") {
    roomsIds = (await getRooms(sectionId)).map((room: any) => room.id);
    if (roomsIds.length === 0) {
      throw new NotAvailableError("No active rooms found in the section.");
    }
  } else {
    roomsIds = [roomId];
  }
  const roomsettings = roomsIds.map((roomId) => {
    return getRoomSettings(roomId, date, availabilityTimesService);
  });
  const roomSettings = await Promise.all(roomsettings);
  if (!roomSettings || roomSettings.length === 0) {
    throw new NotFoundError(
      `No availability settings found for the given section ${sectionId}`
    );
  }

  const defaultSlots = roomSettings
    .filter((room) => room && room.settings)
    .flatMap((room) =>
      room.settings?.map((setting) => ({
        startTime: setting.startTime,
        endTime: setting.endTime,
        room_id: room.room_id,
      }))
    );

  if (defaultSlots.length === 0) {
    throw new NotFoundError(
      `No available time slots found for the rooms in section ${sectionId}`
    );
  }

  const allowedStatusIds = [
    Number(Deno.env.get("VISIT_REQUEST_STATUS_COMPLETED")) || 6,
    Number(Deno.env.get("VISIT_REQUEST_STATUS_APPROVED")) || 2,
    Number(Deno.env.get("VISIT_REQUEST_STATUS_PENDING")) || 1,
    Number(Deno.env.get("VISIT_REQUEST_STATUS_STARTING_SOON")) || 3,
    Number(Deno.env.get("VISIT_REQUEST_STATUS_AWAITING_ADMIN")) || 4,
  ];

  let promisList: Promise<any>[] = [];
  if (is_room_default && roomId !== null) {
    promisList = [
      getVisits(
        { key: "room_id", value: [roomId] },
        fromDateStr,
        toDateStr,
        allowedStatusIds,
        ["room_id", "datetime", "duration"]
      ),
      getVisits(
        { key: "inmate_id", value: [inmateId] },
        fromDateStr,
        toDateStr,
        allowedStatusIds,
        ["datetime", "duration"]
      ),
      getVisits(
        { key: "visitor_id", value: [visitorId] },
        fromDateStr,
        toDateStr,
        allowedStatusIds,
        ["datetime", "duration"]
      ),
      getOverridesAsRoomsSlots([roomId], date),
      Promise.resolve([]),
    ]
  } else {
    promisList = [
      getVisits(
        { key: "room_id", value: roomsIds },
        fromDateStr,
        toDateStr,
        allowedStatusIds,
        ["room_id", "datetime", "duration"]
      ),
      getVisits(
        { key: "inmate_id", value: [inmateId] },
        fromDateStr,
        toDateStr,
        allowedStatusIds,
        ["datetime", "duration"]
      ),
      getVisits(
        { key: "visitor_id", value: [visitorId] },
        fromDateStr,
        toDateStr,
        allowedStatusIds,
        ["datetime", "duration"]
      ),
      getOverridesAsRoomsSlots(roomsIds, date),
      getSectionOveridesAsSlots(sectionId, date),
    ]
  }

  const [roomSlotsData, inmateSlotsData, visitorSlotsData, roomOverrides, sectionOverrides] =
    await Promise.all(promisList);

  const roomSlotsDataFormatted = roomSlotsData.map((slot: any) =>
    convertVisitTime(slot)
  );
  const inmateSlotsDataFormatted = inmateSlotsData.map((slot: any) =>
    generalConvertVisit(slot)
  );
  const visitorSlotsDataFormatted = visitorSlotsData.map((slot: any) =>
    generalConvertVisit(slot)
  );

  const isToday = date === new Date().toISOString().split("T")[0];

  let filterPipeline;
  if (is_room_default && roomId !== null) {
  filterPipeline = pipe(
    (slots) => roomAntiMatchSlots(roomSlotsDataFormatted, slots),
    (slots) => roomAntiMatchSlots(roomOverrides, slots),
    (slots) => generalAntiMatchSlots(inmateSlotsDataFormatted, slots),
    (slots) => generalAntiMatchSlots(visitorSlotsDataFormatted, slots),
    (slots) => matchSlots(slots, settings),
    (slots) => {
      if (!isToday) return slots;

      const currentTime = new Date();
      const startOfDay = new Date(`${date}T00:00:00.000Z`);
      return generalAntiMatchSlots(
        [{ startTime: startOfDay, endTime: currentTime }],
        slots
      );
    },
    (slots) => uniqueSlots(slots) // Remove duplicates
  );} else {
    filterPipeline = pipe(
    (slots) => roomAntiMatchSlots(roomSlotsDataFormatted, slots),
    (slots) => roomAntiMatchSlots(roomOverrides, slots),
    (slots) => generalAntiMatchSlots(inmateSlotsDataFormatted, slots),
    (slots) => generalAntiMatchSlots(visitorSlotsDataFormatted, slots),
    (slots) => generalAntiMatchSlots(sectionOverrides, slots),
    (slots) => matchSlots(slots, settings),
    (slots) => {
      if (!isToday) return slots;

      const currentTime = new Date();
      const startOfDay = new Date(`${date}T00:00:00.000Z`);
      return generalAntiMatchSlots(
        [{ startTime: startOfDay, endTime: currentTime }],
        slots
      );
    },
    (slots) => uniqueSlots(slots) // Remove duplicates
  );
  }

  const availableSlots = filterPipeline(defaultSlots);
  if (availableSlots.length === 0) {
    throw new NotAvailableError("No Available slots found in the section.");
  }

  return availableSlots;
};
