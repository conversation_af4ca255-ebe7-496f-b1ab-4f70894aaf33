import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { Pipeline } from "../../src/pipeline/Pipeline.ts";
import { Slot } from "../../src/entities/Slot.ts";
import { RoomAvailabilityStage } from "../../src/pipeline/RoomAvailabilityStage.ts";

export const getRoomSettings = async (roomId: string, date: string, availabilityTimesService) => {

  const pipeline = new Pipeline<Slot[]>();
  pipeline.addStage(
      new RoomAvailabilityStage(roomId, date, availabilityTimesService)
    );

  const roomSettings = await pipeline.execute([]);
  return {settings: roomSettings,
    room_id: roomId,
  };
};
