import { NotAvailableError } from "../../src/errors/notAvailalbeError.ts";

export const matchSlots = (slotsA, slotsB) => {
  if (!slotsA || !slotsB) {
    throw new NotAvailableError("No Available slots found in the section.");
  }
  return slotsA.filter((slotA) => {
    for (let i = 0; i < slotsB.length; i++) {
      const slotB = slotsB[i];
      if (
        slotA.startTime >= slotB.startTime &&
        slotA.endTime <= slotB.endTime
      ) {
        return true;
      }
    }
    return false;
  });
};
