import { getOverrides } from "../_repositories/overrides.ts";

export async function getOverridesAsRoomsSlots(
  roomIds: string[],
  date: string
): Promise<any> {
  // Call the get function from the repository
  const overrides = await getOverrides(roomIds, date);
  const overridesAsRoomsSlots = overrides.map((override) =>
    override.override_dates.map((overrideDate) => {
      const slots = override.overrides_timeslots.map((slot) => ({
        startTime: new Date(`${overrideDate.date}T${slot.starttime}`),
        endTime: new Date(`${overrideDate.date}T${slot.endtime}`),
        room_id: override.room_id,
      }));
      return slots;
    })
  );
  return overridesAsRoomsSlots.flat(2);
}
