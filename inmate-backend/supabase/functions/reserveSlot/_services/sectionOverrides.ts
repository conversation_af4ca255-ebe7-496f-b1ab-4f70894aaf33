import { getOverrides } from "../_repositories/sectionOverrides.ts";

export async function getSectionOveridesAsSlots(
  section_id: string,
  date: string
): Promise<any> {
  // Call the get function from the repository
  const overrides = await getOverrides(section_id, date);
  const overridesAsSlots = overrides.map((override) =>
    override.section_override_dates.map((overrideDate) => {
      const slots = override.section_overrides_timeslots.map((slot) => ({
        startTime: new Date(`${overrideDate.date}T${slot.starttime}`),
        endTime: new Date(`${overrideDate.date}T${slot.endtime}`),
      }));
      return slots;
    })
  );
  return overridesAsSlots.flat(2);
}
