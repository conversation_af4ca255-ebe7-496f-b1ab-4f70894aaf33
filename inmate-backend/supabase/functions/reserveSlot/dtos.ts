import { z } from "npm:zod@latest";
import { getBookingWindowLimit } from "./_repositories/getBookingWindowLimit.ts";

const slotsSchema = z
  .object({
    startTime: z.string().datetime(),
    endTime: z.string().datetime(),
    room_id: z.string().uuid(),
  })
  .refine(
    (data) => {
      const start = new Date(data.startTime);
      const end = new Date(data.endTime);
      return start < end;
    },
    {
      message: "startTime must be before endTime",
    }
  ).refine(
    (data) => {
      const start = new Date(data.startTime);
      const end = new Date(data.endTime);
      const now = new Date();
      return start >= now && end >= now;
    },
    {
      message: "startTime and endTime must be in the future",
    }
  );

export type Slot = z.infer<typeof slotsSchema>;

export const createVisitRequestSchema = z.object({
  inmateId: z.string().uuid(),
  visitorId: z.string().uuid(),
  date: z.string().date().refine((date) => {
    const today = (new Date()).toISOString().split('T')[0];
    const todayDate = new Date(`${today}T00:00:00Z`);
    return new Date(date) >= todayDate;
  }, {
    message: "Date must be today or in the future",
  }).refine( async (date) => {
    const bookingWindowLimit = await getBookingWindowLimit();
    const today = (new Date()).toISOString().split('T')[0];
    const todayDate = new Date(`${today}T00:00:00Z`);
    const msDiff = new Date(date).getTime() - todayDate.getTime();
    const bookingWindowLimitMs = bookingWindowLimit * 24 * 60 * 60 * 1000; // Convert days to milliseconds
    if ( bookingWindowLimitMs <  msDiff) {
      return false;
    }
    return true;
  }, {
    message: `Date must be within the booking window limit of ${await getBookingWindowLimit()} days`, 
  })
    ,
  slot: slotsSchema,
});
