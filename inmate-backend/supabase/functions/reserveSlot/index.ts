import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { reserveVisit } from "./_services/visitRequests.ts";
import { ResponseHelper } from "../src/utils/ResponseHelper.ts";
import { withLogging } from "../src/utils/logger.ts"; // Change from default import to named import
import { getSettings } from "./_services/getSettings.ts";
import { filterTimes } from "./_services/filterTimes.ts";
import { AvailabilityTimesService } from "../src/services/AvailabilityTimesService.ts";
import { getMeetingDuration } from "./_repositories/meetingDuration.ts";
import { createVisitRequestSchema } from "./dtos.ts";

Deno.serve(
  withLogging(async (req) => {
    if (req.method === "OPTIONS") {
      return ResponseHelper.successResponse(null, "CORS preflight response");
    }

    // Check authentication for all non-OPTIONS requests
    // ToDO: Implement proper authentication check

    try {
      if (req.method !== "POST") {
        return ResponseHelper.errorResponse(
          "Invalid request method, only POST is allowed",
          405
        );
      }

      const body = await req.json();
      const parsedBody = await createVisitRequestSchema.safeParseAsync(body);
      if (!parsedBody.success) {
        return new Response(
          JSON.stringify({
            error: "Invalid request body",
            details: parsedBody.error.format(),
          }),
          { status: 400 }
        );
      }
      const { inmateId, visitorId, date, slot } = body;

      const [meetingDuration, betweenMeetingInterval] =
        await getMeetingDuration();
      const availabilityTimesService = new AvailabilityTimesService(
        meetingDuration,
        betweenMeetingInterval
      );
      const settings = await getSettings(inmateId, date);
      let filteredSettings;
      const fromDate = `${date}T00:00:00.000Z`;
      const toDate = `${date}T23:59:59.999Z`;
      if ("settings" in settings) {
        filteredSettings = await filterTimes(
          settings.section_id,
          settings.room_id,
          settings.settings,
          fromDate,
          toDate,
          date,
          availabilityTimesService,
          inmateId,
          visitorId,
          settings.is_room_default
        );
      } else {
        return ResponseHelper.errorResponse("Invalid settings response", 500);
      }
      if (!settings) {
        return ResponseHelper.errorResponse(
          "No settings found for the given inmate",
          404
        );
      }

      const startTime = new Date(slot.startTime);
      const endTime = new Date(slot.endTime);

      let isAvailable: boolean = false;
      for (const time of filteredSettings) {
        if (
          time.startTime.getTime() === startTime.getTime() &&
          time.endTime.getTime() === endTime.getTime() &&
          slot.room_id === time.room_id
        ) {
          isAvailable = true;
          break;
        }
      }
      if (!isAvailable) {
        return ResponseHelper.errorResponse(
          "The selected time slot is not available",
          450
        );
      }
      await reserveVisit(slot, visitorId, inmateId);
      return ResponseHelper.successResponse(
        {
          startTime: slot.startTime,
          endTime: slot.endTime,
          roomId: slot.room_id,
          inmateId,
          visitorId,
          reservationDate: date,
        },
        `Successfully reserved the slot ${slot.startTime} - ${slot.endTime} room ID: ${slot.room_id}`
      );
    } catch (error) {
      const status = error.statusCode || 500;
      const errorMessage = error.message || "Internal Server Error";
      if (status === 500) {
        console.error("Internal Server Error:", error);
      }
      return ResponseHelper.errorResponse(errorMessage, status);
    }
  }, "Reserve Slot")
);
