import 'https://esm.sh/@supabase/functions-js/src/edge-runtime.d.ts';
import { createClient } from 'npm:@supabase/supabase-js@2.39.3';
import { withLogging } from '../src/utils/logger.ts';  // Change from default import to named import

const supabase = createClient(Deno.env.get('SUPABASE_URL')!, Deno.env.get('SUPABASE_ANON_KEY')!);

const corsHeaders = {
  'Access-Control-Allow-Origin': '*', // Replace * with your frontend URL for tighter security
  'Access-Control-Allow-Methods': 'POST, GET, OPTIONS', // Specify allowed methods
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Max-Age': '86400', // Cache preflight response for 24 hours,


};
function formatDateTimeAndDuration(datetimeStr, durationMinutes) {
  // Parse the input datetime string
  const startTime = new Date(datetimeStr);

  // Calculate the end time by adding the duration
  const endTime = new Date(startTime.getTime() + durationMinutes * 60000);

  // Format the time range as "10:00 AM - 10:30 AM"
  const timeRange = `${formatTime(startTime)} - ${formatTime(endTime)}`;

  // Format the date as "24 November 2024"
  const dateStr = formatDate(startTime);

  return { timeRange, dateStr };
}

// Helper function to format time as "10:00 AM"
function formatTime(date) {
  let hours = date.getHours();
  const minutes = date.getMinutes();
  const ampm = hours >= 12 ? 'PM' : 'AM';
  hours = hours % 12;
  hours = hours ? hours : 12; // Handle midnight (0 hours)
  const formattedMinutes = minutes < 10 ? `0${minutes}` : minutes;
  return `${hours}:${formattedMinutes} ${ampm}`;
}

// Helper function to format date as "24 November 2024"
function formatDate(date) {
  const days = date.getDate();
  const month = date.toLocaleString('default', { month: 'long' });
  const year = date.getFullYear();
  return `${days} ${month} ${year}`;
}

Deno.serve(withLogging(async (req) => {
  try {
    const headers = {
      'Access-Control-Allow-Origin': '*', // Replace with your frontend URL
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
      'Access-Control-Max-Age': '86400',
      // ... other headers
    };
    if (req.method === 'OPTIONS') {
      return new Response(null, { headers });
    }

    const url = new URL(req.url);
    const requestId = url.searchParams.get('requestId');

    if (!requestId) {
      console.log("requestId is null");
      throw new Error('requestId is required');
    }

    let { data: requestData, error: requestError } = await supabase
        .from('visit_requests')
        .select('id , inmate_id(id,inmate_code,full_name), visitor_id(id,full_name,national_id) , datetime , duration ,visit_number , requested_inmate_name ,from , to ,reason')
        // .select('*')
        .eq('id', requestId)
        .single();
    console.log("requestData", requestData);
    if (requestData == null) {
      throw new Error('Request not found');
    }
    //

    console.log("Request Data:", requestData);
    const { data: visitor_inmate_relation, error: visitor_inmate_relationError } = await supabase
        .from('inmate_visitors')
        .select('*')
        .eq('inmate_id', requestData.inmate_id.id)
        .eq('visitor_id', requestData.visitor_id.id)
        .single();

    console.log("asfddafd",visitor_inmate_relation)

    if(visitor_inmate_relation == null || visitor_inmate_relation.length == 0){
      throw new Error('Visitor Inmate Relation not found');
    }

    const { data: documents, error: documentError } = await supabase
        .from('inmate_visitor_documents')
        .select('*')
        .eq('inmate_visitor_id', visitor_inmate_relation.id);

    // console.log("Documents:", document);
    console.log("Visitor Inmate Relation:", visitor_inmate_relation);
    // Step 6: Return the final response with inmate details

// Example usage

    const { timeRange, dateStr } = formatDateTimeAndDuration(requestData.datetime, requestData.duration);

    console.log("Time Range:", timeRange); // Output: "12:00 AM - 01:00 AM"
    console.log("Date:", dateStr); // Output: "5 February 2025"
    return new Response(
        JSON.stringify({
          data: {
            id: requestId,
            inmate: requestData.inmate_id.full_name,
            inmate_code: requestData.inmate_id.inmate_code,
            requested_inmate_name: visitor_inmate_relation.requested_inmate_name,
            visitor: requestData.visitor_id.full_name,
            visitor_national_id: requestData.visitor_id.national_id,
            date: dateStr,
            reason: requestData.reason,
            from : requestData.from,
            to : requestData.to,
            visit_number: requestData.visit_number,
            visitor_inmate_relation: visitor_inmate_relation.relation,
            other: visitor_inmate_relation.other
            ,

            documents: documents.map(doc => ({
              documentName: doc.document_name,
              documentUrl: doc.document_url,
              documentSize: doc.document_size
            })),

          },

        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200,
        }
    );
  } catch (error) {
    return new Response(
        JSON.stringify({ error: (error as Error).message }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 400,
        }
    );
  }
},"review_request"));
