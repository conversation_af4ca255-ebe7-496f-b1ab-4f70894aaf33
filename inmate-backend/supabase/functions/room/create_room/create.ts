import 'https://esm.sh/@supabase/functions-js/src/edge-runtime.d.ts';
import { createClient } from 'npm:@supabase/supabase-js@2.39.3';

// Initialize the Supabase client with environment variables
const supabase = createClient(Deno.env.get('SUPABASE_URL')!, Deno.env.get('SUPABASE_ANON_KEY')!);

// CORS headers to allow cross-origin requests
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

// Interface for the room update request
interface RoomUpdate {
  name: string;
  section_id: string;
  active: boolean;
  is_building_default: boolean;
  filteredDays: {
    day: string;
    isAvailable: boolean;
    times: {
      from: string;
      to: string;
    }[];
  }[];
}

// Function to validate the time format (HH:mm:ss)
const isValidTimeFormat = (time: string): boolean =>
  /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$/.test(time);

// Function to check if a room already exists with the same name and section_id
async function checkDuplicateRoom(section_id: string, name: string): Promise<boolean> {
  const { data, error } = await supabase
    .from('rooms')
    .select('id')
    .eq('section_id', section_id)
    .eq('name', name)
    .is('deleted_at', null);  // Check if the room is not deleted

  if (error) throw new Error(`Failed to check for duplicate room: ${error.message}`);
  return data && data.length > 0;  // Return true if room exists
}

// Function to validate the input data before processing
function validateInput(data: RoomUpdate) {
  const { section_id, name, filteredDays, is_building_default } = data;

  if (!section_id || typeof section_id !== 'string') {
    throw new Error('Invalid section_id: must be a valid string.');
  }
  if (!name || typeof name !== 'string') {
    throw new Error('Invalid room name: must be a valid string.');
  }
  if (!is_building_default && (!Array.isArray(filteredDays) || filteredDays.length === 0)) {
    throw new Error('Invalid filteredDays: must be a non-empty array for non-default rooms.');
  }

  if (!is_building_default) {
    for (const { day, times } of filteredDays) {
      if (!day || typeof day !== 'string') {
        throw new Error('Invalid day: must be a non-empty string.');
      }
      // if (!Array.isArray(times) || times.length === 0) {
      //   throw new Error(`Invalid times for day ${day}: must be a non-empty array.`);
      // }
      // for (const { from, to } of times) {
      //   if (!from || !to || !isValidTimeFormat(from) || !isValidTimeFormat(to)) {
      //     throw new Error(`Invalid time format in times for day ${day}: must be in HH:mm:ss format.`);
      //   }
      // }
    }
  }
}

// Function to create a room
async function createRoom(data: RoomUpdate) {
  const { name, section_id, active, is_building_default } = data;

  // Check for duplicate room name in the section
  const isDuplicate = await checkDuplicateRoom(section_id, name);
  if (isDuplicate) {
    throw new Error(`A room with the name "${name}" already exists in this section.`);
  }

  const { data: room, error } = await supabase
    .from('rooms')
    .insert([{
      name,
      section_id: section_id,
      active,
      is_building_default
    }])
    .select('id')
    .single();

  if (error) throw new Error(`Failed to create room: ${error.message}`);
  if (!room) throw new Error('Room creation failed: no data returned');

  return room.id;  // Return the ID of the created room
}

// Function to create availability settings for the room
async function createAvailabilitySettings(roomId: string, days: RoomUpdate['filteredDays']) {
  const { data: settings, error } = await supabase
    .from('room_availability_settings')
    .insert(
      days.map(({ day }) => ({
        room_id: roomId,
        day : day.toLowerCase(),
        is_available: true
      }))
    )
    .select('id, day');  // Get the created settings

  if (error) throw new Error(`Failed to create availability settings: ${error.message}`);
  if (!settings) throw new Error('No availability settings created');
  return settings;  // Return the created availability settings
}

// Function to create availability times for the settings
async function createAvailabilityTimes(settingId: string, from: string, to: string) {
  const { error } = await supabase
    .from('availability_times')
    .insert([{
      setting_id: settingId,
      type: 'room',
      available_from: from,
      available_to: to
    }]);
  if (error) throw new Error(`Failed to create availability time: ${error.message}`);
}
// Main function to handle the request and create room along with availability settings and times

export default async function create(req: Request) {
  try {
    const data: RoomUpdate = await req.json();  // Parse the incoming JSON data
    console.log('Received request:', data);
        // Validate input data
    validateInput(data);

    // Create the room (includes duplicate check)
    const roomId = await createRoom(data);
    console.log('Created room with ID:', roomId);

    // If not a building default room, create availability settings and times
    if (!data.is_building_default) {
      // Create availability settings
      const settings = await createAvailabilitySettings(roomId, data.filteredDays);
      console.log('Created availability settings:', settings);

      // Create availability times
      for (const {day, times} of data.filteredDays) {
        const setting = settings.find(s => s.day === day.toLowerCase());
        if (!setting) {
            console.log(setting)
          throw new Error(`No setting found for day: ${day}`);
        }

        for (const {from, to} of times) {
          await createAvailabilityTimes(setting.id, from, to);
        }
      }
      console.log('Created all availability times');
    }

    // Return success response
    return new Response(
        JSON.stringify({
          success: true,
          message: 'Room created successfully',
          roomId
        }),
        {
          headers: {...corsHeaders, 'Content-Type': 'application/json'},
          status: 200,
        }
    );
  } catch (error) {
    console.error('Error:', error);
    // Return error response if something went wrong
    return new Response(
        JSON.stringify({
          success: false,
          error: error.message
        }),
        {
          headers: {...corsHeaders, 'Content-Type': 'application/json'},
          status: 400,
        }
    );
  }

}