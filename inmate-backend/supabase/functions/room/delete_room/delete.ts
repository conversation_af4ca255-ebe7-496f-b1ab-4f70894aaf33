import 'https://esm.sh/@supabase/functions-js/src/edge-runtime.d.ts';
import { createClient } from 'npm:@supabase/supabase-js@2.39.3';
import hasUserPermission from "../../src/utils/hasPermission.ts";
const supabase = createClient(Deno.env.get('SUPABASE_URL')!, Deno.env.get('SUPABASE_ANON_KEY')!);

export const corsHeaders = {
  'Access-Control-Allow-Origin': '*', // Replace with your frontend URL
  'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Max-Age': '86400',
  'Content-Type': 'application/json',
};
export function filterCompletedRooms(data) {
  return data.filter(room => 
    room.visit_requests.some(visit => 
      !['completed', 'missed', 'denied', 'ended'].includes(visit.status_id.name)
    )
  );
}
export default async function deleteRoom(req: Request) {
  try {
    const { roomId , user_id } = await req.json();
    const headers = {
      'Access-Control-Allow-Origin': '*', // Replace with your frontend URL
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
      'Access-Control-Max-Age': '86400',
      // ... other headers
    };
    if (req.method === 'OPTIONS') {
      return new Response(null, { headers });
    }
    if (!roomId || typeof roomId !== 'string') {
      return new Response(
        JSON.stringify({ error: 'Invalid or missing roomId.' }),
        { status: 400, headers: corsHeaders }
      );
    }

    console.log('Received roomId:', roomId);

    // Check if the room exists
    const { data: room, error: roomError } = await supabase
      .from('rooms')
      .select('*')
      .eq('id', roomId)
      .single();

    if (roomError || !room) {
      return new Response(
        JSON.stringify({ error: `Room with ID ${roomId} does not exist.` }),
        { status: 404, headers: corsHeaders }
      );
    }

    console.log('Room found:', room);
    const { data: user_roles, error: userError } = await supabase
        .from('users')
        .select('* , role_id(name,role_permissions(permission_id(name)))')
        .eq('user_auth_id', user_id)
        .single();

    if (userError || !user_roles) {
      return new Response(
          JSON.stringify({ error: `User with ID ${user_id} does not exist.` }),
          { status: 404, headers: corsHeaders }
      );
    }
    console.log('user_roles:', user_roles.role_id.name);
    console.log('user:', user_roles);
    const role = user_roles.role_id.name;
    const permission = await hasUserPermission(user_roles, "Delete a Room");
    if ( !permission) {
      return new Response(
        JSON.stringify({ error: `doesn’t have the required permission`,
                        statusError:455,
                        permission: user_roles.role_id.name}),
                        { status: 404, headers: corsHeaders }
    );
    }
    {/**
     TODO: Check for linked visits
     **/}
    // // Check for linked rooms
    const { data: linkedRooms, error: RoomsError } = await supabase
        .from('rooms')
        .select('*, visit_requests(* , status_id(name))')

        .eq('id', roomId);


    if (RoomsError) {
      return new Response(
        JSON.stringify({ error: `Error checking linked visits. : ${RoomsError.message}` }),
        { status: 500, headers: corsHeaders }
      );
    }
    console.log('Linked rooms:', linkedRooms);
    const filteredVisits = filterCompletedRooms(linkedRooms);
    // console.log('filterd:', filteredVisits);
    if (filteredVisits && filteredVisits.length > 0) {
      console.log('Linked visits found:', filteredVisits);
      return new Response(
          JSON.stringify({
            statusError : 456,
            error: `room_linked_visits`,
            filteredVisits
          }),
        { status: 400, headers: corsHeaders }
      );
    }
    {/**
     TODO: add a check for linked Inmates

    **/}

    const { data: linkedInmates, error: InmatesError } = await supabase
        .from('inmates')
        .select('full_name , section_id(name) , room_id(name)')
        .is("deleted_at",null)

        .eq('room_id', roomId);

    if (InmatesError) {
      return new Response(
          JSON.stringify({ error: `Error checking linked inmates.  :${InmatesError.message}` }),
          { status: 500, headers: corsHeaders }
      );
    }
    //
    if (linkedInmates && linkedInmates.length > 0) {
      console.log('Linked Inmates found:', linkedInmates);
      return new Response(
          JSON.stringify({
            statusError : 453,
            error: `room_linked_inmates`,
            linkedInmates
          }),
          { status: 400, headers: corsHeaders }
      );
    }
    //check if the room is already deleted
    const deleted_at = room.deleted_at;
    console.log('deleted_at:', deleted_at);
    if (deleted_at) {
      return new Response(
        JSON.stringify({ error: `Room with ID ${roomId} is already deleted.` }),
        { status: 404, headers: corsHeaders }
      );
    }
    // set `deleted_at` for the room
    const { error: updateError } = await supabase
      .from('rooms')
      .update({
        deleted_at: new Date().toISOString(),
        active: false,
      })
      .eq('id', roomId);

    if (updateError) {
      return new Response(
        JSON.stringify({ error: 'Error updating the deleted_at for the room.' }),
        { status: 500, headers: corsHeaders }
      );
    }

    console.log('Room marked as deleted and timestamp set successfully.');

    console.log('Room marked as deleted successfully.');

    return new Response(
      JSON.stringify({ message: `Room with ID ${roomId} marked as deleted successfully.` }),
      { status: 200, headers: corsHeaders }
    );
  } catch (error) {
    console.error('Error:', error.message);
    return new Response(
      JSON.stringify({ error: error.message }),
      { status: 500, headers: corsHeaders }
    );
  }
}