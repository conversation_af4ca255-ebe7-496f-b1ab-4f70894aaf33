import 'https://esm.sh/@supabase/functions-js/src/edge-runtime.d.ts';
import { createClient } from 'npm:@supabase/supabase-js@2.39.3';
import { filterCompletedRooms } from "../delete_room/delete.ts";
import hasUserPermission from "../../src/utils/hasPermission.ts";
const supabase = createClient(Deno.env.get('SUPABASE_URL')!, Deno.env.get('SUPABASE_ANON_KEY')!);

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Max-Age': '86400',
  'content-type': 'application/json',
};

const isValidTimeFormat = (time: string): boolean =>
  /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$/.test(time);

// New time comparison helpers
const timeToMinutes = (time: string): number => {
  const [hours, minutes] = time.split(':').map(Number);
  return hours * 60 + minutes;
};

const timeRangesOverlap = (range1: { from: string, to: string }, range2: { from: string, to: string }): boolean => {
  const [start1, end1] = [range1.from, range1.to].map(timeToMinutes);
  const [start2, end2] = [range2.from, range2.to].map(timeToMinutes);
  return start1 < end2 && end1 > start2;
};

const validateEditInput = (roomId: any, roomCode: any, roomAvailability: any, isBuildingDefault: any, filteredDays: any[]) => {
  if (!roomId || typeof roomId !== 'string') {
    throw new Error('Invalid roomId: must be a valid string.');
  }
  if (!roomCode || typeof roomCode !== 'string') {
    throw new Error('Invalid roomCode: must be a non-empty string.');
  }
  if (typeof roomAvailability !== 'boolean') {
    throw new Error('Invalid roomAvailability: must be a boolean.');
  }
  if (typeof isBuildingDefault !== 'boolean') {
    throw new Error('Invalid isBuildingDefault: must be a boolean.');
  }
};

async function checkTimeConflicts(roomId: string, filteredDays: any[]) {
  const availableDays = filteredDays
    .filter(day => day.is_available)
    .map(day => day.day.toLowerCase());

  if (availableDays.length === 0) return null;

  const { data: rooms, error } = await supabase
    .from('rooms')
    .select('*, visit_requests(*, status_id(*))')
    .eq('id', roomId);

  if (error) throw new Error(`Error fetching visits: ${error.message}`);
  console.log(rooms,"rooms visits");
  const activeVisits = rooms
  .flatMap(room => 
    room.visit_requests
      .filter(request => 
        request.status_id && 
        !["missed", "completed", "ended", "denied", "admin_cancelled", "visitor_cancelled"]
          .includes(request.status_id.name)
      )
      .map(request => ({
        ...request,
        room_id: room.id
      }))
  );

  const conflicts = [];
  console.log(activeVisits,"activeVisits");

  for (const visit of activeVisits) {
    const visitDate = new Date(visit.datetime);
    const visitDay = visitDate.toLocaleDateString('en-US', { 
      weekday: 'long', 
      timeZone: 'UTC' 
    }).toLowerCase();

    const visitStart = visitDate.toISOString().split('T')[1].slice(0, 8);
    const visitEnd = new Date(visitDate.getTime() + visit.duration * 60000)
      .toISOString().split('T')[1].slice(0, 8);

    const dayConfig = filteredDays.find(d => 
      d.day.toLowerCase() === visitDay && d.is_available
    );
    console.log("visit:",visitDate);
    if (dayConfig) {
      for (const timeRange of dayConfig.times) {
        if (timeRangesOverlap(
          { from: visitStart, to: visitEnd },
          { from: timeRange.from, to: timeRange.to }
        )) {
          conflicts.push({
            visit_id: visit.id,
            room_id: roomId,
            visit_datetime: visit.datetime,
            visit_duration: visit.duration,
            conflicting_day: dayConfig.day,
            conflicting_time: timeRange,
            visit_time: { from: visitStart, to: visitEnd }
          });
          break;
        }
      }
    }
  }

  return conflicts.length > 0 ? conflicts : null;
}


async function checkVisitsOnUnavailableDays(roomId: string, filteredDays: any[]) {
  const unavailableDays = filteredDays
    .filter(day => day.is_available === false)
    .map(day => day.day.toLowerCase());

  if (unavailableDays.length === 0) return null;

  const { data: rooms, error } = await supabase
    .from('rooms')
    .select('*, visit_requests(*, status_id(*))')
    .eq('id', roomId);

  if (error) throw new Error(`Error fetching visits: ${error.message}`);

  const problematicVisits = rooms
    .flatMap(room => 
      room.visit_requests
        .filter(request => {
          const visitDay = new Date(request.datetime).toLocaleDateString('en-US', { 
            weekday: 'long', 
            timeZone: 'UTC' 
          }).toLowerCase();
          return (
            unavailableDays.includes(visitDay) &&
            request.status_id &&
            !["missed", "completed", "ended", "denied", "admin_cancelled", "visitor_cancelled"]
              .includes(request.status_id.name)
          );
        })
        .map(request => ({
          ...request,
          room_id: room.id
        }))
    );

  return problematicVisits.length > 0 ? problematicVisits : null;
}

export default async function edit(req: Request) {
  try {
    const { roomId, roomCode, roomAvailability, isBuildingDefault, filteredDays, user_id } = await req.json();
    validateEditInput(roomId, roomCode, roomAvailability, isBuildingDefault, filteredDays);

    // Permission check and room validation
    const { data: user_roles } = await supabase
      .from('users')
      .select('*, role_id(name,role_permissions(permission_id(name)))')
      .eq('user_auth_id', user_id)
      .single();

    if (!await hasUserPermission(user_roles, "Edit a Room")) {
      return new Response(
        JSON.stringify({ error: `User with ID ${user_id} does not exist.` }),
        { status: 404, headers: corsHeaders }
      );
    }

    console.log('user_roles:', user_roles.role_id.name);
    console.log('user:', user_roles);

    const permission = await hasUserPermission(user_roles, "Edit a Room");
    console.log('permission:', permission);

    if (!permission) {
      return new Response(
        JSON.stringify({
          error: `doesn’t have the required permission`,
          statusError: 455,
          permission: user_roles.role_id.name
        }),
        { status: 404, headers: corsHeaders }
      );
    }
  // Check if room code already exists for a different room
  const { data: existingRoom, error: existingRoomError } = await supabase
    .from('rooms')
    .select('id')
    .eq('name', roomCode)
    .neq('id', roomId) // Exclude the current room
    .single();

  if (existingRoomError && existingRoomError.code !== 'PGRST116') {
    throw new Error(`Error checking room code: ${existingRoomError.message}`);
  }

  if (existingRoom) {
    return new Response(
      JSON.stringify({
        error: 'Room code already exists',
        statusError: 459,
      }),
      { status: 409, headers: corsHeaders }
    );
  }

    // Existing update logic
    if (roomAvailability === false) {
      const { data: linkedRooms } = await supabase
        .from('rooms')
        .select('*, visit_requests(*, status_id(name))')
        .eq('id', roomId);
      console.log(linkedRooms ,"asfasfadf");
      const filteredVisits = filterCompletedRooms(linkedRooms || []);
      if (filteredVisits.length > 0) {
        return new Response(
          JSON.stringify({
            statusError: 456,
            error: `room_linked_visits`,
            filteredVisits
          }),
          { status: 409, headers: corsHeaders }
        );
      }
    }

    // Check for time conflicts
    if (!isBuildingDefault) {
      // const problematicVisits = await checkVisitsOnUnavailableDays(roomId, filteredDays);
      // console.log("problematicVisits:", problematicVisits);

      // if (problematicVisits) {
      //   return new Response(
      //     JSON.stringify({
      //       statusError: 458, // New error code for visits on unavailable days
      //       error: `visits_on_unavailable_days`,
      //       problematicVisits
      //     }),
      //     { status: 409, headers: corsHeaders }
      //   );
      // }

      // const timeConflicts = await checkTimeConflicts(roomId, filteredDays);
      // console.log("timeConflicts::", timeConflicts);

      // if (timeConflicts) {
      //   return new Response(
      //     JSON.stringify({
      //       statusError: 457,
      //       error: `time_conflicts_with_existing_visits`,
      //       timeConflicts
      //     }),
      //     { status: 409, headers: corsHeaders }
      //   );
      // }
    }

    // Database updates
    const { error: updateError } = await supabase
      .from('rooms')
      .update({
        name: roomCode,
        active: roomAvailability,
        is_building_default: isBuildingDefault
      })
      .eq('id', roomId);

    if (updateError) throw new Error(`Update failed: ${updateError.message}`);

    if (!isBuildingDefault) {
      // Step 1: Fetch all relevant setting IDs for the room
      const { data: settingsData, error: fetchSettingsError } = await supabase
        .from('room_availability_settings')
        .select('id')
        .eq('room_id', roomId);

      if (fetchSettingsError) {
        throw new Error(`Error fetching availability settings: ${fetchSettingsError.message}`);
      }

      const settingIds = settingsData.map(({ id }: { id: string }) => id);

      // Step 2: Delete all availability times for the fetched setting IDs
      const { error: deleteTimesError } = await supabase
        .from('availability_times')
        .delete()
        .in('setting_id', settingIds);

      if (deleteTimesError) {
        throw new Error(`Error deleting availability times: ${deleteTimesError.message}`);
      }

      // Step 3: Delete old room_availability_settings
      const { error: deleteSettingsError } = await supabase
        .from('room_availability_settings')
        .delete()
        .eq('room_id', roomId);

      if (deleteSettingsError) {
        throw new Error(`Error deleting old room_availability_settings: ${deleteSettingsError.message}`);
      }

      // Step 4: Insert updated room_availability_settings
      const availabilitySettingsToInsert = filteredDays.map(({ day }) => ({
        room_id: roomId,
        day: day.toLowerCase(),
        is_available: true
      }));

      const { data: roomAvailabilities, error: availabilityError } = await supabase
        .from('room_availability_settings')
        .insert(availabilitySettingsToInsert)
        .select('id, day');

      if (availabilityError || !roomAvailabilities) {
        throw new Error(`Error inserting into room_availability_settings: ${availabilityError?.message || 'No data returned'}`);
      }

      const dayToSettingId = roomAvailabilities.reduce((acc, { id, day }) => {
        acc[day] = id;
        return acc;
      }, {} as Record<string, string>);

      // Step 5: Insert updated availability times
      const availabilityTimesToInsert = filteredDays.flatMap(({ day, times }) =>
        times.map(({ from, to }) => ({
          setting_id: dayToSettingId[day.toLowerCase()],
          type: 'room',
          available_from: from,
          available_to: to,
        }))
      );

      const { error: timesError } = await supabase
        .from('availability_times')
        .insert(availabilityTimesToInsert);

      if (timesError) {
        throw new Error(`Error inserting into availability_times: ${timesError.message}`);
      }
    } else {
      // Delete availability settings and availability times for building default rooms
      const { data: settingsData, error: fetchSettingsError } = await supabase
        .from('room_availability_settings')
        .select('id')
        .eq('room_id', roomId);

      if (fetchSettingsError) {
        throw new Error(`Error fetching availability settings: ${fetchSettingsError.message}`);
      }

      const settingIds = settingsData.map(({ id }: { id: string }) => id);

      const { error: deleteTimesError } = await supabase
        .from('availability_times')
        .delete()
        .in('setting_id', settingIds);

      if (deleteTimesError) {
        throw new Error(`Error deleting availability times: ${deleteTimesError.message}`);
      }

      const { error: deleteSettingsError } = await supabase
        .from('room_availability_settings')
        .delete()
        .eq('room_id', roomId);

      if (deleteSettingsError) {
        throw new Error(`Error deleting old room_availability_settings: ${deleteSettingsError.message}`);
      }
    }

    return new Response(
      JSON.stringify({ message: 'Room updated successfully' }),
      { headers: corsHeaders, status: 200 }
    );

  } catch (error) {
    return new Response(
      JSON.stringify({
        error: error.message,
        details: error.stack
      }),
      { headers: corsHeaders, status: 400 }
    );
  }
}