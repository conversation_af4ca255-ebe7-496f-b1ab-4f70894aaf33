// Setup type definitions for built-in Supabase Runtime APIs
import 'https://esm.sh/@supabase/functions-js/src/edge-runtime.d.ts';
import { createClient } from 'npm:@supabase/supabase-js@2.39.3';

// Initialize Supabase client
const supabase = createClient(Deno.env.get('SUPABASE_URL')!, Deno.env.get('SUPABASE_ANON_KEY')!);

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*', // Replace * with your frontend URL for tighter security
  'Access-Control-Allow-Methods': 'POST, GET, OPTIONS', // Specify allowed methods
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Max-Age': '86400', // Cache preflight response for 24 hours
};

export default async function searchRooms(queryparams: Record<string, string | null>) {

  try {
    const section_id = queryparams.section_id;
    const keyword = queryparams.keyword || '';

    console.log("Received query parameters:", { section_id, keyword });

    // Validate required fields
    if (!section_id) {
      return withCors(
        new Response(
          JSON.stringify({ error: "Section ID is required" }),
          { status: 400 }
        )
      );
    }

    // Fetch rooms linked to the given section
    const { data: rooms, error } = await supabase
      .from("rooms")
      .select("*")
      .is("deleted_at", null)  // Correctly checks for NULL
      .eq("section_id", section_id);

    if (error) {
      console.error("Error fetching rooms:", error);
      return withCors(
        new Response(
          JSON.stringify({ error: `Error fetching rooms : ${error.message}` }),
          { status: 500 }
        )
      );
    }

    if (!rooms || rooms.length === 0) {
      console.log("No rooms found for the given section ID");
      return withCors(
        new Response(
          JSON.stringify({ message: "No rooms found" ,
          data:[]},),
          { status: 200 ,
          headers: { "Content-Type": "application/json" } // Explicitly setting Content-Type
          }
        )
      );
    }

    console.log("Fetched rooms:", rooms);

    // If keyword is empty, return all rooms; otherwise, filter by keyword
    const filteredRooms = keyword
      ? rooms.filter((room) =>
        Object.values(room).some((value) =>
          typeof value === "string" &&
          value.toLowerCase().includes(keyword.toLowerCase())
        )
      )
      : rooms;

    console.log("Filtered rooms by keyword:", filteredRooms);

    return withCors(
      new Response(
        JSON.stringify({ rooms: filteredRooms }),
        { status: 200, headers: { "Content-Type": "application/json" } }
      )
    );
  } catch (error) {
    console.error("Unexpected error:", error);
    return withCors(
      new Response(
        JSON.stringify({ error: "Internal Server Error" }),
        { status: 500 }
      )
    );
  }
}

// Helper to add CORS headers
function withCors(response) {
  response.headers.set("Access-Control-Allow-Origin", "*");
  response.headers.set("Access-Control-Allow-Methods", "GET, OPTIONS");
  response.headers.set("Access-Control-Allow-Headers", "Content-Type:application/json");
  return response;
}
