import { z } from "npm:zod@latest";
import { ValidationError } from "../../src/errors/validationError.ts";

function slotsOverlap(slot1: Slots, slot2: Slots): boolean {
  const start1 = new Date(`1970-01-01T${slot1.startTime}`);
  const end1 = new Date(`1970-01-01T${slot1.endTime}`);
  const start2 = new Date(`1970-01-01T${slot2.startTime}`);
  const end2 = new Date(`1970-01-01T${slot2.endTime}`);

  return start1 < end2 && start2 < end1 && start1 !== start2 && end1 !== end2;
}

function hasOverlappingSlots(slots: Slots[]): boolean {
  for (let i = 0; i < slots.length; i++) {
    for (let j = i + 1; j < slots.length; j++) {
      if (slotsOverlap(slots[i], slots[j])) {
        return true;
      }
    }
  }
  return false;
}

const dateSchema = z
  .string()
  .date()
  .refine(
    (date) => {
      return (
        new Date(`${date}T00:00:00.000`) >=
        new Date(`${new Date().toISOString().split("T")[0]}T00:00:00.000`)
      );
    },
    {
      message: "Date must be today or in the future",
    }
  );

const slotsSchema = z
  .object({
    startTime: z.string().time(),
    endTime: z.string().time(),
  })
  .refine(
    (data) => {
      const start = new Date(`1970-01-01T${data.startTime}`);
      const end = new Date(`1970-01-01T${data.endTime}`);
      return start < end;
    },
    {
      message: "startTime must be before endTime",
    }
  );

export type Slots = z.infer<typeof slotsSchema>;
export type DateType = z.infer<typeof dateSchema>;

const slotsArraySchema = z
  .array(slotsSchema)
  .refine(
    (slots) => {
      return slots.length > 0;
    },
    {
      message: "At least one slot is required",
    }
  )
  .refine(
    (slots) => {
      return !hasOverlappingSlots(slots);
    },
    {
      message: "Slots cannot overlap with each other",
    }
  );

const dateArraySchema = z
  .array(dateSchema)
  .refine(
    (dates) => {
      return dates.length > 0;
    },
    {
      message: "At least one date is required",
    }
  )
  .refine(
    (dates) => {
      const datesSet = new Set(dates);
      return datesSet.size === dates.length;
    },
    {
      message: "Dates must be unique",
    }
  );

export const createOverrideSchema = z
  .object({
    section_id: z.string().uuid(),
    dates: dateArraySchema,
    slots: slotsArraySchema,
  })
  .refine((data) => {
    for (const date of data.dates) {
      for (const slot of data.slots) {
        const start = new Date(`${date}T${slot.startTime}`);
        const end = new Date(`${date}T${slot.endTime}`);
        if (start < new Date() && end < new Date()) {

          throw new ValidationError(
            `Slot ${slot.startTime} - ${slot.endTime} both start and end times on ${date} are in the past`
          );
        }
      }
    }
    return true; // or your actual validation result
  });

export const patchOverrideSchema = z.object({
  id: z.string().uuid(),
  dates: dateArraySchema.optional(),
  slots: slotsArraySchema.optional(),
});

export const uuidSchema = z.string().uuid();
