import { supabase } from "../../src/lib/SupabaseClient.ts";
import { NotFoundError } from "../../src/errors/notFoundError.ts";
import { Slots, DateType } from "../_dtos/dtos.ts";
import { ValidationError } from "../../src/errors/validationError.ts";
import { CustomError } from "../../src/errors/customError.ts";

export const deleteOverride = async (id: string) => {
  const { data: data, error: checkExist } = await supabase
    .from("section_overrides")
    .select("id")
    .eq("id", id)
    .single();

  if (checkExist) {
    if (checkExist.code === "PGRST116") {
      throw new NotFoundError("Override not found");
    } else {
      throw new Error(`Error updating override date: ${checkExist.message}`);
    }
  }
  
  const { data: dataDelete, error: errorDelete } = await supabase
    .from("section_overrides")
    .delete()
    .eq("id", id);
  if (errorDelete) {
    throw new Error(`Error deleting override: ${errorDelete.message}`);
  }
};

export const getOverride = async ({
  id,
  section_id,
}: {
  id: string | null;
  section_id: string | null;
}) => {
  // If querying by id, get single override
  if (id) {
    const { data, error } = await supabase
      .from("section_overrides")
      .select("*, section_override_dates(*), section_overrides_timeslots(*)")
      .eq("id", id)
      .single();

    if (error) {
      if (error.code === "PGRST116") {
        throw new NotFoundError("Override not found");
      } else {
        throw new Error(`Error getting override: ${error.message}`);
      }
    }
    if (!data) {
      throw new NotFoundError("Override not found");
    }
    return data;
  }
  
  // If querying by section_id, get list of overrides
  if (section_id) {
    const { data, error } = await supabase
      .from("section_overrides")
      .select("*, section_override_dates(*), section_overrides_timeslots(*)")
      .eq("section_id", section_id);

    if (error) {
      throw new Error(`Error getting overrides: ${error.message}`);
    }
    
    return data || [];
  }

  throw new ValidationError("Either id or section_id must be provided");
};

export async function createSlots(override_id: string, slots: Slots[]) {
  // Call the create function from the repository
  const { data: slotsData, error: slotsError } = await supabase
    .from("section_overrides_timeslots")
    .insert(
      slots.map((slot) => ({
        override_id,
        starttime: slot.startTime,
        endtime: slot.endTime,
      }))
    )
    .select("id, starttime, endtime");

  if (slotsError) {
    throw new Error(`Error creating override slot: ${slotsError.message}`);
  }

  return slotsData;
}

export async function create(section_id: string, dates: DateType[], slots: Slots[]) {
  // Call the create function from the repository
  const { data: overrideData, error: overrideError } = await supabase
    .from("section_overrides")
    .insert({
      section_id
    })
    .select("id, section_id")
    .single();

  if (overrideError) {
    if (overrideError.code === "23505") {
      throw new CustomError(
        "An override for this room and date already exists.", 454
      );
    }
    throw new Error(`Error creating override: ${overrideError.messgae}`);
  }

  if (!overrideData) {
    throw new Error(`Error creating override: ${overrideData.message}`);
  }

  const { data: checkdata, error: checkError } = await supabase
    .from("section_overrides")
    .select("id, section_id")
    .eq("id", overrideData.id)
    .single();
    // Rollback the override if dates fail
  const { data: datesData, error: datesError } = await supabase
    .from("section_override_dates")
    .insert(
      dates.map((date) => ({
        override_id: overrideData.id,
        date: date,
      }))
    ).select("id, date");

  if (datesError) {
    // Rollback the override if timeslots fail
    await supabase.from("overrides").delete().eq("id", overrideData.id);
    throw new Error(`Error creating dates: ${datesError.message}`);
  }

  const { data: slotsData, error: slotsError } = await supabase
    .from("section_overrides_timeslots")
    .insert(
      slots.map((slot) => ({
        override_id: overrideData.id,
        starttime: slot.startTime,
        endtime: slot.endTime,
      }))
    )
    .select("id, starttime, endtime");

  if (slotsError) {
    // Rollback the override if timeslots fail
    await supabase.from("section_overrides").delete().eq("id", overrideData.id);
    throw new Error(`Error creating timeslots: ${slotsError.message}`);
  }

  return {
    ...overrideData,
    section_override_dates: dates,
    section_overrides_timeslots: slots,
  };
}

export async function deleteSlots(override_id: string) {
  // Call the create function from the repository
  const { data, error } = await supabase
    .from("section_overrides_timeslots")
    .delete()
    .eq("override_id", override_id);

  if (error) {
    throw new Error(`Error deleting override slot: ${error.message}`);
  }

  return data;
}

export async function updateOverrideDate(id: string, date: string) {
  const { data, error } = await supabase
    .from("section_overrides")
    .update({ date })
    .eq("id", id)
    .select("id, section_id, date")
    .single();

  if (error) {
    if (error.code === "23505") {
      throw new CustomError(
        "An override for this room and date already exists.", 454
      );
    } else if (error.code === "PGRST116") {
      throw new NotFoundError("Override not found");
    } else {
      throw new Error(`Error updating override date: ${error.message}`);
    }
  }

  return data;
}

export async function deleteOverrideDate(id: string) {
  const { data, error } = await supabase
    .from("section_override_dates")
    .delete()
    .eq("override_id", id);

  if (error) {
    throw new Error(`Error deleting override date: ${error.message}`);
  }

  return data;
}

export async function createDates(override_id: string, dates: DateType[]) {
  const { data, error } = await supabase
    .from("section_override_dates")
    .insert(
      dates.map((date) => ({
        override_id,
        date,
      }))
    )
    .select("id, date");

  if (error) {
    throw new Error(`Error creating override dates: ${error.message}`);
  }

  return data;
}