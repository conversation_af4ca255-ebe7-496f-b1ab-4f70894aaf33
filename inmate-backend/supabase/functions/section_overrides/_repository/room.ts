import { supabase } from "../../src/lib/SupabaseClient.ts";
import { CustomError } from "../../src/errors/customError.ts";

export async function getAvailability(room_id: string, date: string) {
  const { data: sectionSettings, error: sectionError } = await supabase
    .from("room_availability_settings")
    .select("id")
    .eq("room_id", room_id)
    .eq(
      "day",
      new Date(date).toLocaleString("en-US", { weekday: "long" }).toLowerCase()
    );

  if (sectionError) {
    console.error("Error fetching section settings:", sectionError);
    throw new Error(
      `Failed to fetch section settings: ${sectionError.message}`
    );
  }

  let settings: any[] = [];
  if (sectionSettings && sectionSettings.length !== 0) {
    const settingIds = sectionSettings.map((setting) => setting.id);

    // Now get the availability_times using the setting IDs
    const { data: availabilityTimes, error: timesError } = await supabase
      .from("availability_times")
      .select("setting_id, type, available_from, available_to")
      .eq("type", "room")
      .in("setting_id", settingIds);

    if (timesError) {
      throw new Error(
        `Failed to fetch availability times: ${timesError.message}`
      );
    }

    settings = availabilityTimes.map((time) => ({
      setting_id: time.setting_id,
      startTime: new Date(`${date}T${time.available_from}`),
      endTime: new Date(`${date}T${time.available_to}`),
    }));
  }
  return settings;
}

export async function getSectionRooms(section_id: string) {
  const { data: rooms, error: roomError } = await supabase
    .from("rooms")
    .select("id, section_id")
    .eq("section_id", section_id);
  if (roomError) {
    if (roomError.code === "PGRST116") {
      throw new CustomError("The Section Has No Rooms", 451, {
        section_id,
      });
    } else {
      throw new Error("Server Error");
    }
  }
  const roomIds = rooms.map((room) => room.id);
  return roomIds;
}
