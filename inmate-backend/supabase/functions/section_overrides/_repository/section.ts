import { supabase } from "../../src/lib/SupabaseClient.ts";
import { NotFoundError } from "../../src/errors/notFoundError.ts";
import { CustomError } from "../../src/errors/customError.ts";

export async function getAvailability(section_id: string, date: string) {

  const { data: section, error: sectionError } = await supabase
    .from("sections")
    .select("id, is_available")
    .eq("id", section_id)
    .single();
  if (sectionError) {
    if (sectionError.code === "PGRST116") {
      throw new NotFoundError("The Section Does Not Exist");
    } else {
      throw new Error(`Error Fetching Section: ${sectionError.message}`);
    }
  }
  if (!section.is_available) {
    throw new CustomError("The Section is not available", 450);
  }
  // const { data: sectionSettings, error: sectionSettingsError } = await supabase
  //   .from("section_availability_settings")
  //   .select("id")
  //   .eq("section_id", section_id)
  //   .eq("day", new Date(date)
  //           .toLocaleString('en-US', {weekday: 'long'})
  //           .toLowerCase());

  // if (sectionSettingsError) {
  //   throw new Error(
  //     "Server Error"
  //   );
  // }

  // let settings: any[] = [];
  // if (sectionSettings && sectionSettings.length !== 0) {
  //   const settingIds = sectionSettings.map((setting) => setting.id);

  //   // Now get the availability_times using the setting IDs
  //   const { data: availabilityTimes, error: timesError } = await supabase
  //     .from("availability_times")
  //     .select("setting_id, type, available_from, available_to")
  //     .eq("type", "section")
  //     .in("setting_id", settingIds);

  //   if (timesError) {
  //     throw new Error(
  //       `Failed to fetch availability times: ${timesError.message}`
  //     );
  //   }

  //   settings = availabilityTimes.map((time) => ({
  //     setting_id: time.setting_id,
  //     startTime: new Date(`${date}T${time.available_from}`),
  //     endTime: new Date(`${date}T${time.available_to}`),
  //   }));
  // }
  // if (settings.length === 0) {
  //   throw new CustomError("No availability settings found for this section", 451);
  // }
  // return settings;
}
