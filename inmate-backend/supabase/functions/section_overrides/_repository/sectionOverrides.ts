import { supabase } from "../../src/lib/SupabaseClient.ts";
import { CustomError } from "../../src/errors/customError.ts";

export async function validateOverrideDate(
  section_id: string,
  date: string
): Promise<void> {
  // Get all overrides for the section without date filtering
  const { data, error } = await supabase
    .from("section_overrides")
    .select(
      `
      id, 
      section_override_dates(id, date),
      section_overrides_timeslots(id, starttime, endtime)
    `
    )
    .eq("section_id", section_id);

  if (error) {
    throw new Error(`Error fetching overrides: ${error.message}`);
  }
  const filteredOverrides = data
    .map((override) => {
      // Filter to only include overrides that have the specific date
      override.section_override_dates = override.section_override_dates.filter(
        (overrideDate) => overrideDate.date === date
      );
      return override;
    })
    .filter((override) => override.section_override_dates.length > 0);
    if (filteredOverrides.length > 0) {
      throw new CustomError(
        `There are section overrides for section ${section_id} on date ${date}`,
        454,
        {
          section_id,
          date,
        }
      );
    }
}
