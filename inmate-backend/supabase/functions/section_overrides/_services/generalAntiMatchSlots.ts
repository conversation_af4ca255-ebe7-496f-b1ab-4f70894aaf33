import { CustomError } from "../../src/errors/customError.ts";

export const generalAntiMatchSlots = (slotsA, slotsB, date) => {
  // Filter out slots that overlap with any reservation
  return slotsB.filter((slot) => {
    // Check each reservation for overlap with this slot
    const slotStart = new Date(
        `${date}T${slot.startTime}Z`
      );
    const slotEnd = new Date(`${date}T${slot.endTime}Z`);
    for (const reservation of slotsA) {

      // Simple and correct overlap check
      if (reservation.startTime < slotEnd && reservation.endTime > slotStart) {
        throw new CustomError(
          `Slot ${slot.startTime} - ${slot.endTime} overlaps with existing reservation ${reservation.startTime} - ${reservation.endTime} visit number ${reservation.id}`,
          452,
          {
            slotStart: slot.startTime,
            slotEnd: slot.endTime,
            reservationStart: reservation.startTime,
            reservationEnd: reservation.endTime,
            date,
            visitNumber: reservation.id
          }
        );
      }

    }

    return true; // No overlap found, keep this slot
  });
};
