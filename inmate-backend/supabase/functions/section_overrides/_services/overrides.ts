import {
  create as createOverrideRepository,
  getOverride as getOverrideRepository,
  deleteOverride as deleteOverrideRepository,
  createSlots as createSlotsRepository,
  deleteSlots as deleteSlotsRepository,
  deleteOverrideDate as deleteOverrideDateRepository,
  createDates as createDatesRepository,
} from "../_repository/overrides.ts";
import { checkOverridesInAvailableTimes } from "./checkOverrides.ts";
import { getVisits } from "../_repository/visitRequests.ts";
import { validateOverrideDate } from "../_repository/sectionOverrides.ts";
import { convertVisitTime } from "./convertVisit.ts";
import { generalAntiMatchSlots } from "./generalAntiMatchSlots.ts";
import { getSectionRooms } from "../_repository/room.ts";
import { getAvailability } from "../_repository/section.ts";
import { Slots, DateType, createOverrideSchema } from "../_dtos/dtos.ts";
export const deleteOverride = async (id: string) => {
  await deleteOverrideRepository(id);
};

export const getOverride = async ({
  id,
  section_id,
}: {
  id: string | null;
  section_id: string | null;
}) => {
  const override = await getOverrideRepository({ id, section_id });

  return override;
};

export async function createOverride(
  section_id: string,
  dates: DateType[],
  slots: Slots[]
) {
  await Promise.all(
    dates.map((date) => checkBySingleDate(section_id, date, slots))
  );
  // Call the create function from the repository
  const result = await createOverrideRepository(section_id, dates, slots);
  return result;
}

export async function updateOverride(
  override_id: string,
  dates: DateType[] | null | undefined,
  updateSlots: Slots[] | null | undefined
) {
  let override: any = {};
  if (dates && updateSlots) {
    override = await getOverrideRepository({
      id: override_id,
      section_id: null,
    });
    await Promise.all(
      dates.map((singleDate) =>
        checkBySingleDateUpdate(override.section_id, singleDate, updateSlots)
      )
    );
    createOverrideSchema.safeParse({
      section_id: override.section_id,
      dates,
      slots: updateSlots,
    });
    await deleteOverrideDateRepository(override_id);
    override.section_override_dates = await createDatesRepository(
      override_id,
      dates
    );
    await deleteSlotsRepository(override_id);
    override.section_overrides_timeslots = await createSlotsRepository(
      override_id,
      updateSlots
    );
  } else if (dates) {
    override = await getOverrideRepository({
      id: override_id,
      section_id: null,
    });
    const slots = override.slots.map((slot) => ({
      startTime: slot.starttime,
      endTime: slot.endtime,
    }));
    await Promise.all(
      dates.map((singleDate) =>
        checkBySingleDateUpdate(override.section_id, singleDate, slots)
      )
    );
    createOverrideSchema.safeParse({
      section_id: override.section_id,
      dates,
      slots,
    });
    await deleteOverrideDateRepository(override_id);
    override.section_override_dates = await createDatesRepository(
      override_id,
      dates
    );
  } else if (updateSlots) {
    override = await getOverrideRepository({
      id: override_id,
      section_id: null,
    });
    Promise.all(
      override.section_override_dates.map((singleDate) =>
        checkBySingleDateUpdate(
          override.section_id,
          singleDate.date,
          updateSlots
        )
      )
    );
    createOverrideSchema.safeParse({
      section_id: override.section_id,
      dates: override.section_override_dates.map((date) => date.date),
      slots: updateSlots,
    });
    await deleteSlotsRepository(override_id);
    override.section_overrides_timeslots = await createSlotsRepository(
      override_id,
      updateSlots
    );
  }
  return override;
}

async function checkBySingleDate(
  section_id: string,
  date: DateType,
  slots: Slots[]
) {
  await getAvailability(section_id, date);
  // await Promise.all(
  //   slots.map((slot) =>
  //     checkOverridesInAvailableTimes(availableTimes, slot, [date])
  //   )
  // );
  await validateOverrideDate(section_id, date);
  const roomIds = await getSectionRooms(section_id);
  const visits = await getVisits(date, roomIds);
  const visitTimes = visits.map((visit) => convertVisitTime(visit));
  generalAntiMatchSlots(visitTimes, slots, date);
}

async function checkBySingleDateUpdate(
  section_id: string,
  date: DateType,
  slots: Slots[]
) {
  await getAvailability(section_id, date);
  // await Promise.all(
  //   slots.map((slot) =>
  //     checkOverridesInAvailableTimes(availableTimes, slot, [date])
  //   )
  // );
  const roomIds = await getSectionRooms(section_id);
  const visits = await getVisits(date, roomIds);
  const visitTimes = visits.map((visit) => convertVisitTime(visit));
  generalAntiMatchSlots(visitTimes, slots, date);
}
