export class CustomError extends <PERSON>rror {
  public statusCode: number;
  [key: string]: any; // Allow dynamic properties

  constructor(message: string, statusCode?: number, additionalProperties?: Record<string, any>) {
    super(message);
    this.name = "CustomError";
    this.statusCode = statusCode || 400;
    
    // Set additional properties dynamically
    if (additionalProperties) {
      Object.assign(this, additionalProperties);
    }
  }

  // Method to set properties after instantiation
  setProperty(key: string, value: any): this {
    this[key] = value;
    return this;
  }

  // Method to set multiple properties
  setProperties(properties: Record<string, any>): this {
    Object.assign(this, properties);
    return this;
  }
}
