// Advanced version using Proxy for dynamic property access
export class DynamicCustomError extends Error {
  public statusCode: number;
  private _properties: Record<string, any> = {};

  constructor(message: string, statusCode?: number) {
    super(message);
    this.name = "ValidationError";
    this.statusCode = statusCode || 400;

    // Return a Proxy to intercept property access
    return new Proxy(this, {
      get(target, prop, receiver) {
        if (prop in target || typeof prop === 'symbol') {
          return Reflect.get(target, prop, receiver);
        }
        return target._properties[prop as string];
      },
      set(target, prop, value, receiver) {
        if (prop in target || typeof prop === 'symbol') {
          return Reflect.set(target, prop, value, receiver);
        }
        target._properties[prop as string] = value;
        return true;
      },
      has(target, prop) {
        return Reflect.has(target, prop) || prop in target._properties;
      },
      ownKeys(target) {
        return [...Reflect.ownKeys(target), ...Object.keys(target._properties)];
      }
    });
  }

  getCustomProperties() {
    return { ...this._properties };
  }

  setCustomProperties(properties: Record<string, any>) {
    Object.assign(this._properties, properties);
    return this;
  }
}

// Usage
const dynamicError = new DynamicCustomError("Something failed", 500) as any;
dynamicError.userId = "12345";
dynamicError.requestId = "req-abc";
dynamicError.customData = { nested: { value: true } };

console.log(dynamicError.userId);        // "12345"
console.log(dynamicError.customData);    // { nested: { value: true } }
