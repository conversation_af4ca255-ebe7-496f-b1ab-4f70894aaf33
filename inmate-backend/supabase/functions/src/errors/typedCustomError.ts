// Type-safe version with generics
export class TypedCustomError<T = Record<string, any>> extends Error {
  public statusCode: number;
  public data: T;

  constructor(
    message: string, 
    statusCode?: number, 
    data?: T
  ) {
    super(message);
    this.name = "ValidationError";
    this.statusCode = statusCode || 400;
    this.data = data || {} as T;
  }

  setData(data: Partial<T>): this {
    this.data = { ...this.data, ...data };
    return this;
  }

  updateData(key: keyof T, value: T[keyof T]): this {
    this.data[key] = value;
    return this;
  }
}

// Usage with specific types
interface ValidationErrorData {
  field: string;
  code: string;
  value?: any;
}

interface DatabaseErrorData {
  query: string;
  table: string;
  constraint?: string;
}

// Type-safe usage
const validationError = new TypedCustomError<ValidationErrorData>(
  "Invalid email format",
  400,
  { field: "email", code: "INVALID_FORMAT" }
);

const dbError = new TypedCustomError<DatabaseErrorData>(
  "Database constraint violation",
  500,
  { query: "INSERT INTO users...", table: "users" }
);
