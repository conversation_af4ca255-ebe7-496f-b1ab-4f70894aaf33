// @ts-ignore
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

// In Supabase edge functions, environment variables are available via Deno.env
const SUPABASE_URL = Deno.env.get("SUPABASE_URL");
const SUPABASE_ANON_KEY = Deno.env.get("SUPABASE_ANON_KEY");
const SERVICE_ROLE_KEY = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY");

export const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
export const adminSupabase = createClient(SUPABASE_URL, SERVICE_ROLE_KEY, {
  auth: {
    autoRefreshToken: false,
    persistSession: false,
  },
});
