import { RoomServiceClient } from 'npm:livekit-server-sdk@latest';

const livekitHost = Deno.env.get('LIVEKIT_HTTP_HOST');
const livekitApiKey = Deno.env.get('LIVEKIT_API_KEY');
const livekitApiSecret = Deno.env.get('LIVEKIT_API_SECRET');

if (!livekitHost || !livekitApiKey || !livekitApiSecret) {
  throw new Error('Missing required LiveKit environment variables');
}

export const roomService = new RoomServiceClient(
  livekitHost,
  livekitApiKey,
  livekitApiSecret,
);
