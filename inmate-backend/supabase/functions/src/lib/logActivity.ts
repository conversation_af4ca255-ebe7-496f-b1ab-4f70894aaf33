// supabase/functions/lib/logActivity.ts
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.38.4";

const supabase = createClient(
    Deno.env.get("SUPABASE_URL")!,
    Deno.env.get("SUPABASE_SERVICE_ROLE_KEY")!
);

interface LogParams {
    user_id: string | null;
    entity_type: string;
    entity_id: string;
    action: "create" | "update" | "delete";
    old_data?: unknown;
    new_data?: unknown;
    metadata?: Record<string, unknown>;
}

export async function logActivity(params: LogParams) {
    const { error } = await supabase
        .from("activity_logs")
        .insert({
            user_id: params.user_id,
            entity_type: params.entity_type,
            entity_id: params.entity_id,
            action: params.action,
            old_data: params.old_data,
            new_data: params.new_data,
            metadata: params.metadata,
        });

    if (error) {
        console.error("Failed to log activity:", error);
        throw error;
    }
}

export async function getUserFromRequest(req: Request) {
    const token = req.headers.get("Authorization")?.replace("Bearer ", "");
    const { data: { user } } = await supabase.auth.getUser(token);
    return user;
}