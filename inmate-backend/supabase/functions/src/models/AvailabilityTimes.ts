import {supabase} from "../lib/SupabaseClient.ts";

export async function AvailabilityTimes(settings: any, settingsData: any) {
    const {data: timesData, error: timesError} = await supabase
        .from('availability_times')
        .select('id, setting_id, available_from, available_to')
        .eq('type', settings.model)
        .in('setting_id', settingsData.map(({id}) => id));
    if (timesError) {
        throw new Error(`Error fetching AvailabilityTimes: ${timesError.message}`);
    }

    return timesData
}
