import {supabase} from "../lib/SupabaseClient.ts";

export interface Building {
    id: string;
    name: string;
    section_availability_settings: Record<string, any>;
}

export async function getBuildingById(buildingId: string): Promise<Building | null> {
    const {data: buildingData, error: roomError} = await supabase
        .from('sections')
        .select('id, is_available, section_availability_settings(id, day)')
        .eq('id', buildingId)
        .single();

    if (roomError) {
        throw new Error(`Error fetching building details and availability settings: ${roomError.message}`);
    }
    return buildingData;
}
