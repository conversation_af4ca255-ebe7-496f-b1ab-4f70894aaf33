import {supabase} from "../lib/SupabaseClient.ts";
import {getBuildingById} from "./Building.ts";

export interface Inmate {
    id: string;
    full_name: string;
    national_id: string;
    number_of_visits: number;
    section_id: string;
    room_id: string | null;
    is_building_default: boolean;
    is_room_default: boolean;
    inmate_code: string;
    nationality: string;
    status_id: number;
    inmate_availability_settings?: Record<string, any> | null;
}

export async function getInmateById(inmateId: string, columns: string = '*'): Promise<Inmate | null> {
    const {data: inmateData, error: inmateError} = await supabase
        .from('inmates')
        .select(columns)
        .eq('id', inmateId)
        .single();

    if (inmateError) {
        throw new Error(`Error fetching inmate details and availability settings: ${inmateError.message}`);
    }
    return inmateData;
}

/**
 * Retrieves the effective settings for an inmate.
 * If customSettings are provided and non-empty, returns them.
 * Otherwise, falls back to the building's defaultSettings.
 */
export async function getInmateEffectiveSettings(inmateId: string): Promise<Record<string, any> | null> {
    const inmate = await getInmateById(inmateId, 'id, status_id, section_id, inmate_availability_settings(id, day)');
    if (!inmate) return null;

    if (inmate.inmate_availability_settings && Object.keys(inmate.inmate_availability_settings).length > 0) {
        return {
            'model': 'inmate',
            'times_settings': inmate.inmate_availability_settings
        };
    } else {
        const building = await getBuildingById(inmate.section_id);
        return building ? {
            'model': 'section',
            'times_settings': building.section_availability_settings
        } : null;
    }
}
