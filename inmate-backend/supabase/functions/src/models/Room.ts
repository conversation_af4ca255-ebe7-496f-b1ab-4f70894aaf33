import {supabase} from "../lib/SupabaseClient.ts";
import {getBuildingById} from "./Building.ts";
import { NotAvailableError } from "../errors/notAvailalbeError.ts";

export interface Room {
    id: string;
    name: string;
    section_id: string;
    active: boolean;
    room_availability_settings?: Record<string, any> | null;
    is_building_default: boolean;
}

export async function getRoomById(roomId: string): Promise<Room | null> {
    const {data: roomData, error: roomError} = await supabase
        .from('rooms')
        .select('active, section_id, room_availability_settings(id, day), is_building_default')
        .eq('id', roomId)
        .single();

    if (roomData.active === false) {
        throw new NotAvailableError(`Room with ID ${roomId} is inactive.`);
    }

    if (roomError) {
        throw new Error(`Error fetching room details and availability settings: ${roomError.message}`);
    }
    return roomData;
}

/**
 * Retrieves the effective settings for a room.
 * If customSettings are provided and non-empty, returns them.
 * Otherwise, falls back to the building's defaultSettings.
 */
export async function getRoomEffectiveSettings(roomId: string): Promise<Record<string, any> | null> {
    const room = await getRoomById(roomId);
    if (!room) return null;

    if (room.room_availability_settings && Object.keys(room.room_availability_settings).length > 0 && !room.is_building_default) {
        return {
            'model': 'room',
            'times_settings': room.room_availability_settings
        };
    } else {
        const building = await getBuildingById(room.section_id);
        return building ? {
            'model': 'section',
            'times_settings': building.section_availability_settings
        } : null;
    }
}
