/*
* TODO::This file is deprecated
* */
import {Pipeline, PipelineStage} from "./Pipeline.ts";
import {AvailabilityTimesService} from "../services/AvailabilityTimesService.ts";
import {Slot} from "../entities/Slot.ts";

export class BuildingAvailabilityStage implements PipelineStage<Slot[]> {
    constructor(private buildingId: string, private date: string, private availabilityTimesService: AvailabilityTimesService) {
    }

    async process(slots: Slot[]): Promise<Slot[]> {

        if (!this.buildingId) return slots;

        // Retrieve building availability slots
        const BuildingSlots = await this.availabilityTimesService.getAvailability(this.buildingId, this.date, "section");

        // If there are initial slots already, merge them with the newly retrieved ones
        // using the Pipeline.matchSlots static function; otherwise, use the retrieved slots directly.
        const matchedSlots = slots.length > 0
            ? Pipeline.matchSlots(slots, BuildingSlots)
            : BuildingSlots;

        return matchedSlots;
    }

}
