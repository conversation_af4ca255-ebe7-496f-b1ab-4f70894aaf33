import {Pipeline, PipelineStage} from "./Pipeline.ts";
import {AvailabilityTimesService} from "../services/AvailabilityTimesService.ts";
import {Slot} from "../entities/Slot.ts";

export class InmateAvailabilityStage implements PipelineStage<Slot[]> {
    constructor(private inmateId: string, private date: string, private availabilityTimesService: AvailabilityTimesService) {
    }

    // @ts-ignore
    async process(slots: Slot[]): Promise<Slot[]> {
        if (!this.inmateId) return slots;
        const inmateSlots = await this.availabilityTimesService.getAvailability(this.inmateId, this.date, "inmate");

        // If there are initial slots already, merge them with the newly retrieved ones
        // using the Pipeline.matchSlots static function; otherwise, use the retrieved slots directly.
        const matchedSlots = slots.length > 0
            ? Pipeline.matchSlots(slots, inmateSlots)
            : inmateSlots;

        return matchedSlots;
    }
}