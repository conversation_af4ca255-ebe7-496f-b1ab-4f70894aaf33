import {PipelineStage} from "./Pipeline.ts";
import {Slot} from "../entities/Slot.ts";

export class InmateAvailabilityValidationStage implements PipelineStage<Slot[]> {
    constructor(private inmateData: any, private date: string) {
    }

    // @ts-ignore
    async process(slots: Slot[]): Promise<Slot[] | null> {
        if (!this.inmateData || !this.date) return slots;

        if (this.inmateData.status_id !== (Deno.env.get("INMATE_STATUS_ID_AVAILABLE") || 1)) {
            throw new Error(`Inmate is unavailable (Status: ${this.inmateData.status}).`);
        }

        return slots;
    }
}
