import {Slot} from "../entities/Slot.ts";

export interface PipelineStage<T> {
    process(input: T): Promise<T | null>;
}

export class Pipeline<T> {
    private stages: PipelineStage<T>[] = [];

    addStage(stage: PipelineStage<T>): this {
        this.stages.push(stage);
        return this;
    }

    public static matchSlots(slotsA: Slot[], slotsB: Slot[]): Slot[] {

        // If one array is empty, return the other one with some transformations
        if (slotsB.length === 0 && slotsA.length > 0) {
            return slotsA;
        }
        
        if (slotsA.length === 0 && slotsB.length > 0) {
            return slotsB;
        }
        
        // If both arrays have data, do the intersection logic
        if (slotsA.length > 0 && slotsB.length > 0) {
            const combined = slotsA.flatMap(slotA => {
                return slotsB.map(slotB => {
                    const latestStart = new Date(Math.max(slotA.startTime.getTime(), slotB.startTime.getTime()));
                    const earliestEnd = new Date(Math.min(slotA.endTime.getTime(), slotB.endTime.getTime()));

                    if (latestStart < earliestEnd) {
                        console.log("Slot A:", slotA);
                        console.log("Slot B:", slotB);
                        console.log('latestStart',latestStart);
                        console.log('earliestEnd', earliestEnd)
                        console.log('finish');
                        return {
                            id: `${slotA.id}-${slotB.id}`,
                            startTime: latestStart,
                            endTime: earliestEnd,
                        };
                    }
                    return null;
                }).filter(slot => slot !== null);
            });
            
            return combined;
        }

        return [];
    }

    // @ts-ignore
    async execute(input: T): Promise<T | null> {
        let result: T | null = input;

        try {
            for (const stage of this.stages) {
                if (result === null) {
                    console.error("Pipeline execution stopped.");
                    return null;
                }

                result = (await stage.process(result));
                if (result === null) {
                    console.error("Stage returned null. Stopping pipeline.");
                    return null;
                }
            }
        } catch (error) {
            console.error("Pipeline Execution Error:", error.message);
            return null; // Stop the pipeline and return null when an error occurs
        }

        return result;
    }
}
