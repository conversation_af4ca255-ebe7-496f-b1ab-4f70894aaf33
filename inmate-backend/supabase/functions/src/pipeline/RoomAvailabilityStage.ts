import {Pipeline, PipelineStage} from "./Pipeline.ts";
import {AvailabilityTimesService} from "../services/AvailabilityTimesService.ts";
import {Slot} from "../entities/Slot.ts";

export class RoomAvailabilityStage implements PipelineStage<Slot[]> {
    constructor(private roomId: string | null, private date: string, private availabilityTimesService: AvailabilityTimesService) {
    }

    async process(slots: Slot[]): Promise<Slot[]> {

        if (!this.roomId) return slots;

        // Retrieve room availability slots
        const roomSlots = await this.availabilityTimesService.getAvailability(this.roomId, this.date, "room");

        // If there are initial slots already, merge them with the newly retrieved ones
        // using the Pipeline.matchSlots static function; otherwise, use the retrieved slots directly.
        const matchedSlots = slots.length > 0
            ? Pipeline.matchSlots(slots, roomSlots)
            : roomSlots;

        return matchedSlots;
    }
}
