import {Slot} from "../entities/Slot.ts";
import {getInmateEffectiveSettings} from "../models/Inmate.ts";
import {getRoomEffectiveSettings} from "../models/Room.ts";
import {AvailabilityTimes} from "../models/AvailabilityTimes.ts";

export class AvailabilityTimesService {

    private meetingDuration: number;
    private betweenMeetingDuration: number;

    constructor(meetingDuration: number = 25, betweenMeetingDuration: number = 5) {
        this.meetingDuration = meetingDuration;
        this.betweenMeetingDuration = betweenMeetingDuration;
    }
    
    async getAvailability(availableId: string, date: string, availableType: 'section' | 'room' | 'inmate'): Promise<Slot[]> {
        let data = []
        if (availableType === 'inmate') {
            data = await this.getInmateTimes(availableId, date)
        } else if (availableType === 'room') {
            data = await this.getRoomTimes(availableId, date)
        }
        if (data.length > 0) {
            const slots: Slot[] = [];
            data.map((record: any) => this.generateSlots(record, date, slots, this.meetingDuration, this.betweenMeetingDuration));
            return slots
        }
        return data
    }

    private generateSlots(record: any, date: string, slots: Slot[], intervalMinutes: number, betweenMeetingDuration: number) {
        // Convert to Date objects
        const startTime = new Date(`${date}T${record.available_from}`);
        const endTime = new Date(`${date}T${record.available_to}`);
        
        // Calculate total duration in minutes
        const durationMs = endTime.getTime() - startTime.getTime();
        const durationMinutes = durationMs / (1000 * 60);
        
        // Split into 30-minute intervals
        const interval = intervalMinutes + betweenMeetingDuration;
        const numberOfSlots = Math.floor(durationMinutes / interval);
        
        for (let i = 0; i < numberOfSlots; i++) {
            const slotStart = new Date(startTime);
            slotStart.setMinutes(startTime.getMinutes() + (i * interval));
            
            let slotEnd = new Date(slotStart);
            slotEnd.setMinutes(slotStart.getMinutes() + intervalMinutes);
            
            // Don't exceed the original end time
            if (slotEnd > endTime) {
                slotEnd = new Date(endTime);
            }
            
            slots.push({
                id: `${record.id}-${i}`,  // Unique ID for each sub-slot
                startTime: slotStart,
                endTime: slotEnd,
            });
        }
    }

    private async getInmateTimes(inmateId: string, date: string) {
        // Convert date to uppercase day of the week to match database format
        const dayOfWeek = new Date(date)
            .toLocaleString('en-US', {weekday: 'long'})
            .toUpperCase();

        // Fetch inmate details along with availability settings for the given day
        const settings = await getInmateEffectiveSettings(inmateId);
        if (!settings) {
            console.error(`Inmate not found or no settings available with id ${inmateId}`)
            return []
        }
        // Filter only settings that match the requested day

        const settingsData = (settings.times_settings || []).filter(setting => setting.day.toUpperCase() === dayOfWeek);

        if (settingsData.length === 0) {
            console.error(`No availability settings found for the given inmate ${inmateId} on ${dayOfWeek}`)
            return []
        }

        // Step 3: Fetch availability times for INMATE type
        return AvailabilityTimes(settings, settingsData)
    }

    private async getRoomTimes(roomId: string, date: string) {
        // Convert date to uppercase day of the week to match database format
        const dayOfWeek = new Date(date)
            .toLocaleString('en-US', {weekday: 'long'})
            .toUpperCase();

        // Combine Step 1 and Step 2: Fetch room details along with availability settings for the given day
        const settings = await getRoomEffectiveSettings(roomId)
        if (!settings) {
            console.error('Inmate not found or no settings available')
            return []
        }

        // Filter only settings that match the requested day
        const settingsData = (settings.times_settings || []).filter(setting => setting.day.toUpperCase() === dayOfWeek);

        if (settingsData.length === 0) {
            console.error(`No availability settings found for the given room ${roomId} on ${dayOfWeek}`)
            return []
        }

        // Step 3: Fetch availability times for ROOM type
        return AvailabilityTimes(settings, settingsData)
    }
}