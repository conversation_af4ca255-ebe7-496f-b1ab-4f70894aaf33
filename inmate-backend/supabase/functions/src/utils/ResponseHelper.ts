const DEFAULT_HEADERS = {
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Methods": "GET, OPTIONS",
    "Access-Control-Allow-Headers": "Content-Type, Authorization",
    "Access-Control-Allow-Credentials": "true",
    "Vary": "Origin",
    "Content-Type": "application/json",
};

export class ResponseHelper {
    /**
     * Standardized success response
     * @param data - The response payload
     * @param message - Optional success message
     * @param metadata - Optional metadata (pagination, additional info)
     * @param headers - Optional additional headers (will be merged with default CORS headers)
     * @returns Standardized success response object
     */
    static successResponse(data: any, message: string = "Success", metadata: any = null, headers: Record<string, string> = {}) {
        const response: any = {
            status: "success",
            message,
            data
        };
        if (metadata) response.metadata = metadata;

        return new Response(JSON.stringify(response), {
            status: 200,
            headers: {...DEFAULT_HEADERS, ...headers}, // Merge default headers with custom ones
        });
    }

    /**
     * Standardized error response
     * @param errorMessage - Error message
     * @param statusCode - HTTP status code (default 400)
     * @param details - Optional details for debugging
     * @param headers - Optional additional headers (will be merged with default CORS headers)
     * @returns Standardized error response object
     */
    static errorResponse(errorMessage: string, statusCode: number = 400, details: any = null, headers: Record<string, string> = {}) {
        console.error(`Error [${statusCode}]:`, errorMessage, details || "");
        const response: any = {
            status: "error",
            message: errorMessage,
        };
        if (details) response.details = details;

        return new Response(JSON.stringify(response), {
            status: statusCode,
            headers: {...DEFAULT_HEADERS, ...headers}, // Merge default headers with custom ones
        });
    }
}
