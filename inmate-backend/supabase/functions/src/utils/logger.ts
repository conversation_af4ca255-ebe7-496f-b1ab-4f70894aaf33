import { createClient } from "npm:@supabase/supabase-js@2.39.3";

const supabase = createClient(
  Deno.env.get("SUPABASE_URL")!,
  Deno.env.get("SUPABASE_ANON_KEY")!
);

// const isProduction = Deno.env.get("ENVIRONMENT") === "production";
const isProduction = true; // For testing purposes, set to true


export function withLogging(handler: (req: Request) => Promise<Response>, name?: string) {
    return async (req: Request) => {
      const logs: string[] = [];
      const originalLog = console.log;
      const originalError = console.error;
  
      // Override logs
      console.log = (...args: unknown[]) => {
        const msg = args.map(arg => {
          try {
            return typeof arg === "object" ? JSON.stringify(arg, null, 2) : String(arg);
          } catch {
            return String(arg);
          }
        }).join(" ");
        logs.push(`[${new Date().toISOString()}] ${msg}`);
        originalLog(...args);
      };
  
      console.error = (...args: unknown[]) => {
        const msg = args.map(arg => {
          if (arg instanceof Error) {
            return `${arg.name}: ${arg.message}\n${arg.stack}`;
          }
          try {
            return typeof arg === "object" ? JSON.stringify(arg, null, 2) : String(arg);
          } catch {
            return String(arg);
          }
        }).join(" ");
        logs.push(`[${new Date().toISOString()}] ERROR: ${msg}`);
        originalError(...args);
      };
      
  
      try {
        console.log("Request started", name);
        const response = await handler(req);
        console.log(`Responded with status ${response.status}`, name);
        return response;
      } catch (err) {
        console.error("Handler error:", err);
        return new Response("Internal Server Error", { status: 500 });
      } finally {
        // Always log, regardless of success/failure
        if (name) {
          try {
            await logToDatabase(logs.join("\n"), name);
          } catch (logError) {
            originalError("Failed to store logs:", logError);
          }
        }
        // Restore console
        console.log = originalLog;
        console.error = originalError;
      }
    };
  }
  

function logConsole(level: "info" | "error", message: string, name?: string) {
  const label = name ? `[${name}]` : "";
  const timestamp = new Date().toISOString();

  if (level === "info") {
    console.log(`%c[INFO] ${timestamp} ${label} ${message}`, "color: green");
  } else {
    console.error(`%c[ERROR] ${timestamp} ${label} ${message}`, "color: red");
  }
}

export async function logToStorage(logs: string, functionName: string) {
    try {
      const date = new Date().toISOString().split("T")[0];
      const logFile = `logs/${date}.log`;
  
      const { data, error: downloadError } = await supabase.storage
        .from("edge-logs")
        .download(logFile);
  
      let existingLogs = "";
      if (data) existingLogs = await data.text();
  
      const finalLog = `[${new Date().toISOString()}] [${functionName}] ${logs}`;
      const updatedLogs = `${existingLogs}\n${finalLog}`;
  
      const { error } = await supabase.storage
        .from("edge-logs")
        .upload(logFile, new Blob([updatedLogs]), {
          upsert: true,
          contentType: "text/plain",
        });
  
      if (error) console.error("Failed to upload logs to storage:", error.message);
    } catch (error) {
      console.error("Error logging to storage:", error.message);
    }
  }
  

export async function logToDatabase(message: string, functionName?: string, level: string = "info") {
  try {
    const { error } = await supabase.from("function_logs").insert([
      {
        function_name: functionName,
        message,
      },
    ]);

    if (error) console.error("DB log failed:", error.message);
  } catch (error) {
    console.error("DB logging error:", error);
  }
}
