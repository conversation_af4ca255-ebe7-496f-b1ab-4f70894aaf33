export function isValidISODate(date: Date, dateString: string): boolean {

    // Check if the parsed date is valid and matches the input
    return (
      !isNaN(date.getTime()) && 
      date.toISOString() === dateString
    );
  }

/**
* Simple function to validate if a Date object matches a date string (date portion only)
*/
export function isValidDateSimple(date: Date, dateString: string): boolean {
  return !isNaN(date.getTime()) && 
         date.toISOString().split('T')[0] === dateString.split('T')[0];
}
