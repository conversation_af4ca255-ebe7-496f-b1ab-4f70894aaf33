import { supabase } from "../../src/lib/SupabaseClient.ts";
import { NotFoundError } from "../../src/errors/notFoundError.ts";
import { CustomError } from "../../src/errors/customError.ts";

export const getRoom = async (roomId: string): Promise<any> => {
  const { data, error } = await supabase
    .from("rooms")
    .select("active, status_id")
    .eq("id", roomId)
    .single();

  if (error) {
    if (error.code === "PGRST116") {
      throw new NotFoundError(`Room with ID ${roomId} not found`);
    }
    throw new Error(`Error fetching room: ${error.message}`);
  }
  if (!data) {
    throw new NotFoundError(`Room with ID ${roomId} not found`);
  }
  if (!data.active) {
    throw new CustomError("The Room is Inactive", 470);
  }
  if (data.status_id !== 2) {
    throw new CustomError("The Room is not connected", 470);
  }
};
