import { supabase } from "../../src/lib/SupabaseClient.ts";

export const getServer = async () => {
  const { data, error } = await supabase
    .from("servers")
    .select(
      "link"
    )
    .eq("isactive", true);

  if (error) {
    throw new Error(`Error fetching active server: ${error.message}`);
  }

  if (!data || data.length === 0) {
    new Error("No active server found, defaulting to localhost");
  }
  return data[0].link; // Default to localhost if no active server found
};
