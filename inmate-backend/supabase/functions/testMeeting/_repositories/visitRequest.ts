import { supabase } from "../../src/lib/SupabaseClient.ts";
import { NotFoundError } from "../../src/errors/notFoundError.ts";

export async function create(
  visitor_id: string,
  inmate_id: string,
  room_id: string,
  datetime: string,
  duration: number,
  meeting_url: string
): Promise<any> {
  const { data, error } = await supabase
    .from("visit_requests")
    .insert({
      visitor_id,
      inmate_id,
      datetime,
      duration,
      status_id: Number(Deno.env.get("VISIT_REQUEST_STATUS_APPROVED")) || 2,
      archived: false,
      room_id,
      test: true,
      meeting_url,
    })
    .select("*");

  if (error) {
    throw new Error(`Error fetching visit request: ${error.message}`);
  }
  return data;
}

export const update = async (meetingId: string, updates: Record<string, any>): Promise<any> => {
  const { data, error } = await supabase
    .from("visit_requests")
    .update(updates)
    .eq("id", meetingId)
    .select() // Return all updated fields
    .single();

  if (error) {
    if (error.code === "PGRST116") {
      throw new NotFoundError("Meeting not found");
    } else {
      throw new Error(`Error updating meeting: ${error.message}`);
    }
  }

}


export async function getVisits(room_id: string, date: string) {
  // First, check if there are any conflicting visit requests
  const allowedStatusIds = [
    Number(Deno.env.get("VISIT_REQUEST_STATUS_STARTING_SOON")) || 3,
    Number(Deno.env.get("VISIT_REQUEST_STATUS_AWAITING_ADMIN")) || 4,
    Number(Deno.env.get("VISIT_REQUEST_STATUS_RUNNIING")) || 5,

  ];

  const { data: conflictingVisits, error: checkError } = await supabase
    .from("visit_requests")
    .select("id, datetime, room_id, visit_number, duration")
    .eq("room_id", room_id)
    .eq("test", false)
    .gte("datetime", `${date}T00:00:00.000Z`)
    .lte("datetime", `${date}T23:59:59.999Z`)
    .in("status_id", allowedStatusIds);

  if (checkError) {
    if (checkError.code === "PGRST116") {
      // Do nothing for this specific error code
    } else {
      throw new Error("Server Error");
    }
  }
  return conflictingVisits;
}

export async function getTestMeetings(room_id: string, date: string) {
  // First, check if there are any conflicting visit requests
  const allowedStatusIds = [
    Number(Deno.env.get("VISIT_REQUEST_STATUS_STARTING_SOON")) || 3,
    Number(Deno.env.get("VISIT_REQUEST_STATUS_AWAITING_ADMIN")) || 4,
    Number(Deno.env.get("VISIT_REQUEST_STATUS_RUNNIING")) || 5,
  ];

  const { data: conflictingVisits, error: checkError } = await supabase
    .from("visit_requests")
    .select("id, datetime, room_id, visit_number, duration")
    .eq("room_id", room_id)
    .eq("test", true)
    .gte("datetime", `${date}T00:00:00.000Z`)
    .lte("datetime", `${date}T23:59:59.999Z`)
    .in("status_id", allowedStatusIds);

  if (checkError) {
    if (checkError.code === "PGRST116") {
      // Do nothing for this specific error code
    } else {
      throw new Error("Server Error");
    }
  }
  return conflictingVisits;
}
