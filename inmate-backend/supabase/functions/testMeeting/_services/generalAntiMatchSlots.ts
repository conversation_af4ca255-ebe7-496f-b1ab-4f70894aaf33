import { CustomError } from "../../src/errors/customError.ts";

export const generalAntiMatchSlots = (slotsA, { startTime, endTime }, date, message) => {
  // Filter out slots that overlap with any reservation
  // Check each reservation for overlap with this slot

  for (const reservation of slotsA) {
    if (reservation.startTime < endTime && reservation.endTime > startTime) {
      const statusCode = message === "visit" ? 471 : 472;
      throw new CustomError(`You have conflict with ${message} number ${reservation.id}`, statusCode, {
        reservationStart: reservation.startTime,
        reservationEnd: reservation.endTime,
        date,
        reservationType: message,
        visitNumber: reservation.id,
      });
    }
  }

};
