import {
  getVisits,
  getTestMeetings,
  create,
  update
} from "../_repositories/visitRequest.ts";
import { getMeetingDuration } from "../_repositories/meetingDuration.ts";
import { getVisitor } from "../_repositories/visitor.ts";
import { getInmate } from "../_repositories/inmate.ts";
import { getServer } from "../_repositories/server.ts";
import { convertVisitTime } from "./convertVisit.ts";
import { generalAntiMatchSlots } from "./generalAntiMatchSlots.ts";
import { roomService } from "../../src/lib/liveKit.ts";
import { getRoom } from "../_repositories/room.ts";

async function checkRoomAvailability(
  roomId: string,
  date: string,
  startTime: Date,
  endTime: Date
) {
  await Promise.all([
    checkAgainstVisits(roomId, date, startTime, endTime),
    checkAgainstTestMeetings(roomId, date, startTime, endTime),
  ]);
}

async function checkAgainstVisits(
  roomId: string,
  date: string,
  startTime: Date,
  endTime: Date
) {
  const visits = await getVisits(roomId, date);
  const visitTimes = visits.map((visit) => convertVisitTime(visit));
  generalAntiMatchSlots(visitTimes, { startTime, endTime }, date, "visit");
}

async function checkAgainstTestMeetings(
  roomId: string,
  date: string,
  startTime: Date,
  endTime: Date
) {
  const testMeetings = await getTestMeetings(roomId, date);
  const testMeetingsTimes = testMeetings.map((visit) =>
    convertVisitTime(visit)
  );
  generalAntiMatchSlots(
    testMeetingsTimes,
    { startTime, endTime },
    date,
    "test meeting"
  );
}

export async function startTestMeeting(roomId) {
  await getRoom(roomId);
  const [meetingDuration, betweenMeetingInterval] = await getMeetingDuration();

  const datetime = new Date();
  const date = datetime.toISOString().split("T")[0];
  datetime.setSeconds(0, 0); // Set seconds and milliseconds to 0
  const endTime = new Date(
    datetime.getTime() + (meetingDuration + betweenMeetingInterval) * 60000
  );
  await checkRoomAvailability(roomId, date, datetime, endTime);
  const [visitorId, inmateId, serverLink] = await Promise.all([
    getVisitor(),
    getInmate(),
    getServer(),
  ]);

  const testMeeting = await create(
    visitorId,
    inmateId,
    roomId,
    datetime.toISOString(),
    meetingDuration,
    serverLink
  );

  const createOptions = {
    name: testMeeting[0].id,
    maxParticipants: 3,
    emptyTimeout: meetingDuration,
  };

  await roomService.createRoom(createOptions);
  await update(testMeeting[0].id, {
    status_id: Deno.env.get("VISIT_REQUEST_STATUS_RUNNIING") || 5,
  });

  return testMeeting[0];
}
