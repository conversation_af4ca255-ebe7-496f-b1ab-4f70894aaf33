import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { startTestMeeting } from "./_services/visitRequest.ts";
import { ResponseHelper } from "../src/utils/ResponseHelper.ts";
import { withLogging } from "../src/utils/logger.ts"; // Change from default import to named import
import { createTestMeetingSchema } from "./dtos.ts";

Deno.serve(
  withLogging(async (req) => {
    if (req.method === "OPTIONS") {
      return ResponseHelper.successResponse(null, "CORS preflight response");
    }
  
    try {
      if (req.method !== "POST") {
        return ResponseHelper.errorResponse(
          "Invalid request method, only POST is allowed",
          405
        );
      }

      const body = await req.json();
      const parsedBody = createTestMeetingSchema.safeParse(body);
      if (!parsedBody.success) {
        return new Response(
          JSON.stringify({
            error: "Invalid request body",
            details: parsedBody.error.format(),
          }),
          { status: 400 }
        );
      }
      const { roomId } = body;
      const testMeeting = await startTestMeeting(roomId);
      return ResponseHelper.successResponse(testMeeting,
        "Test Meeting Got successfully"
      );
    } catch (error) {
      const status = error.statusCode || 500;
      const errorMessage = error.message || "Internal Server Error";
      if (status === 500) {
        console.error("Internal Server Error:", error);
      }
      return ResponseHelper.errorResponse(errorMessage, status);
    }
  }, "Test Meeting")
);
