import { supabase } from "../../src/lib/SupabaseClient.ts";
import { NotFoundError } from "../../src/errors/notFoundError.ts";

export async function get(visit_id: string) {

  const { data: transcriptionData, error: checkError } = await supabase
    .from("meetings_transacriptions")
    .select("transcription")
    .eq("meeting_id", visit_id)
    .single();

  if (checkError) {
    if (checkError.code === "PGRST116") {
      throw new NotFoundError(
        `Transcription not found for visit_id: ${visit_id}`
      );
    } else {
      throw new Error(`Error fetching transcription: ${checkError.message}`);
    }
  }
  return transcriptionData?.transcription || null;
}
