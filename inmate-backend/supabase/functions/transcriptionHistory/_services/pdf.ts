import { jsPDF } from "https://esm.sh/jspdf@latest";

export async function yeildPdf(transcription: any, visitId: string) {
  // Parse the stringified transcription data
  let transcriptionData;
  try {
    transcriptionData = JSON.parse(transcription);
  } catch (error) {
    return new Response(
      JSON.stringify({ error: "Invalid transcription data format" }),
      { status: 400 }
    );
  }

  // Generate PDF
  const pdfDoc = new jsPDF();
  const pageHeight = pdfDoc.internal.pageSize.height;
  const pageWidth = pdfDoc.internal.pageSize.width;
  const margin = 20;
  let yPosition = margin;

  // Add title
  pdfDoc.setFontSize(16);
  pdfDoc.setFont("helvetica", "bold");
  pdfDoc.text("Meeting Transcription", margin, yPosition);
  yPosition += 20;

  // Add visit information
  pdfDoc.setFontSize(12);
  pdfDoc.setFont("helvetica", "normal");
  pdfDoc.text(`Visit ID: ${visitId}`, margin, yPosition);
  yPosition += 10;
  pdfDoc.text(`Generated: ${new Date().toLocaleString()}`, margin, yPosition);
  yPosition += 20;

  // Process transcription entries with participant, transcript, and timestamp
  if (Array.isArray(transcriptionData)) {
    transcriptionData.forEach((item, index) => {
      // Check if we need a new page
      if (yPosition > pageHeight - 40) {
        pdfDoc.addPage();
        yPosition = margin;
      }

      // Format the line similar to the text file: "participant: transcript [timestamp]"
      const formattedLine = `${item.participant}: ${item.transcript} [${item.timestamp}]`;
      
      // Split text to fit within page width
      const splitText = pdfDoc.splitTextToSize(formattedLine, pageWidth - 2 * margin);
      splitText.forEach((line: string) => {
        if (yPosition > pageHeight - 20) {
          pdfDoc.addPage();
          yPosition = margin;
        }
        pdfDoc.setFont("helvetica", "normal");
        pdfDoc.setFontSize(11);
        pdfDoc.text(line, margin, yPosition);
        yPosition += 6;
      });

      yPosition += 3; // Small space between entries
    });
  } else {
    // Handle case where transcription is a single string
    const splitText = pdfDoc.splitTextToSize(
      transcriptionData,
      pageWidth - 2 * margin
    );
    splitText.forEach((line: string) => {
      if (yPosition > pageHeight - 20) {
        pdfDoc.addPage();
        yPosition = margin;
      }
      pdfDoc.text(line, margin, yPosition);
      yPosition += 6;
    });
  }

  // Generate PDF buffer
  return pdfDoc.output("arraybuffer");
}
