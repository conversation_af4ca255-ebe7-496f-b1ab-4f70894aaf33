import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { withLogging } from "../src/utils/logger.ts";
import { uuidSchema } from "./_dtos/dtos.ts";
import { read } from "./_services/transcription.ts";
import { yeildPdf } from "./_services/pdf.ts";
import { createTextFile } from "./_services/textFile.ts";

Deno.serve(
  withLogging(async (req) => {
    try {
      if (req.method === "GET") {
        const url = new URL(req.url);
        const params = url.pathname;
        const pathList = params.split("/");
        if (pathList.length < 3) {
          return new Response(
            JSON.stringify({
              error: "Invalid request parameters",
              details: "Missing visit ID in the URL",
            }),
            { status: 400 }
          );
        }
        const visitId = pathList[2];
        const parsedRoomId = uuidSchema.safeParse(visitId);
        if (!parsedRoomId.success) {
          return new Response(
            JSON.stringify({
              error: "Invalid Visit ID",
              details: parsedRoomId.error.format(),
            }),
            { status: 400 }
          );
        }
        const transcription = await read(visitId);
        const pdf = url.searchParams.get("pdf");
        if (pdf) {
          const pdfBuffer = await yeildPdf(transcription, visitId);

          return new Response(pdfBuffer, {
            status: 200,
            headers: {
              "Content-Type": "application/pdf",
              "Content-Disposition": `attachment; filename="transcription_${visitId}.pdf"`,
            },
          });
        }
        const textFile = url.searchParams.get("textFile");
        if (textFile) {
          const textContent = createTextFile(transcription);
          return new Response(textContent, {
            status: 200,
            headers: {
              "Content-Type": "text/plain",
              "Content-Disposition": `attachment; filename="transcription_${visitId}.txt"`,
            },
          });
        }
        return new Response(transcription, {
          status: 200,
        });
      }

      return new Response("Method not allowed", { status: 405 });
    } catch (error) {
      const status = error.statusCode || 500;
      const errorMessage = error.message || "Internal Server Error";
      const data = error.additionalProperties || {};

      return new Response(JSON.stringify({ error: errorMessage, data }), {
        status,
      });
    }
  }, "Overrides")
);
