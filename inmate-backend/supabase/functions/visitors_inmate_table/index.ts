// Setup type definitions for built-in Supabase Runtime APIs
import "https://esm.sh/@supabase/functions-js/src/edge-runtime.d.ts";
import {createClient} from "npm:@supabase/supabase-js@2.39.3";

// Initialize Supabase client
const supabase = createClient(Deno.env.get("SUPABASE_URL")!, Deno.env.get("SUPABASE_ANON_KEY")!);

// CORS headers
const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Methods": "POST, GET, OPTIONS",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
  "Access-Control-Max-Age": "86400",
};

Deno.serve(async (req) => {
  if (req.method === "OPTIONS") {
    return withCors(new Response(null, { status: 204 }));
  }

  if (req.method !== "GET") {
    return withCors(new Response("Method Not Allowed", { status: 405 }));
  }

  try {
    const url = new URL(req.url);
    const inmateid = url.searchParams.get("inmateId");
    const keyword = url.searchParams.get("keyword") || "";
    const page = parseInt(url.searchParams.get("page") || "1");
    const pageSize = parseInt(url.searchParams.get("pageSize") || "10"); // Default to 10 per page

    console.log("Received query parameters:", { inmateid, keyword, page, pageSize });

    if (!inmateid) {
      return withCors(
        new Response(
          JSON.stringify({ error: "inmate ID is required" }),
          { status: 400 }
        )
      );
    }
    const { data: inmate_visitor_count } = await supabase
      .from("inmate_visitors")
      .select("*")
      .eq("inmate_id", inmateid)

    const { data: inmate_visitor, error } = await supabase
      .from("inmate_visitors")
      .select("id, inmate_id, visitor_id(id, full_name, national_id), relation")
      .eq("inmate_id", inmateid)
      .range((page - 1) * pageSize, page * pageSize - 1); // Implement pagination

    if (error) {
      console.error("Error fetching inmate_visitor data:", error);
      return withCors(
        new Response(
          JSON.stringify({ error: "Error fetching inmate_visitor data" }),
          { status: 500 }
        )
      );
    }

    if (!inmate_visitor || inmate_visitor.length === 0) {
      console.log("No visitors found for the given inmate ID");
      return withCors(
        new Response(
          JSON.stringify({ message: "No inmate visitors for this inmate found" }),
          { status: 404 }
        )
      );
    }

    // Fetch completed visits and documents for each visitor
    const visitorDataPromises = inmate_visitor.map(async (visitor) => {
      const { count: completedVisitsCount, error: visitError } = await supabase
        .from("visit_requests")
        .select("*", { count: "exact", head: true })
        .eq("inmate_id", inmateid)
        .eq("visitor_id", visitor.visitor_id.id)
        .eq("status_id", "6");

      if (visitError) {
        console.error(
          `Error fetching completed visits for visitor_id ${visitor.visitor_id.id}:`,
          visitError
        );
      }

      const { data: inmateVisitorDocuments, error: documentError } = await supabase
        .from("inmate_visitor_documents")
        .select("*")
        .eq("inmate_visitor_id", visitor.id);

      if (documentError) {
        console.error(
          `Error fetching documents for inmate_visitor_id ${visitor.id}:`,
          documentError
        );
      }
      const VisitorName = visitor.visitor_id.full_name
      return {
        // ...visitor,
        visitor_name : VisitorName,
        personal_id: visitor.visitor_id.national_id,
        relation : visitor.relation,

        completedVisitsCount: completedVisitsCount || 0,
        documents: inmateVisitorDocuments,
      };
    });

    const visitorDataWithDetails = await Promise.all(visitorDataPromises);

    console.log("Fetched visitor data with details:", visitorDataWithDetails);

    // Apply keyword filter if provided (filtering by visitor full name)
    const filteredData = keyword
      ? visitorDataWithDetails.filter((item) =>
        item.visitor_name.toLowerCase().includes(keyword.toLowerCase())
      )
      : visitorDataWithDetails;
    console.log("Filtered data:", inmate_visitor_count);
    const datacount = inmate_visitor_count.length;
    console.log("datacount", inmate_visitor_count.length);
    // Calculate total pages
    const totalPages = Math.ceil(datacount / pageSize);

    return withCors(
      new Response(
        JSON.stringify({
          data: filteredData,
          page,
          pageSize,
          total : datacount,
        }),
        { status: 200 ,
          headers: { "Content-Type": "application/json" } // Explicitly setting Content-Type
        }
      )
    );
  } catch (error) {
    console.error("Unexpected error:", error);
    return withCors(
      new Response(
        JSON.stringify({ error: "Internal Server Error" }),
        { status: 500 }
      )
    );
  }
});

// Helper to add CORS headers
function withCors(response) {
  response.headers.set("Access-Control-Allow-Origin", "*");
  response.headers.set("Access-Control-Allow-Methods", "GET, OPTIONS");
  response.headers.set("Access-Control-Allow-Headers", "Content-Type, Authorization");
  return response;
}


