alter table "auth"."audit_log_entries" alter column "created_at" set data type timestamp(6) with time zone using "created_at"::timestamp(6) with time zone;
alter table "auth"."flow_state" alter column "auth_code_issued_at" set data type timestamp(6) with time zone using "auth_code_issued_at"::timestamp(6) with time zone;
alter table "auth"."flow_state" alter column "created_at" set data type timestamp(6) with time zone using "created_at"::timestamp(6) with time zone;
alter table "auth"."flow_state" alter column "updated_at" set data type timestamp(6) with time zone using "updated_at"::timestamp(6) with time zone;
-- alter table "auth"."mfa_challenges" drop column "web_authn_session_data";
-- alter table "auth"."mfa_factors" drop column "web_authn_aaguid";
-- alter table "auth"."mfa_factors" drop column "web_authn_credential";
alter table "auth"."users" add column "is_active" boolean default true;
-- CREATE UNIQUE INDEX mfa_factors_phone_key ON auth.mfa_factors USING btree (phone);
CREATE UNIQUE INDEX schema_migrations_version_idx ON auth.schema_migrations USING btree (version);
-- CREATE UNIQUE INDEX unique_verified_phone_factor ON auth.mfa_factors USING btree (user_id, phone);
-- alter table "auth"."mfa_factors" add constraint "mfa_factors_phone_key" UNIQUE using index "mfa_factors_phone_key";
set check_function_bodies = off;
CREATE OR REPLACE FUNCTION auth.email()
 RETURNS text
 LANGUAGE sql
 STABLE
AS $function$
  select nullif(current_setting('request.jwt.claim.email', true), '')::text;
$function$;
CREATE OR REPLACE FUNCTION auth.role()
 RETURNS text
 LANGUAGE sql
 STABLE
AS $function$
  select nullif(current_setting('request.jwt.claim.role', true), '')::text;
$function$;
CREATE OR REPLACE FUNCTION auth.uid()
 RETURNS uuid
 LANGUAGE sql
 STABLE
AS $function$
  select nullif(current_setting('request.jwt.claim.sub', true), '')::uuid;
$function$;
-- grant delete on table "auth"."audit_log_entries" to "PUBLIC";
-- grant insert on table "auth"."audit_log_entries" to "PUBLIC";
-- grant select on table "auth"."audit_log_entries" to "PUBLIC";
-- grant update on table "auth"."audit_log_entries" to "PUBLIC";
grant delete on table "auth"."audit_log_entries" to "anon";
grant insert on table "auth"."audit_log_entries" to "anon";
grant references on table "auth"."audit_log_entries" to "anon";
grant select on table "auth"."audit_log_entries" to "anon";
grant trigger on table "auth"."audit_log_entries" to "anon";
grant truncate on table "auth"."audit_log_entries" to "anon";
grant update on table "auth"."audit_log_entries" to "anon";
grant delete on table "auth"."audit_log_entries" to "authenticated";
grant insert on table "auth"."audit_log_entries" to "authenticated";
grant references on table "auth"."audit_log_entries" to "authenticated";
grant select on table "auth"."audit_log_entries" to "authenticated";
grant trigger on table "auth"."audit_log_entries" to "authenticated";
grant truncate on table "auth"."audit_log_entries" to "authenticated";
grant update on table "auth"."audit_log_entries" to "authenticated";
grant delete on table "auth"."audit_log_entries" to "service_role";
grant insert on table "auth"."audit_log_entries" to "service_role";
grant references on table "auth"."audit_log_entries" to "service_role";
grant select on table "auth"."audit_log_entries" to "service_role";
grant trigger on table "auth"."audit_log_entries" to "service_role";
grant truncate on table "auth"."audit_log_entries" to "service_role";
grant update on table "auth"."audit_log_entries" to "service_role";
-- grant delete on table "auth"."flow_state" to "PUBLIC";
-- grant insert on table "auth"."flow_state" to "PUBLIC";
-- grant select on table "auth"."flow_state" to "PUBLIC";
-- grant update on table "auth"."flow_state" to "PUBLIC";
grant delete on table "auth"."flow_state" to "anon";
grant insert on table "auth"."flow_state" to "anon";
grant references on table "auth"."flow_state" to "anon";
grant select on table "auth"."flow_state" to "anon";
grant trigger on table "auth"."flow_state" to "anon";
grant truncate on table "auth"."flow_state" to "anon";
grant update on table "auth"."flow_state" to "anon";
grant delete on table "auth"."flow_state" to "authenticated";
grant insert on table "auth"."flow_state" to "authenticated";
grant references on table "auth"."flow_state" to "authenticated";
grant select on table "auth"."flow_state" to "authenticated";
grant trigger on table "auth"."flow_state" to "authenticated";
grant truncate on table "auth"."flow_state" to "authenticated";
grant update on table "auth"."flow_state" to "authenticated";
grant delete on table "auth"."flow_state" to "service_role";
grant insert on table "auth"."flow_state" to "service_role";
grant references on table "auth"."flow_state" to "service_role";
grant select on table "auth"."flow_state" to "service_role";
grant trigger on table "auth"."flow_state" to "service_role";
grant truncate on table "auth"."flow_state" to "service_role";
grant update on table "auth"."flow_state" to "service_role";
-- grant delete on table "auth"."identities" to "PUBLIC";
-- grant insert on table "auth"."identities" to "PUBLIC";
-- grant select on table "auth"."identities" to "PUBLIC";
-- grant update on table "auth"."identities" to "PUBLIC";
grant delete on table "auth"."identities" to "anon";
grant insert on table "auth"."identities" to "anon";
grant references on table "auth"."identities" to "anon";
grant select on table "auth"."identities" to "anon";
grant trigger on table "auth"."identities" to "anon";
grant truncate on table "auth"."identities" to "anon";
grant update on table "auth"."identities" to "anon";
grant delete on table "auth"."identities" to "authenticated";
grant insert on table "auth"."identities" to "authenticated";
grant references on table "auth"."identities" to "authenticated";
grant select on table "auth"."identities" to "authenticated";
grant trigger on table "auth"."identities" to "authenticated";
grant truncate on table "auth"."identities" to "authenticated";
grant update on table "auth"."identities" to "authenticated";
grant delete on table "auth"."identities" to "service_role";
grant insert on table "auth"."identities" to "service_role";
grant references on table "auth"."identities" to "service_role";
grant select on table "auth"."identities" to "service_role";
grant trigger on table "auth"."identities" to "service_role";
grant truncate on table "auth"."identities" to "service_role";
grant update on table "auth"."identities" to "service_role";
-- grant delete on table "auth"."instances" to "PUBLIC";
-- grant insert on table "auth"."instances" to "PUBLIC";
-- grant select on table "auth"."instances" to "PUBLIC";
-- grant update on table "auth"."instances" to "PUBLIC";
grant delete on table "auth"."instances" to "anon";
grant insert on table "auth"."instances" to "anon";
grant references on table "auth"."instances" to "anon";
grant select on table "auth"."instances" to "anon";
grant trigger on table "auth"."instances" to "anon";
grant truncate on table "auth"."instances" to "anon";
grant update on table "auth"."instances" to "anon";
grant delete on table "auth"."instances" to "authenticated";
grant insert on table "auth"."instances" to "authenticated";
grant references on table "auth"."instances" to "authenticated";
grant select on table "auth"."instances" to "authenticated";
grant trigger on table "auth"."instances" to "authenticated";
grant truncate on table "auth"."instances" to "authenticated";
grant update on table "auth"."instances" to "authenticated";
grant delete on table "auth"."instances" to "service_role";
grant insert on table "auth"."instances" to "service_role";
grant references on table "auth"."instances" to "service_role";
grant select on table "auth"."instances" to "service_role";
grant trigger on table "auth"."instances" to "service_role";
grant truncate on table "auth"."instances" to "service_role";
grant update on table "auth"."instances" to "service_role";
-- grant delete on table "auth"."mfa_amr_claims" to "PUBLIC";
-- grant insert on table "auth"."mfa_amr_claims" to "PUBLIC";
-- grant select on table "auth"."mfa_amr_claims" to "PUBLIC";
-- grant update on table "auth"."mfa_amr_claims" to "PUBLIC";
grant delete on table "auth"."mfa_amr_claims" to "anon";
grant insert on table "auth"."mfa_amr_claims" to "anon";
grant references on table "auth"."mfa_amr_claims" to "anon";
grant select on table "auth"."mfa_amr_claims" to "anon";
grant trigger on table "auth"."mfa_amr_claims" to "anon";
grant truncate on table "auth"."mfa_amr_claims" to "anon";
grant update on table "auth"."mfa_amr_claims" to "anon";
grant delete on table "auth"."mfa_amr_claims" to "authenticated";
grant insert on table "auth"."mfa_amr_claims" to "authenticated";
grant references on table "auth"."mfa_amr_claims" to "authenticated";
grant select on table "auth"."mfa_amr_claims" to "authenticated";
grant trigger on table "auth"."mfa_amr_claims" to "authenticated";
grant truncate on table "auth"."mfa_amr_claims" to "authenticated";
grant update on table "auth"."mfa_amr_claims" to "authenticated";
grant delete on table "auth"."mfa_amr_claims" to "service_role";
grant insert on table "auth"."mfa_amr_claims" to "service_role";
grant references on table "auth"."mfa_amr_claims" to "service_role";
grant select on table "auth"."mfa_amr_claims" to "service_role";
grant trigger on table "auth"."mfa_amr_claims" to "service_role";
grant truncate on table "auth"."mfa_amr_claims" to "service_role";
grant update on table "auth"."mfa_amr_claims" to "service_role";
-- grant delete on table "auth"."mfa_challenges" to "PUBLIC";
-- grant insert on table "auth"."mfa_challenges" to "PUBLIC";
-- grant select on table "auth"."mfa_challenges" to "PUBLIC";
-- grant update on table "auth"."mfa_challenges" to "PUBLIC";
grant delete on table "auth"."mfa_challenges" to "anon";
grant insert on table "auth"."mfa_challenges" to "anon";
grant references on table "auth"."mfa_challenges" to "anon";
grant select on table "auth"."mfa_challenges" to "anon";
grant trigger on table "auth"."mfa_challenges" to "anon";
grant truncate on table "auth"."mfa_challenges" to "anon";
grant update on table "auth"."mfa_challenges" to "anon";
grant delete on table "auth"."mfa_challenges" to "authenticated";
grant insert on table "auth"."mfa_challenges" to "authenticated";
grant references on table "auth"."mfa_challenges" to "authenticated";
grant select on table "auth"."mfa_challenges" to "authenticated";
grant trigger on table "auth"."mfa_challenges" to "authenticated";
grant truncate on table "auth"."mfa_challenges" to "authenticated";
grant update on table "auth"."mfa_challenges" to "authenticated";
grant delete on table "auth"."mfa_challenges" to "service_role";
grant insert on table "auth"."mfa_challenges" to "service_role";
grant references on table "auth"."mfa_challenges" to "service_role";
grant select on table "auth"."mfa_challenges" to "service_role";
grant trigger on table "auth"."mfa_challenges" to "service_role";
grant truncate on table "auth"."mfa_challenges" to "service_role";
grant update on table "auth"."mfa_challenges" to "service_role";
-- grant delete on table "auth"."mfa_factors" to "PUBLIC";
-- grant insert on table "auth"."mfa_factors" to "PUBLIC";
-- grant select on table "auth"."mfa_factors" to "PUBLIC";
-- grant update on table "auth"."mfa_factors" to "PUBLIC";
grant delete on table "auth"."mfa_factors" to "anon";
grant insert on table "auth"."mfa_factors" to "anon";
grant references on table "auth"."mfa_factors" to "anon";
grant select on table "auth"."mfa_factors" to "anon";
grant trigger on table "auth"."mfa_factors" to "anon";
grant truncate on table "auth"."mfa_factors" to "anon";
grant update on table "auth"."mfa_factors" to "anon";
grant delete on table "auth"."mfa_factors" to "authenticated";
grant insert on table "auth"."mfa_factors" to "authenticated";
grant references on table "auth"."mfa_factors" to "authenticated";
grant select on table "auth"."mfa_factors" to "authenticated";
grant trigger on table "auth"."mfa_factors" to "authenticated";
grant truncate on table "auth"."mfa_factors" to "authenticated";
grant update on table "auth"."mfa_factors" to "authenticated";
grant delete on table "auth"."mfa_factors" to "service_role";
grant insert on table "auth"."mfa_factors" to "service_role";
grant references on table "auth"."mfa_factors" to "service_role";
grant select on table "auth"."mfa_factors" to "service_role";
grant trigger on table "auth"."mfa_factors" to "service_role";
grant truncate on table "auth"."mfa_factors" to "service_role";
grant update on table "auth"."mfa_factors" to "service_role";
-- grant delete on table "auth"."one_time_tokens" to "PUBLIC";
-- grant insert on table "auth"."one_time_tokens" to "PUBLIC";
-- grant select on table "auth"."one_time_tokens" to "PUBLIC";
-- grant update on table "auth"."one_time_tokens" to "PUBLIC";
-- grant delete on table "auth"."refresh_tokens" to "PUBLIC";
-- grant insert on table "auth"."refresh_tokens" to "PUBLIC";
-- grant select on table "auth"."refresh_tokens" to "PUBLIC";
-- grant update on table "auth"."refresh_tokens" to "PUBLIC";
-- grant delete on table "auth"."saml_providers" to "PUBLIC";
-- grant insert on table "auth"."saml_providers" to "PUBLIC";
-- grant select on table "auth"."saml_providers" to "PUBLIC";
-- grant update on table "auth"."saml_providers" to "PUBLIC";
-- grant delete on table "auth"."saml_relay_states" to "PUBLIC";
-- grant insert on table "auth"."saml_relay_states" to "PUBLIC";
-- grant select on table "auth"."saml_relay_states" to "PUBLIC";
-- grant update on table "auth"."saml_relay_states" to "PUBLIC";
-- grant delete on table "auth"."schema_migrations" to "PUBLIC";
-- grant insert on table "auth"."schema_migrations" to "PUBLIC";
-- grant select on table "auth"."schema_migrations" to "PUBLIC";
-- grant update on table "auth"."schema_migrations" to "PUBLIC";
-- grant delete on table "auth"."sessions" to "PUBLIC";
-- grant insert on table "auth"."sessions" to "PUBLIC";
-- grant select on table "auth"."sessions" to "PUBLIC";
-- grant update on table "auth"."sessions" to "PUBLIC";
-- grant delete on table "auth"."sso_domains" to "PUBLIC";
-- grant insert on table "auth"."sso_domains" to "PUBLIC";
-- grant select on table "auth"."sso_domains" to "PUBLIC";
-- grant update on table "auth"."sso_domains" to "PUBLIC";
-- grant delete on table "auth"."sso_providers" to "PUBLIC";
-- grant insert on table "auth"."sso_providers" to "PUBLIC";
-- grant select on table "auth"."sso_providers" to "PUBLIC";
-- grant update on table "auth"."sso_providers" to "PUBLIC";
-- grant delete on table "auth"."users" to "PUBLIC";
-- grant insert on table "auth"."users" to "PUBLIC";
-- grant select on table "auth"."users" to "PUBLIC";
-- grant update on table "auth"."users" to "PUBLIC";
grant delete on table "auth"."users" to "anon";
grant insert on table "auth"."users" to "anon";
grant update on table "auth"."users" to "anon";
create policy "Allow only active users to log in"
on "auth"."users"
as permissive
for select
-- to public
using ((is_active = true));
create policy "Allow only active users"
on "auth"."users"
as permissive
for select
-- to public
using ((is_active = true));
CREATE TRIGGER on_auth_user_created AFTER INSERT ON auth.users FOR EACH ROW EXECUTE FUNCTION user_trigger_function();
-- CREATE TRIGGER trigger_revoke_sessions AFTER UPDATE OF is_active ON auth.users FOR EACH ROW WHEN ((new.is_active = false)) EXECUTE FUNCTION revoke_user_sessions();
-- CREATE TRIGGER user_delete_trigger AFTER DELETE ON auth.users FOR EACH ROW EXECUTE FUNCTION delete_user_from_public();
drop trigger if exists "objects_delete_delete_prefix" on "storage"."objects";
drop trigger if exists "objects_insert_create_prefix" on "storage"."objects";
drop trigger if exists "objects_update_create_prefix" on "storage"."objects";
-- drop trigger if exists "prefixes_create_hierarchy" on "storage"."prefixes";
-- drop trigger if exists "prefixes_delete_hierarchy" on "storage"."prefixes";
-- revoke delete on table "storage"."prefixes" from "anon";
-- revoke insert on table "storage"."prefixes" from "anon";
-- revoke references on table "storage"."prefixes" from "anon";
-- revoke select on table "storage"."prefixes" from "anon";
-- revoke trigger on table "storage"."prefixes" from "anon";
-- revoke truncate on table "storage"."prefixes" from "anon";
-- revoke update on table "storage"."prefixes" from "anon";
-- revoke delete on table "storage"."prefixes" from "authenticated";
-- revoke insert on table "storage"."prefixes" from "authenticated";
-- revoke references on table "storage"."prefixes" from "authenticated";
-- revoke select on table "storage"."prefixes" from "authenticated";
-- revoke trigger on table "storage"."prefixes" from "authenticated";
-- revoke truncate on table "storage"."prefixes" from "authenticated";
-- revoke update on table "storage"."prefixes" from "authenticated";
-- revoke delete on table "storage"."prefixes" from "service_role";
-- revoke insert on table "storage"."prefixes" from "service_role";
-- revoke references on table "storage"."prefixes" from "service_role";
-- revoke select on table "storage"."prefixes" from "service_role";
-- revoke trigger on table "storage"."prefixes" from "service_role";
-- revoke truncate on table "storage"."prefixes" from "service_role";
-- revoke update on table "storage"."prefixes" from "service_role";
-- alter table "storage"."prefixes" drop constraint "prefixes_bucketId_fkey";
drop function if exists "storage"."add_prefixes"(_bucket_id text, _name text);
drop function if exists "storage"."delete_prefix"(_bucket_id text, _name text);
drop function if exists "storage"."delete_prefix_hierarchy_trigger"();
drop function if exists "storage"."get_level"(name text);
drop function if exists "storage"."get_prefix"(name text);
drop function if exists "storage"."get_prefixes"(name text);
drop function if exists "storage"."objects_insert_prefix_trigger"();
drop function if exists "storage"."objects_update_prefix_trigger"();
drop function if exists "storage"."prefixes_insert_trigger"();
drop function if exists "storage"."search_legacy_v1"(prefix text, bucketname text, limits integer, levels integer, offsets integer, search text, sortcolumn text, sortorder text);
drop function if exists "storage"."search_v1_optimised"(prefix text, bucketname text, limits integer, levels integer, offsets integer, search text, sortcolumn text, sortorder text);
drop function if exists "storage"."search_v2"(prefix text, bucket_name text, limits integer, levels integer, start_after text);
-- alter table "storage"."prefixes" drop constraint "prefixes_pkey";
drop index if exists "storage"."idx_name_bucket_level_unique";
drop index if exists "storage"."idx_objects_lower_name";
drop index if exists "storage"."idx_prefixes_lower_name";
drop index if exists "storage"."objects_bucket_id_level_idx";
drop index if exists "storage"."prefixes_pkey";
-- drop table "storage"."prefixes";
-- alter table "storage"."objects" drop column "level";
alter table "storage"."objects" drop column "user_metadata";
set check_function_bodies = off;
CREATE OR REPLACE FUNCTION storage.extension(name text)
 RETURNS text
 LANGUAGE plpgsql
AS $function$
DECLARE
_parts text[];
_filename text;
BEGIN
	select string_to_array(name, '/') into _parts;
	select _parts[array_length(_parts,1)] into _filename;
	-- @todo return the last part instead of 2
	return reverse(split_part(reverse(_filename), '.', 1));
END
$function$;
CREATE OR REPLACE FUNCTION storage.foldername(name text)
 RETURNS text[]
 LANGUAGE plpgsql
AS $function$
DECLARE
_parts text[];
BEGIN
	select string_to_array(name, '/') into _parts;
	return _parts[1:array_length(_parts,1)-1];
END
$function$;
CREATE OR REPLACE FUNCTION storage.get_size_by_bucket()
 RETURNS TABLE(size bigint, bucket_id text)
 LANGUAGE plpgsql
AS $function$
BEGIN
    return query
        select sum((metadata->>'size')::int) as size, obj.bucket_id
        from "storage".objects as obj
        group by obj.bucket_id;
END
$function$;
CREATE OR REPLACE FUNCTION storage.search(prefix text, bucketname text, limits integer DEFAULT 100, levels integer DEFAULT 1, offsets integer DEFAULT 0, search text DEFAULT ''::text, sortcolumn text DEFAULT 'name'::text, sortorder text DEFAULT 'asc'::text)
 RETURNS TABLE(name text, id uuid, updated_at timestamp with time zone, created_at timestamp with time zone, last_accessed_at timestamp with time zone, metadata jsonb)
 LANGUAGE plpgsql
 STABLE
AS $function$
declare
  v_order_by text;
  v_sort_order text;
begin
  case
    when sortcolumn = 'name' then
      v_order_by = 'name';
    when sortcolumn = 'updated_at' then
      v_order_by = 'updated_at';
    when sortcolumn = 'created_at' then
      v_order_by = 'created_at';
    when sortcolumn = 'last_accessed_at' then
      v_order_by = 'last_accessed_at';
    else
      v_order_by = 'name';
  end case;

  case
    when sortorder = 'asc' then
      v_sort_order = 'asc';
    when sortorder = 'desc' then
      v_sort_order = 'desc';
    else
      v_sort_order = 'asc';
  end case;

  v_order_by = v_order_by || ' ' || v_sort_order;

  return query execute
    'with folders as (
       select path_tokens[$1] as folder
       from storage.objects
         where objects.name ilike $2 || $3 || ''%''
           and bucket_id = $4
           and array_length(regexp_split_to_array(objects.name, ''/''), 1) <> $1
       group by folder
       order by folder ' || v_sort_order || '
     )
     (select folder as "name",
            null as id,
            null as updated_at,
            null as created_at,
            null as last_accessed_at,
            null as metadata from folders)
     union all
     (select path_tokens[$1] as "name",
            id,
            updated_at,
            created_at,
            last_accessed_at,
            metadata
     from storage.objects
     where objects.name ilike $2 || $3 || ''%''
       and bucket_id = $4
       and array_length(regexp_split_to_array(objects.name, ''/''), 1) = $1
     order by ' || v_order_by || ')
     limit $5
     offset $6' using levels, prefix, search, bucketname, limits, offsets;
end;
$function$;
grant delete on table "storage"."s3_multipart_uploads" to "anon";
grant insert on table "storage"."s3_multipart_uploads" to "anon";
grant references on table "storage"."s3_multipart_uploads" to "anon";
grant trigger on table "storage"."s3_multipart_uploads" to "anon";
grant truncate on table "storage"."s3_multipart_uploads" to "anon";
grant update on table "storage"."s3_multipart_uploads" to "anon";
grant delete on table "storage"."s3_multipart_uploads" to "authenticated";
grant insert on table "storage"."s3_multipart_uploads" to "authenticated";
grant references on table "storage"."s3_multipart_uploads" to "authenticated";
grant trigger on table "storage"."s3_multipart_uploads" to "authenticated";
grant truncate on table "storage"."s3_multipart_uploads" to "authenticated";
grant update on table "storage"."s3_multipart_uploads" to "authenticated";
grant delete on table "storage"."s3_multipart_uploads_parts" to "anon";
grant insert on table "storage"."s3_multipart_uploads_parts" to "anon";
grant references on table "storage"."s3_multipart_uploads_parts" to "anon";
grant trigger on table "storage"."s3_multipart_uploads_parts" to "anon";
grant truncate on table "storage"."s3_multipart_uploads_parts" to "anon";
grant update on table "storage"."s3_multipart_uploads_parts" to "anon";
grant delete on table "storage"."s3_multipart_uploads_parts" to "authenticated";
grant insert on table "storage"."s3_multipart_uploads_parts" to "authenticated";
grant references on table "storage"."s3_multipart_uploads_parts" to "authenticated";
grant trigger on table "storage"."s3_multipart_uploads_parts" to "authenticated";
grant truncate on table "storage"."s3_multipart_uploads_parts" to "authenticated";
grant update on table "storage"."s3_multipart_uploads_parts" to "authenticated";
create policy "allow all 1j1tb37_0"
on "storage"."objects"
as permissive
for insert
-- to public
with check ((bucket_id = 'inmate_documents'::text));
create policy "allow all 1j1tb37_1"
on "storage"."objects"
as permissive
for update
-- to public
using ((bucket_id = 'inmate_documents'::text));
create policy "allow all 1j1tb37_2"
on "storage"."objects"
as permissive
for delete
-- to public
using ((bucket_id = 'inmate_documents'::text));
create policy "allow all 1j1tb37_3"
on "storage"."objects"
as permissive
for select
-- to public
using ((bucket_id = 'inmate_documents'::text));
