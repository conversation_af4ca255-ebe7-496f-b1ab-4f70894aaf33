drop policy "Allow only active users to log in" on "auth"."users";

drop policy "Allow only active users" on "auth"."users";

alter table "auth"."audit_log_entries" alter column "created_at" set data type timestamp with time zone using "created_at"::timestamp with time zone;

alter table "auth"."flow_state" alter column "auth_code_issued_at" set data type timestamp with time zone using "auth_code_issued_at"::timestamp with time zone;

alter table "auth"."flow_state" alter column "created_at" set data type timestamp with time zone using "created_at"::timestamp with time zone;

alter table "auth"."flow_state" alter column "updated_at" set data type timestamp with time zone using "updated_at"::timestamp with time zone;

alter table "auth"."mfa_challenges" add column "web_authn_session_data" jsonb;

alter table "auth"."mfa_factors" add column "web_authn_aaguid" uuid;

alter table "auth"."mfa_factors" add column "web_authn_credential" jsonb;

-- alter table "auth"."users" drop column "is_active";

alter table "auth"."users" alter column "banned_until" set data type timestamp without time zone using "banned_until"::timestamp without time zone;

CREATE UNIQUE INDEX unique_phone_factor_per_user ON auth.mfa_factors USING btree (user_id, phone);

set check_function_bodies = off;

CREATE OR REPLACE FUNCTION auth.email()
 RETURNS text
 LANGUAGE sql
 STABLE
AS $function$
  select 
  coalesce(
    nullif(current_setting('request.jwt.claim.email', true), ''),
    (nullif(current_setting('request.jwt.claims', true), '')::jsonb ->> 'email')
  )::text
$function$
;

CREATE OR REPLACE FUNCTION auth.role()
 RETURNS text
 LANGUAGE sql
 STABLE
AS $function$
  select 
  coalesce(
    nullif(current_setting('request.jwt.claim.role', true), ''),
    (nullif(current_setting('request.jwt.claims', true), '')::jsonb ->> 'role')
  )::text
$function$
;

CREATE OR REPLACE FUNCTION auth.uid()
 RETURNS uuid
 LANGUAGE sql
 STABLE
AS $function$
  select 
  coalesce(
    nullif(current_setting('request.jwt.claim.sub', true), ''),
    (nullif(current_setting('request.jwt.claims', true), '')::jsonb ->> 'sub')
  )::uuid
$function$
;

grant delete on table "auth"."audit_log_entries" to PUBLIC;

grant insert on table "auth"."audit_log_entries" to PUBLIC;

grant select on table "auth"."audit_log_entries" to PUBLIC;

grant update on table "auth"."audit_log_entries" to PUBLIC;

grant delete on table "auth"."flow_state" to PUBLIC;

grant insert on table "auth"."flow_state" to PUBLIC;

grant select on table "auth"."flow_state" to PUBLIC;

grant update on table "auth"."flow_state" to PUBLIC;

grant delete on table "auth"."identities" to PUBLIC;

grant insert on table "auth"."identities" to PUBLIC;

grant select on table "auth"."identities" to PUBLIC;

grant update on table "auth"."identities" to PUBLIC;

grant delete on table "auth"."instances" to PUBLIC;

grant insert on table "auth"."instances" to PUBLIC;

grant select on table "auth"."instances" to PUBLIC;

grant update on table "auth"."instances" to PUBLIC;

grant delete on table "auth"."mfa_amr_claims" to PUBLIC;

grant insert on table "auth"."mfa_amr_claims" to PUBLIC;

grant select on table "auth"."mfa_amr_claims" to PUBLIC;

grant update on table "auth"."mfa_amr_claims" to PUBLIC;

grant delete on table "auth"."mfa_challenges" to PUBLIC;

grant insert on table "auth"."mfa_challenges" to PUBLIC;

grant select on table "auth"."mfa_challenges" to PUBLIC;

grant update on table "auth"."mfa_challenges" to PUBLIC;

grant delete on table "auth"."mfa_factors" to PUBLIC;

grant insert on table "auth"."mfa_factors" to PUBLIC;

grant select on table "auth"."mfa_factors" to PUBLIC;

grant update on table "auth"."mfa_factors" to PUBLIC;

grant delete on table "auth"."one_time_tokens" to PUBLIC;

grant insert on table "auth"."one_time_tokens" to PUBLIC;

grant select on table "auth"."one_time_tokens" to PUBLIC;

grant update on table "auth"."one_time_tokens" to PUBLIC;

grant delete on table "auth"."refresh_tokens" to PUBLIC;

grant insert on table "auth"."refresh_tokens" to PUBLIC;

grant select on table "auth"."refresh_tokens" to PUBLIC;

grant update on table "auth"."refresh_tokens" to PUBLIC;

grant delete on table "auth"."saml_providers" to PUBLIC;

grant insert on table "auth"."saml_providers" to PUBLIC;

grant select on table "auth"."saml_providers" to PUBLIC;

grant update on table "auth"."saml_providers" to PUBLIC;

grant delete on table "auth"."saml_relay_states" to PUBLIC;

grant insert on table "auth"."saml_relay_states" to PUBLIC;

grant select on table "auth"."saml_relay_states" to PUBLIC;

grant update on table "auth"."saml_relay_states" to PUBLIC;

grant delete on table "auth"."schema_migrations" to PUBLIC;

grant insert on table "auth"."schema_migrations" to PUBLIC;

grant select on table "auth"."schema_migrations" to PUBLIC;

grant update on table "auth"."schema_migrations" to PUBLIC;

grant delete on table "auth"."sessions" to PUBLIC;

grant insert on table "auth"."sessions" to PUBLIC;

grant select on table "auth"."sessions" to PUBLIC;

grant update on table "auth"."sessions" to PUBLIC;

grant delete on table "auth"."sso_domains" to PUBLIC;

grant insert on table "auth"."sso_domains" to PUBLIC;

grant select on table "auth"."sso_domains" to PUBLIC;

grant update on table "auth"."sso_domains" to PUBLIC;

grant delete on table "auth"."sso_providers" to PUBLIC;

grant insert on table "auth"."sso_providers" to PUBLIC;

grant select on table "auth"."sso_providers" to PUBLIC;

grant update on table "auth"."sso_providers" to PUBLIC;

grant delete on table "auth"."users" to PUBLIC;

grant insert on table "auth"."users" to PUBLIC;

grant select on table "auth"."users" to PUBLIC;

grant update on table "auth"."users" to PUBLIC;

CREATE TRIGGER user_delete_trigger AFTER DELETE ON auth.users FOR EACH ROW EXECUTE FUNCTION delete_user_from_public();


