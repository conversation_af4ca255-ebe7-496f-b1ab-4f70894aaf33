drop function if exists "public"."get_visitor_requests_with_meeting_records_not_archived"();

drop function if exists "public"."get_visitor_visit_requests_with_meeting_records_not_archived"();

create table "public"."overrides" (
    "id" uuid not null default gen_random_uuid(),
    "room_id" text not null,
    "date" date not null
);


create table "public"."overrides_timeslots" (
    "id" uuid not null default gen_random_uuid(),
    "override_id" uuid not null,
    "starttime" time(6) without time zone not null,
    "endtime" time(6) without time zone not null
);


create table "public"."section_overrides" (
    "id" uuid not null default gen_random_uuid(),
    "section_id" text not null,
    "date" date not null
);


create table "public"."section_overrides_timeslots" (
    "id" uuid not null default gen_random_uuid(),
    "override_id" uuid not null,
    "starttime" time(6) without time zone not null,
    "endtime" time(6) without time zone not null
);


CREATE UNIQUE INDEX overrides_pkey ON public.overrides USING btree (id);

CREATE UNIQUE INDEX overrides_timeslots_pkey ON public.overrides_timeslots USING btree (id);

CREATE UNIQUE INDEX section_overrides_pkey ON public.section_overrides USING btree (id);

CREATE UNIQUE INDEX section_overrides_timeslots_pkey ON public.section_overrides_timeslots USING btree (id);

alter table "public"."overrides" add constraint "overrides_pkey" PRIMARY KEY using index "overrides_pkey";

alter table "public"."overrides_timeslots" add constraint "overrides_timeslots_pkey" PRIMARY KEY using index "overrides_timeslots_pkey";

alter table "public"."section_overrides" add constraint "section_overrides_pkey" PRIMARY KEY using index "section_overrides_pkey";

alter table "public"."section_overrides_timeslots" add constraint "section_overrides_timeslots_pkey" PRIMARY KEY using index "section_overrides_timeslots_pkey";

alter table "public"."overrides" add constraint "fk_room" FOREIGN KEY (room_id) REFERENCES rooms(id) ON DELETE RESTRICT not valid;

alter table "public"."overrides" validate constraint "fk_room";

alter table "public"."overrides_timeslots" add constraint "fk_override" FOREIGN KEY (override_id) REFERENCES overrides(id) ON DELETE CASCADE not valid;

alter table "public"."overrides_timeslots" validate constraint "fk_override";

alter table "public"."section_overrides" add constraint "fk_section" FOREIGN KEY (section_id) REFERENCES sections(id) ON DELETE RESTRICT not valid;

alter table "public"."section_overrides" validate constraint "fk_section";

alter table "public"."section_overrides_timeslots" add constraint "fk_section_override" FOREIGN KEY (override_id) REFERENCES section_overrides(id) ON DELETE CASCADE not valid;

alter table "public"."section_overrides_timeslots" validate constraint "fk_section_override";

set check_function_bodies = off;

create or replace view "public"."visit_requests_view" as  SELECT vr.id,
    vr.visit_number,
    vr.datetime,
    vr.duration,
    vr.actual_duration,
    vr.started_at,
    vr.ended_at,
    vr."from",
    vr."to",
    vr.title,
    vr.reason,
    vr.status_id,
    s.name AS status_name,
    vr.visitor_id,
    v.full_name AS visitor_name,
    vr.inmate_id,
    i.full_name AS inmate_name,
    vr.requested_inmate_name,
    vr.user_id,
    u.full_name AS user_name,
    vr.room_id,
    r.name AS room_name,
    vr.meeting_url,
    vr.archived,
    vr.deleted_at,
    vr.created_at,
    vr.updated_at,
    vr.first_response_time
   FROM (((((visit_requests vr
     LEFT JOIN visitors v ON ((v.id = vr.visitor_id)))
     LEFT JOIN inmates i ON ((i.id = vr.inmate_id)))
     LEFT JOIN visit_requests_status s ON ((s.id = vr.status_id)))
     LEFT JOIN users u ON ((u.user_auth_id = vr.user_id)))
     LEFT JOIN rooms r ON ((r.id = vr.room_id)));


CREATE OR REPLACE FUNCTION public.get_visitor_requests_with_meeting_records_not_archived()
 RETURNS TABLE(request_id text, request_status text, request_number text, inmate_full_name text, inmate_id text, visitor_name text, request_date timestamp without time zone, "from" time without time zone, "to" time without time zone, relation text, meeting_status text, meeting_date_time timestamp without time zone, record_id text, recording_url text, record_duration integer, archived boolean, record_size integer, feedback_rate integer, feedback_comment text)
 LANGUAGE plpgsql
AS $function$
BEGIN
  RETURN QUERY
  SELECT DISTINCT ON (mrec.id)
    vr.id,
    vr.status_id::TEXT,
    vr.visit_number::TEXT,
    inm.full_name AS inmate_full_name,
    vr.inmate_id,
    vis.full_name AS visitor_name,
    vr.datetime::timestamp WITHOUT TIME ZONE AS request_date,
    vr."from"::time WITHOUT TIME ZONE AS "from",
    vr."to"::time WITHOUT TIME ZONE AS "to",
    iv.relation::TEXT,
    vr.status_id::TEXT AS meeting_status,
    vr.datetime::timestamp WITHOUT TIME ZONE AS meeting_date_time,
    mrec.id,
    mrec.recording_url,
    mrec.duration AS record_duration,
    mrec.archived,
    mrec.record_size,
    feedback.rate,
    feedback.comment
  FROM public.visit_requests vr
    LEFT JOIN public.inmate_visitors iv
      ON vr.inmate_id = iv.inmate_id AND vr.visitor_id = iv.visitor_id
    LEFT JOIN public.inmates inm
      ON inm.id = vr.inmate_id
    JOIN public.visitors vis
      ON vis.id = vr.visitor_id
    JOIN public.meeting_records mrec
      ON mrec.visit_request_id = vr.id
    LEFT JOIN public.meeting_feedbacks feedback
      ON mrec.visit_request_id = feedback.visit_request_id
  WHERE mrec.deleted_at IS NULL
    AND mrec.archived = FALSE;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.get_visitor_visit_requests_with_meeting_records_not_archived()
 RETURNS TABLE(request_id text, request_status text, request_number text, inmate_full_name text, inmate_id text, visitor_name text, request_date timestamp without time zone, "from" time without time zone, "to" time without time zone, relation text, meeting_status text, meeting_date_time timestamp without time zone, record_id text, recording_url text, record_duration integer, archived boolean, record_size integer, feedback_rate integer, feedback_comment text)
 LANGUAGE plpgsql
AS $function$
BEGIN
  RETURN QUERY
  SELECT DISTINCT ON (mrec.id)
      vr.id::text,
      vr.status_id::text,
      vr.visit_number::text,
      inm.full_name,
      vr.inmate_id::text AS inmate_full_name,
      vis.full_name AS visitor_name,
      vr.datetime::timestamp without time zone AS request_date,  -- Explicit cast (optional)
      vr."from"::time AS "from",
      vr."to"::time AS "to",
      iv.relation::text,
      vr.status_id::text AS meeting_status,
      vr.datetime::timestamp without time zone AS meeting_date_time,  -- Explicit cast (optional)
      mrec.id::text,
      mrec.recording_url,
      mrec.duration AS record_duration,
      mrec.archived,
      mrec.record_size,
      feedback.rate,
      feedback.comment
  FROM public.visit_requests vr
      LEFT JOIN public.inmate_visitors iv
        ON vr.inmate_id = iv.inmate_id AND vr.visitor_id = iv.visitor_id
      LEFT JOIN public.inmates inm
        ON inm.id = vr.inmate_id
      JOIN public.visitors vis
        ON vis.id = vr.visitor_id
      JOIN public.meeting_records mrec
        ON mrec.visit_request_id = vr.id
      LEFT JOIN public.meeting_feedbacks feedback
        ON mrec.visit_request_id = feedback.visit_request_id
  WHERE mrec.deleted_at IS NULL
    AND mrec.archived = FALSE;
END;
$function$
;

grant delete on table "public"."overrides" to "anon";

grant insert on table "public"."overrides" to "anon";

grant references on table "public"."overrides" to "anon";

grant select on table "public"."overrides" to "anon";

grant trigger on table "public"."overrides" to "anon";

grant truncate on table "public"."overrides" to "anon";

grant update on table "public"."overrides" to "anon";

grant delete on table "public"."overrides" to "authenticated";

grant insert on table "public"."overrides" to "authenticated";

grant references on table "public"."overrides" to "authenticated";

grant select on table "public"."overrides" to "authenticated";

grant trigger on table "public"."overrides" to "authenticated";

grant truncate on table "public"."overrides" to "authenticated";

grant update on table "public"."overrides" to "authenticated";

grant delete on table "public"."overrides" to "postgres";

grant insert on table "public"."overrides" to "postgres";

grant references on table "public"."overrides" to "postgres";

grant select on table "public"."overrides" to "postgres";

grant trigger on table "public"."overrides" to "postgres";

grant truncate on table "public"."overrides" to "postgres";

grant update on table "public"."overrides" to "postgres";

grant delete on table "public"."overrides" to "service_role";

grant insert on table "public"."overrides" to "service_role";

grant references on table "public"."overrides" to "service_role";

grant select on table "public"."overrides" to "service_role";

grant trigger on table "public"."overrides" to "service_role";

grant truncate on table "public"."overrides" to "service_role";

grant update on table "public"."overrides" to "service_role";

grant delete on table "public"."overrides_timeslots" to "anon";

grant insert on table "public"."overrides_timeslots" to "anon";

grant references on table "public"."overrides_timeslots" to "anon";

grant select on table "public"."overrides_timeslots" to "anon";

grant trigger on table "public"."overrides_timeslots" to "anon";

grant truncate on table "public"."overrides_timeslots" to "anon";

grant update on table "public"."overrides_timeslots" to "anon";

grant delete on table "public"."overrides_timeslots" to "authenticated";

grant insert on table "public"."overrides_timeslots" to "authenticated";

grant references on table "public"."overrides_timeslots" to "authenticated";

grant select on table "public"."overrides_timeslots" to "authenticated";

grant trigger on table "public"."overrides_timeslots" to "authenticated";

grant truncate on table "public"."overrides_timeslots" to "authenticated";

grant update on table "public"."overrides_timeslots" to "authenticated";

grant delete on table "public"."overrides_timeslots" to "postgres";

grant insert on table "public"."overrides_timeslots" to "postgres";

grant references on table "public"."overrides_timeslots" to "postgres";

grant select on table "public"."overrides_timeslots" to "postgres";

grant trigger on table "public"."overrides_timeslots" to "postgres";

grant truncate on table "public"."overrides_timeslots" to "postgres";

grant update on table "public"."overrides_timeslots" to "postgres";

grant delete on table "public"."overrides_timeslots" to "service_role";

grant insert on table "public"."overrides_timeslots" to "service_role";

grant references on table "public"."overrides_timeslots" to "service_role";

grant select on table "public"."overrides_timeslots" to "service_role";

grant trigger on table "public"."overrides_timeslots" to "service_role";

grant truncate on table "public"."overrides_timeslots" to "service_role";

grant update on table "public"."overrides_timeslots" to "service_role";

grant delete on table "public"."section_overrides" to "anon";

grant insert on table "public"."section_overrides" to "anon";

grant references on table "public"."section_overrides" to "anon";

grant select on table "public"."section_overrides" to "anon";

grant trigger on table "public"."section_overrides" to "anon";

grant truncate on table "public"."section_overrides" to "anon";

grant update on table "public"."section_overrides" to "anon";

grant delete on table "public"."section_overrides" to "authenticated";

grant insert on table "public"."section_overrides" to "authenticated";

grant references on table "public"."section_overrides" to "authenticated";

grant select on table "public"."section_overrides" to "authenticated";

grant trigger on table "public"."section_overrides" to "authenticated";

grant truncate on table "public"."section_overrides" to "authenticated";

grant update on table "public"."section_overrides" to "authenticated";

grant delete on table "public"."section_overrides" to "postgres";

grant insert on table "public"."section_overrides" to "postgres";

grant references on table "public"."section_overrides" to "postgres";

grant select on table "public"."section_overrides" to "postgres";

grant trigger on table "public"."section_overrides" to "postgres";

grant truncate on table "public"."section_overrides" to "postgres";

grant update on table "public"."section_overrides" to "postgres";

grant delete on table "public"."section_overrides" to "service_role";

grant insert on table "public"."section_overrides" to "service_role";

grant references on table "public"."section_overrides" to "service_role";

grant select on table "public"."section_overrides" to "service_role";

grant trigger on table "public"."section_overrides" to "service_role";

grant truncate on table "public"."section_overrides" to "service_role";

grant update on table "public"."section_overrides" to "service_role";

grant delete on table "public"."section_overrides_timeslots" to "anon";

grant insert on table "public"."section_overrides_timeslots" to "anon";

grant references on table "public"."section_overrides_timeslots" to "anon";

grant select on table "public"."section_overrides_timeslots" to "anon";

grant trigger on table "public"."section_overrides_timeslots" to "anon";

grant truncate on table "public"."section_overrides_timeslots" to "anon";

grant update on table "public"."section_overrides_timeslots" to "anon";

grant delete on table "public"."section_overrides_timeslots" to "authenticated";

grant insert on table "public"."section_overrides_timeslots" to "authenticated";

grant references on table "public"."section_overrides_timeslots" to "authenticated";

grant select on table "public"."section_overrides_timeslots" to "authenticated";

grant trigger on table "public"."section_overrides_timeslots" to "authenticated";

grant truncate on table "public"."section_overrides_timeslots" to "authenticated";

grant update on table "public"."section_overrides_timeslots" to "authenticated";

grant delete on table "public"."section_overrides_timeslots" to "postgres";

grant insert on table "public"."section_overrides_timeslots" to "postgres";

grant references on table "public"."section_overrides_timeslots" to "postgres";

grant select on table "public"."section_overrides_timeslots" to "postgres";

grant trigger on table "public"."section_overrides_timeslots" to "postgres";

grant truncate on table "public"."section_overrides_timeslots" to "postgres";

grant update on table "public"."section_overrides_timeslots" to "postgres";

grant delete on table "public"."section_overrides_timeslots" to "service_role";

grant insert on table "public"."section_overrides_timeslots" to "service_role";

grant references on table "public"."section_overrides_timeslots" to "service_role";

grant select on table "public"."section_overrides_timeslots" to "service_role";

grant trigger on table "public"."section_overrides_timeslots" to "service_role";

grant truncate on table "public"."section_overrides_timeslots" to "service_role";

grant update on table "public"."section_overrides_timeslots" to "service_role";


