SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;
CREATE EXTENSION IF NOT EXISTS "pg_cron" WITH SCHEMA "pg_catalog";
CREATE EXTENSION IF NOT EXISTS "pg_net" WITH SCHEMA "extensions";
CREATE EXTENSION IF NOT EXISTS "pgsodium";
ALTER SCHEMA "public" OWNER TO "postgres";
CREATE EXTENSION IF NOT EXISTS "pg_graphql" WITH SCHEMA "graphql";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements" WITH SCHEMA "extensions";
CREATE EXTENSION IF NOT EXISTS "pgcrypto" WITH SCHEMA "extensions";
CREATE EXTENSION IF NOT EXISTS "pgjwt" WITH SCHEMA "extensions";
CREATE EXTENSION IF NOT EXISTS "supabase_vault" WITH SCHEMA "vault";
CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA "extensions";
CREATE TYPE "public"."InmateVitorRelationDegree" AS ENUM (
    'FIRST',
    'SECOND',
    'OTHERS',
    'THIRD'
);
ALTER TYPE "public"."InmateVitorRelationDegree" OWNER TO "postgres";
CREATE TYPE "public"."RoleName" AS ENUM (
    'SUPERADMIN',
    'ADMIN'
);
ALTER TYPE "public"."RoleName" OWNER TO "postgres";
CREATE TYPE "public"."availability_type" AS ENUM (
    'room',
    'section',
    'inmate'
);
ALTER TYPE "public"."availability_type" OWNER TO "postgres";
CREATE TYPE "public"."days" AS ENUM (
    'sunday',
    'monday',
    'tuesday',
    'wednesday',
    'thursday',
    'friday',
    'saturday'
);
ALTER TYPE "public"."days" OWNER TO "postgres";
CREATE TYPE "public"."inmate_status" AS ENUM (
    'AVAILABLE',
    'UNAVAILABLE',
    'SUSPENDED'
);
ALTER TYPE "public"."inmate_status" OWNER TO "postgres";
CREATE TYPE "public"."inmate_visitor_relation" AS ENUM (
    'MOTHER',
    'FATHER',
    'BROTHER',
    'SISTER',
    'SON',
    'DAUGHTER',
    'GRANDFATHER',
    'GRANDMOTHER',
    'GRANDSON',
    'GRANDDAUGHTER',
    'UNCLE',
    'AUNT',
    'NEPHEW',
    'NIECE',
    'COUSIN',
    'GREAT_UNCLE',
    'GREAT_AUNT',
    'GREAT_NEPHEW',
    'GREAT_NIECE',
    'HUSBUND',
    'LAWYER',
    'LEGAL_REPRESENTATIVE',
    'GOVERNMENT_OFFICIAL',
    'SOCIAL_WORKER',
    'OTHER'
);
ALTER TYPE "public"."inmate_visitor_relation" OWNER TO "postgres";
CREATE OR REPLACE FUNCTION "public"."approve_visit_if_whitelisted"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    IF EXISTS (
        SELECT 1
        FROM "Inmate_Visitor"
        WHERE inmate_id = NEW.inmate_id
        AND visitor_id = NEW.visitor_id
        AND is_white_list = TRUE
    ) THEN
        NEW.status := 'APPROVED';
    END IF;
    RETURN NEW;
END;
$$;
ALTER FUNCTION "public"."approve_visit_if_whitelisted"() OWNER TO "postgres";
CREATE OR REPLACE FUNCTION "public"."assign_room_to_visit"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
  -- Assign room_id based on criteria
  SELECT r.id
  INTO NEW.room_id
  FROM public."Room" r
  JOIN public."Inmate" i ON i.sectionid = r.sectionid
  WHERE r.active = true
    AND i.id = NEW.inmate_id
    AND NOT EXISTS (
      SELECT 1
      FROM public."Visit_Request" vr
      WHERE vr.room_id = r.id
        AND vr.datetime <= NEW.datetime + interval '1 minute' * NEW.duration
        AND vr.datetime + interval '1 minute' * vr.duration >= NEW.datetime
        AND (vr.status = 'APPROVED' OR vr.status = 'STARTS_SOON')
        AND vr."isDeleted" IS NULL
    )
  ORDER BY random() -- Randomly pick any available room
  LIMIT 1;

  -- If no room is found, raise an error
  IF NEW.room_id IS NULL THEN
    RAISE EXCEPTION 'No available room for the requested time slot.';
  END IF;

  RETURN NEW;
END;
$$;
ALTER FUNCTION "public"."assign_room_to_visit"() OWNER TO "postgres";
CREATE OR REPLACE FUNCTION "public"."delete_user_from_public"() RETURNS "trigger"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
DELETE FROM public."users" WHERE email = OLD.email;
RETURN OLD;
END;
$$;
ALTER FUNCTION "public"."delete_user_from_public"() OWNER TO "postgres";
CREATE OR REPLACE FUNCTION "public"."disable_auth_user_and_remove_identity"() RETURNS "trigger"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
  -- Set is_active to FALSE in auth.users
  UPDATE auth.users 
  SET is_active = FALSE 
  WHERE id = NEW.user_auth_id;

  -- Delete the user identity from auth.identities
  DELETE FROM auth.identities 
  WHERE id = NEW.user_auth_id;

  RETURN NEW;
END;
$$;
ALTER FUNCTION "public"."disable_auth_user_and_remove_identity"() OWNER TO "postgres";
CREATE OR REPLACE FUNCTION "public"."disable_auth_user_on_delete"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
  -- Update auth.users to set is_active = FALSE where the id matches the user_auth_id
  UPDATE auth.users 
  SET is_active = FALSE 
  WHERE id = NEW.user_auth_id;
  
  RETURN NEW;
END;
$$;
ALTER FUNCTION "public"."disable_auth_user_on_delete"() OWNER TO "postgres";
CREATE OR REPLACE FUNCTION "public"."generate_user_notification"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
DECLARE
    title_id int;
    sla_due interval;
BEGIN
    -- Handle new visitation request on INSERT
    IF TG_OP = 'INSERT' THEN
        title_id := 1;
    ELSE
        -- Handle status updates on UPDATE
        CASE NEW.status_id
            WHEN 2 THEN
                -- Check if the SLA is exceeded
                SELECT value::interval INTO sla_due
                FROM settings
                WHERE settings.module = 'sla' AND settings.key = 'request_response_due';

                IF  NOW() - NEW.created_at > sla_due THEN
                    title_id := 14; -- SLA exceeded
                ELSE
                    title_id := 2; -- Normal status update
                END IF;
            WHEN 10 THEN
                title_id := 10;
            WHEN 9 THEN
                title_id := 9;
            ELSE
                -- If status_id is not one of the specified values, do nothing
                RETURN NEW;
        END CASE;
    END IF;

    -- Insert notifications for all users
    INSERT INTO users_notifications (user_id, visitor_id, visit_id, title_id, created_at, seen)
    SELECT user_auth_id, NEW.visitor_id, NEW.id, title_id, NOW(), NULL
    FROM users;

    RETURN NEW;
END;
$$;
ALTER FUNCTION "public"."generate_user_notification"() OWNER TO "postgres";
CREATE OR REPLACE FUNCTION "public"."get_admin_requests_with_meeting_records"() RETURNS TABLE("request_id" "text", "request_status" "text", "request_number" "text", "inmate_full_name" "text", "inmate_id" "text", "admin_id" "text", "admin_name" "text", "request_date" timestamp without time zone, "meeting_status" "text", "meeting_date_time" timestamp without time zone, "record_id" "text", "recording_url" "text", "record_duration" integer, "archived" boolean, "record_size" integer, "feedback_rate" integer, "feedback_comment" "text")
    LANGUAGE "plpgsql"
    AS $$
BEGIN
RETURN QUERY
SELECT DISTINCT ON (vr.id)
    vr.id,
    vr.status::TEXT,
    vr.visit_number::TEXT,
    inm.full_name AS inmate_full_name,
    vr.inmate_id,
    admin.id as admin_id,
    admin.full_name AS admin_name,
    vr.datetime AS request_date,
    mtg.status::TEXT AS meeting_status,
    mtg.meeting_date_time,
    mrec.id,
    mrec.recording_url,
    mrec.duration AS record_duration,
    mrec.archived,
    mrec.record_size,
    feedback.rate,
    feedback.comment
FROM public."visit_requests" vr

    LEFT JOIN public."inmates" inm ON inm.id = vr.inmate_id
    JOIN public."users" admin ON admin.id = vr.user_id
    LEFT JOIN public."meetings" mtg ON mtg.id = vr.meeting_id
    JOIN public."meeting_records" mrec ON mrec.meeting_id = mtg.id
    LEFT JOIN public."meeting_feedbacks" feedback ON mrec.meeting_id = feedback.meeting_id
WHERE mrec."deleted_at" IS NULL AND mrec.archived = TRUE AND vr.visitor_id IS NULL;
END;
$$;
ALTER FUNCTION "public"."get_admin_requests_with_meeting_records"() OWNER TO "postgres";
CREATE OR REPLACE FUNCTION "public"."get_admin_requests_with_meeting_records_not_archived"() RETURNS TABLE("request_id" "text", "request_status" "text", "request_number" "text", "inmate_full_name" "text", "inmate_id" "text", "admin_id" "text", "admin_name" "text", "request_date" timestamp without time zone, "meeting_status" "text", "meeting_date_time" timestamp without time zone, "record_id" "text", "recording_url" "text", "record_duration" integer, "archived" boolean, "record_size" integer, "feedback_rate" integer, "feedback_comment" "text")
    LANGUAGE "plpgsql"
    AS $$
BEGIN
RETURN QUERY
SELECT DISTINCT ON (vr.id)
    vr.id,
    vr.status::TEXT,
    vr.visit_number::TEXT,
    inm.full_name AS inmate_full_name,
    vr.inmate_id,
    admin.id as admin_id,
    admin.full_name AS admin_name,
    vr.datetime AS request_date,
    mtg.status::TEXT AS meeting_status,
    mtg.meeting_date_time,
    mrec.id,
    mrec.recording_url,
    mrec.duration AS record_duration,
    mrec.archived,
    mrec.record_size,
    feedback.rate,
    feedback.comment
FROM public."visit_requests" vr

    LEFT JOIN public."inmates" inm ON inm.id = vr.inmate_id
    JOIN public."users" admin ON admin.id = vr.user_id
    LEFT JOIN public."meetings" mtg ON mtg.id = vr.meeting_id
    JOIN public."meeting_records" mrec ON mrec.meeting_id = mtg.id
    LEFT JOIN public."meeting_feedbacks" feedback ON mrec.meeting_id = feedback.meeting_id
WHERE mrec."deleted_at" IS NULL AND mrec.archived = FALSE AND vr.visitor_id IS NULL;
END;
$$;
ALTER FUNCTION "public"."get_admin_requests_with_meeting_records_not_archived"() OWNER TO "postgres";
CREATE OR REPLACE FUNCTION "public"."get_visitor_requests_with_meeting_records"() RETURNS TABLE("request_id" "text", "request_status" "text", "request_number" "text", "inmate_full_name" "text", "inmate_id" "text", "visitor_name" "text", "request_date" timestamp with time zone, "from" time with time zone, "to" time with time zone, "relation" "text", "meeting_status" "text", "meeting_date_time" timestamp with time zone, "record_id" "text", "recording_url" "text", "record_duration" integer, "archived" boolean, "record_size" integer, "feedback_rate" integer, "feedback_comment" "text")
    LANGUAGE "plpgsql"
    AS $$BEGIN
RETURN QUERY
SELECT DISTINCT ON (mrec.id)
    vr.id,
    vr.status_id::TEXT,
    vr.visit_number::TEXT,
    inm.full_name AS inmate_full_name,
    vr.inmate_id,
    vis.full_name AS visitor_name,
    vr.datetime::timestamptz AS request_date,
    vr."from"::timetz AS "from", -- New field
    vr."to"::timetz AS "to", -- New field
    iv.relation::TEXT,
    vr.status_id::TEXT AS meeting_status, -- Previously from meetings table
    vr.datetime::timestamptz AS meeting_date_time, -- Previously from meetings table
    mrec.id,
    mrec.recording_url,
    mrec.duration AS record_duration,
    mrec.archived,
    mrec.record_size,
    feedback.rate,
    feedback.comment
FROM public.visit_requests vr
    LEFT JOIN public.inmate_visitors iv
ON vr.inmate_id = iv.inmate_id
    AND vr.visitor_id = iv.visitor_id
    LEFT JOIN public.inmates inm
    ON inm.id = vr.inmate_id
    JOIN public.visitors vis
    ON vis.id = vr.visitor_id
    JOIN public.meeting_records mrec
    ON mrec.visit_request_id = vr.id
    LEFT JOIN public.meeting_feedbacks feedback
    ON mrec.visit_request_id = feedback.visit_request_id
WHERE mrec.deleted_at IS NULL
  AND mrec.archived = TRUE;
END;$$;
ALTER FUNCTION "public"."get_visitor_requests_with_meeting_records"() OWNER TO "postgres";
CREATE OR REPLACE FUNCTION "public"."get_visitor_requests_with_meeting_records_not_archived"() RETURNS TABLE("request_id" "text", "request_status" "text", "request_number" "text", "inmate_full_name" "text", "inmate_id" "text", "visitor_name" "text", "request_date" timestamp with time zone, "from" time with time zone, "to" time with time zone, "relation" "text", "meeting_status" "text", "meeting_date_time" timestamp with time zone, "record_id" "text", "recording_url" "text", "record_duration" integer, "archived" boolean, "record_size" integer, "feedback_rate" integer, "feedback_comment" "text")
    LANGUAGE "plpgsql"
    AS $$BEGIN
RETURN QUERY
SELECT DISTINCT ON (mrec.id)
    vr.id,
    vr.status_id::TEXT,
    vr.visit_number::TEXT,
    inm.full_name AS inmate_full_name,
    vr.inmate_id,
    vis.full_name AS visitor_name,
    vr.datetime::timestamptz AS request_date,
    vr."from"::timetz AS "from", -- New field
    vr."to"::timetz AS "to", -- New field
    iv.relation::TEXT,
    vr.status_id::TEXT AS meeting_status, -- Previously from meetings table
    vr.datetime::timestamptz AS meeting_date_time, -- Previously from meetings table
    mrec.id,
    mrec.recording_url,
    mrec.duration AS record_duration,
    mrec.archived,
    mrec.record_size,
    feedback.rate,
    feedback.comment
FROM public.visit_requests vr
    LEFT JOIN public.inmate_visitors iv
ON vr.inmate_id = iv.inmate_id
    AND vr.visitor_id = iv.visitor_id
    LEFT JOIN public.inmates inm
    ON inm.id = vr.inmate_id
    JOIN public.visitors vis
    ON vis.id = vr.visitor_id
    JOIN public.meeting_records mrec
    ON mrec.visit_request_id = vr.id
    LEFT JOIN public.meeting_feedbacks feedback
    ON mrec.visit_request_id = feedback.visit_request_id
WHERE mrec.deleted_at IS NULL
  AND mrec.archived = FALSE;
END;$$;
ALTER FUNCTION "public"."get_visitor_requests_with_meeting_records_not_archived"() OWNER TO "postgres";
CREATE OR REPLACE FUNCTION "public"."get_visitor_requests_with_relation"("visitor_id_input" "uuid") RETURNS TABLE("request_id" "text", "request_status" "text", "request_number" "text", "inamte_full_name" "text", "inmate_id" "text", "visitor_id" "text", "request_date" timestamp without time zone, "relation" "text")
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    RETURN QUERY
    SELECT vr.id,vr.status::TEXT ,vr.visit_number::TEXT , inm.full_name,vr.inmate_id, vr.visitor_id, vr.datetime, iv.relation::TEXT 
    FROM public."Visit_Request" vr
    JOIN public."Inmate_Visitor" iv ON vr.inmate_id = iv.inmate_id AND vr.visitor_id = iv.visitor_id
    JOIN public."Inmate" inm
    ON inm.id = vr.inmate_id
    WHERE vr.visitor_id = visitor_id_input::TEXT AND vr.status IN ('CANCELED','DENIED','APPROVED','PENDING') AND iv."isDeleted" IS null;
END;
$$;
ALTER FUNCTION "public"."get_visitor_requests_with_relation"("visitor_id_input" "uuid") OWNER TO "postgres";
CREATE OR REPLACE FUNCTION "public"."get_visitor_visit_requests_with_meeting_records_archived"() RETURNS TABLE("request_id" "text", "request_status" "text", "request_number" "text", "visitor_full_name" "text", "visitor_id" "text", "inmate_name" "text", "request_date" timestamp with time zone, "from" time with time zone, "to" time with time zone, "relation" "text", "meeting_status" "text", "meeting_date_time" timestamp with time zone, "record_id" "text", "recording_url" "text", "record_duration" integer, "archived" boolean, "record_size" integer, "feedback_rate" integer, "feedback_comment" "text")
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    RETURN QUERY
    SELECT DISTINCT ON (mrec.id)
        vr.id,
        vr.status_id::TEXT,
        vr.visit_number::TEXT,
        vis.full_name AS visitor_full_name,
        vr.visitor_id,
        inm.full_name AS inmate_name,
        vr.datetime::timestamptz AS request_date,
        vr."from"::timetz AS "from", 
        vr."to"::timetz AS "to", 
        iv.relation::TEXT,
        vr.status_id::TEXT AS meeting_status, 
        vr.datetime::timestamptz AS meeting_date_time, 
        mrec.id,
        mrec.recording_url,
        mrec.duration AS record_duration,
        mrec.archived,
        mrec.record_size,
        feedback.rate,
        feedback.comment
    FROM public.visit_requests vr
    
    LEFT JOIN public.inmate_visitors iv
    ON vr.visitor_id = iv.visitor_id
    AND vr.inmate_id = iv.inmate_id
    
    LEFT JOIN public.visitors vis
    ON vis.id = vr.visitor_id
    
    JOIN public.inmates inm
    ON inm.id = vr.inmate_id
    
    JOIN public.meeting_records mrec
    ON mrec.visit_request_id = vr.id
    
    LEFT JOIN public.meeting_feedbacks feedback
    ON mrec.visit_request_id = feedback.visit_request_id
    
    WHERE mrec.deleted_at IS NULL
    AND mrec.archived = TRUE;
END;
$$;
ALTER FUNCTION "public"."get_visitor_visit_requests_with_meeting_records_archived"() OWNER TO "postgres";
CREATE OR REPLACE FUNCTION "public"."get_visitor_visit_requests_with_meeting_records_not_archived"() RETURNS TABLE("request_id" "text", "request_status" "text", "request_number" "text", "visitor_full_name" "text", "visitor_id" "text", "inmate_name" "text", "request_date" timestamp with time zone, "from" time with time zone, "to" time with time zone, "relation" "text", "meeting_status" "text", "meeting_date_time" timestamp with time zone, "record_id" "text", "recording_url" "text", "record_duration" integer, "archived" boolean, "record_size" integer, "feedback_rate" integer, "feedback_comment" "text")
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    RETURN QUERY
    SELECT DISTINCT ON (mrec.id)
        vr.id,
        vr.status_id::TEXT,
        vr.visit_number::TEXT,
        vis.full_name AS visitor_full_name,
        vr.visitor_id,
        inm.full_name AS inmate_name,
        vr.datetime::timestamptz AS request_date,
        vr."from"::timetz AS "from", 
        vr."to"::timetz AS "to", 
        iv.relation::TEXT,
        vr.status_id::TEXT AS meeting_status, 
        vr.datetime::timestamptz AS meeting_date_time, 
        mrec.id,
        mrec.recording_url,
        mrec.duration AS record_duration,
        mrec.archived,
        mrec.record_size,
        feedback.rate,
        feedback.comment
    FROM public.visit_requests vr
    
    LEFT JOIN public.inmate_visitors iv
    ON vr.visitor_id = iv.visitor_id
    AND vr.inmate_id = iv.inmate_id
    
    LEFT JOIN public.visitors vis
    ON vis.id = vr.visitor_id
    
    JOIN public.inmates inm
    ON inm.id = vr.inmate_id
    
    JOIN public.meeting_records mrec
    ON mrec.visit_request_id = vr.id
    
    LEFT JOIN public.meeting_feedbacks feedback
    ON mrec.visit_request_id = feedback.visit_request_id
    
    WHERE mrec.deleted_at IS NULL
    AND mrec.archived = FALSE;
END;
$$;
ALTER FUNCTION "public"."get_visitor_visit_requests_with_meeting_records_not_archived"() OWNER TO "postgres";
CREATE OR REPLACE FUNCTION "public"."handle_new_user"() RETURNS "trigger"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    default_role_id UUID;
BEGIN
    -- Get the default role ID
    SELECT id INTO default_role_id
    FROM public.roles
    WHERE name = 'default_role'
    LIMIT 1;

    -- If not found, skip processing
    IF default_role_id IS NULL THEN
        RAISE NOTICE 'Default role not found';
        RETURN NEW;
    END IF;

    -- Insert or update the user record
    INSERT INTO public.users (user_auth_id, email, role_id, first_name, last_name)
    VALUES (
        NEW.id,
        NEW.email,
        default_role_id,
        NEW.raw_user_meta_data->>'first_name',
        NEW.raw_user_meta_data->>'last_name'
    )
    ON CONFLICT (email) DO UPDATE SET
        user_auth_id = NEW.id,
        role_id = default_role_id,
        first_name = EXCLUDED.first_name,
        last_name = EXCLUDED.last_name;

    RETURN NEW;
EXCEPTION WHEN OTHERS THEN
    RAISE NOTICE 'Error in handle_new_user(): %', SQLERRM;
    RETURN NEW;
END;
$$;
ALTER FUNCTION "public"."handle_new_user"() OWNER TO "postgres";
CREATE OR REPLACE FUNCTION "public"."handle_public_user"() RETURNS "trigger"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$DECLARE
    default_role_id UUID;
    user_name TEXT;
BEGIN
    -- Log the start of the function
    RAISE NOTICE 'Trigger started for user ID: %, email: %', NEW.id, NEW.email;

    -- Fetch the default role ID
    SELECT id INTO default_role_id
    FROM public.roles
    WHERE name = 'default_role'  -- Ensure this matches the actual role name
    LIMIT 1;

    IF default_role_id IS NULL THEN
        RAISE NOTICE 'No default role found. Exiting trigger.';
        RETURN NEW;
    END IF;

    -- Extract username from email
    user_name := split_part(NEW.email, '@', 1);

    -- Insert or update the user in public.users table
    INSERT INTO public."users" (user_auth_id, email, role_id, first_name, last_name)
    VALUES (
        NEW.id, 
        NEW.email, 
        default_role_id, 
        NEW.raw_user_meta_data->>'first_name', 
        NEW.raw_user_meta_data->>'last_name'
    )
    ON CONFLICT (email)
    DO UPDATE SET 
        user_auth_id = NEW.id,
        role_id = default_role_id,
        first_name = NEW.raw_user_meta_data->>'first_name',
        last_name = NEW.raw_user_meta_data->>'last_name';

    -- Log successful insertion
    RAISE NOTICE 'User successfully inserted/updated in public.users';

    RETURN NEW;
EXCEPTION WHEN OTHERS THEN
    RAISE NOTICE 'Error in trigger: %', SQLERRM;
    RETURN NEW;
END;$$;
ALTER FUNCTION "public"."handle_public_user"() OWNER TO "postgres";
CREATE OR REPLACE FUNCTION "public"."log_activity"("p_user_id" "uuid", "p_user_email" "text", "p_entity_type" "text", "p_entity_id" "text", "p_action" "text", "p_action_group" "text" DEFAULT NULL::"text", "p_old_data" "jsonb" DEFAULT NULL::"jsonb", "p_new_data" "jsonb" DEFAULT NULL::"jsonb", "p_metadata" "jsonb" DEFAULT NULL::"jsonb", "p_ip_address" "text" DEFAULT NULL::"text", "p_user_agent" "text" DEFAULT NULL::"text") RETURNS "uuid"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
  v_log_id UUID;
BEGIN
  INSERT INTO activity_logs (
    user_id,
    user_email,
    entity_type,
    entity_id,
    action,
    action_group,
    old_data,
    new_data,
    metadata,
    ip_address,
    user_agent
  ) VALUES (
    p_user_id,
    p_user_email,
    p_entity_type,
    p_entity_id,
    p_action,
    p_action_group,
    p_old_data,
    p_new_data,
    p_metadata,
    p_ip_address,
    p_user_agent
  )
  RETURNING id INTO v_log_id;
  
  RETURN v_log_id;
END;
$$;
ALTER FUNCTION "public"."log_activity"("p_user_id" "uuid", "p_user_email" "text", "p_entity_type" "text", "p_entity_id" "text", "p_action" "text", "p_action_group" "text", "p_old_data" "jsonb", "p_new_data" "jsonb", "p_metadata" "jsonb", "p_ip_address" "text", "p_user_agent" "text") OWNER TO "postgres";
CREATE OR REPLACE FUNCTION "public"."log_custom_activity"("p_entity_type" "text", "p_entity_id" "text", "p_action" "text", "p_action_group" "text" DEFAULT NULL::"text", "p_metadata" "jsonb" DEFAULT NULL::"jsonb") RETURNS "uuid"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
  v_user_id UUID := auth.uid();
  v_log_id UUID;
BEGIN
  INSERT INTO activity_logs (
    user_id,
    entity_type,
    entity_id,
    action,
    action_group,
    metadata
  ) VALUES (
    v_user_id,
    p_entity_type,
    p_entity_id,
    p_action,
    p_action_group,
    p_metadata
  )
  RETURNING id INTO v_log_id;
  
  RETURN v_log_id;
END;
$$;
ALTER FUNCTION "public"."log_custom_activity"("p_entity_type" "text", "p_entity_id" "text", "p_action" "text", "p_action_group" "text", "p_metadata" "jsonb") OWNER TO "postgres";
CREATE OR REPLACE FUNCTION "public"."notify_visit_status_change"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
DECLARE
status_name TEXT;
BEGIN
    -- Get the name of the status
SELECT name INTO status_name FROM visit_requests_status WHERE id = NEW.status_id;

IF OLD.status_id IS DISTINCT FROM NEW.status_id THEN
        INSERT INTO notifications (id, datetime, visitor_id, visit_id, message, seen)
        VALUES (
            gen_random_uuid(),
            NOW(),
            NEW.visitor_id,
            NEW.id,
            status_name,
            FALSE
        );
END IF;

RETURN NEW;
END;
$$;
ALTER FUNCTION "public"."notify_visit_status_change"() OWNER TO "postgres";
CREATE OR REPLACE FUNCTION "public"."quotes"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$BEGIN
  NEW.first_name := REPLACE(NEW.first_name, '"', '');
  NEW.last_name := REPLACE(NEW.last_name, '"', '');
RETURN NEW;
END;$$;
ALTER FUNCTION "public"."quotes"() OWNER TO "postgres";
CREATE OR REPLACE FUNCTION "public"."remove_quotes_from_name"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    -- Remove single and double quotes from first_name
    NEW.first_name = REPLACE(REPLACE(NEW.first_name, '''', ''), '"', '');
    
    -- Remove single and double quotes from last_name
    NEW.last_name = REPLACE(REPLACE(NEW.last_name, '''', ''), '"', '');
    
    RETURN NEW;
END;
$$;
ALTER FUNCTION "public"."remove_quotes_from_name"() OWNER TO "postgres";
CREATE OR REPLACE FUNCTION "public"."remove_quotes_from_names"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
NEW.first_name := REPLACE(NEW.first_name, '"', '');
NEW.last_name := REPLACE(NEW.last_name, '"', '');
RETURN NEW;
END;
$$;
ALTER FUNCTION "public"."remove_quotes_from_names"() OWNER TO "postgres";
CREATE OR REPLACE FUNCTION "public"."revoke_user_sessions"() RETURNS "trigger"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
  -- Revoke all sessions when the user is deactivated
  DELETE FROM auth.sessions WHERE user_id = NEW.id;
  RETURN NEW;
END;
$$;
ALTER FUNCTION "public"."revoke_user_sessions"() OWNER TO "postgres";
CREATE OR REPLACE FUNCTION "public"."set_banned_until"() RETURNS "trigger"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
    -- Update the auth.users table to set banned_until when deleted_at is set
    UPDATE auth.users 
    SET banned_until = (NOW() + INTERVAL '1 year')::TIMESTAMPTZ
    WHERE id = NEW.user_auth_id;

    RETURN NEW;
END;
$$;
ALTER FUNCTION "public"."set_banned_until"() OWNER TO "postgres";
CREATE OR REPLACE FUNCTION "public"."set_first_response_time"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
  -- Only update first_response_time if it's NULL
  IF NEW.first_response_time IS NULL THEN
    NEW.first_response_time := NOW();
  END IF;
  RETURN NEW;
END;
$$;
ALTER FUNCTION "public"."set_first_response_time"() OWNER TO "postgres";
CREATE OR REPLACE FUNCTION "public"."sync_all_auth_users"() RETURNS "void"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$DECLARE
    auth_user RECORD;
    default_role_id UUID;
    full_name TEXT;
BEGIN
    -- Get the default role ID
    SELECT id INTO default_role_id
    FROM public.roles
    WHERE name = 'default_role'
    LIMIT 1;

    IF default_role_id IS NULL THEN
        RAISE EXCEPTION 'Default role not found in public.roles';
    END IF;

    -- Loop through all auth users
    FOR auth_user IN
        SELECT * FROM auth.users
    LOOP
        -- Construct full_name from first_name and last_name
        full_name := (auth_user.raw_user_meta_data ->> 'first_name') || ' ' || (auth_user.raw_user_meta_data ->> 'last_name');

        -- Insert or update the public.users table
        INSERT INTO public.users (
            user_auth_id, email, role_id, first_name, last_name, full_name
        )
        VALUES (
            auth_user.id,
            auth_user.email,
            default_role_id,
            auth_user.raw_user_meta_data ->> 'first_name',
            auth_user.raw_user_meta_data ->> 'last_name',
            full_name
        )
        ON CONFLICT (user_auth_id) DO UPDATE SET
            email = EXCLUDED.email,
            role_id = EXCLUDED.role_id,
            first_name = EXCLUDED.first_name,
            last_name = EXCLUDED.last_name,
            full_name = EXCLUDED.full_name;
    END LOOP;
END;$$;
-- ALTER FUNCTION "public"."sync_all_auth_users"() OWNER TO "supabase_admin";
CREATE OR REPLACE FUNCTION "public"."trigger_log_changes"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
DECLARE
    v_user_id UUID := NULL; -- Will be set from JWT or app context
    v_user_email TEXT := NULL; -- Optional, can be fetched from auth.users
    v_old_data JSONB := NULL; -- For UPDATE/DELETE
    v_new_data JSONB := NULL; -- For INSERT/UPDATE
    v_action TEXT := TG_OP; -- INSERT, UPDATE, or DELETE
    v_entity_type TEXT := TG_TABLE_NAME; -- Name of the table
    v_entity_id TEXT; -- ID of the affected row
BEGIN
    -- Extract user ID from JWT (if available)
    v_user_id := NULLIF((current_setting('request.jwt.claims', TRUE)::jsonb)->>'sub', '')::UUID;

    -- Prepare old and new data
    IF TG_OP = 'UPDATE' THEN
        v_old_data := to_jsonb(OLD);
        v_new_data := to_jsonb(NEW);
        v_entity_id := NEW.id::TEXT;
    ELSIF TG_OP = 'INSERT' THEN
        v_new_data := to_jsonb(NEW);
        v_entity_id := NEW.id::TEXT;
    ELSIF TG_OP = 'DELETE' THEN
        v_old_data := to_jsonb(OLD);
        v_entity_id := OLD.id::TEXT;
    END IF;

    -- Log the activity
    INSERT INTO activity_logs (
        user_id,
        user_email,
        entity_type,
        entity_id,
        action,
        old_data,
        new_data
    ) VALUES (
        v_user_id,
        v_user_email,
        v_entity_type,
        v_entity_id,
        v_action,
        v_old_data,
        v_new_data
    );

    RETURN NEW;
END;
$$;
ALTER FUNCTION "public"."trigger_log_changes"() OWNER TO "postgres";
CREATE OR REPLACE FUNCTION "public"."update_from_to"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
    BEGIN
        NEW."from" := NEW.datetime;
        NEW."to" := NEW.datetime + INTERVAL '1 minute' * NEW.duration;
        RETURN NEW;
    END;
    $$;
ALTER FUNCTION "public"."update_from_to"() OWNER TO "postgres";
CREATE OR REPLACE FUNCTION "public"."update_visit_title_and_status"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$BEGIN
  -- Check if the status_id is being changed to 2
  IF NEW.status_id = 2 THEN
    -- Construct the title using a prompt and visit_number
    NEW.title := 'Meeting for visit request number' || NEW.visit_number;

END IF;

RETURN NEW;
END;$$;
ALTER FUNCTION "public"."update_visit_title_and_status"() OWNER TO "postgres";
CREATE OR REPLACE FUNCTION "public"."user_trigger_function"() RETURNS "trigger"
    LANGUAGE "plpgsql" SECURITY DEFINER
    SET "search_path" TO 'public'
    AS $$DECLARE
    default_role_id UUID;
    user_name TEXT;
    full_name TEXT;
BEGIN
    -- Log the start of the function
    RAISE NOTICE 'Trigger started for user ID: %, email: %', NEW.id, NEW.email;

    -- Fetch the default role ID
    SELECT id INTO default_role_id
    FROM public.roles
    WHERE name = 'default_role'
    LIMIT 1;

    IF default_role_id IS NULL THEN
        RAISE NOTICE 'No default role found. Exiting trigger.';
        RETURN NEW;
    END IF;

    -- Extract username from email
    user_name := split_part(NEW.email, '@', 1);

    -- Concatenate first and last names with a space
    full_name := (NEW.raw_user_meta_data->>'first_name') || ' ' || (NEW.raw_user_meta_data->>'last_name');

    -- Insert or update the user in public.users table
    INSERT INTO public.users (user_auth_id, email, role_id, first_name, last_name, full_name)
    VALUES (
        NEW.id,
        NEW.email,
        default_role_id,
        NEW.raw_user_meta_data->>'first_name',
        NEW.raw_user_meta_data->>'last_name',
        full_name
    )
    ON CONFLICT (email)
    DO UPDATE SET
        user_auth_id = NEW.id,
        role_id = default_role_id,
        first_name = NEW.raw_user_meta_data->>'first_name',
        last_name = NEW.raw_user_meta_data->>'last_name',
        full_name = (NEW.raw_user_meta_data->>'first_name') || ' ' || (NEW.raw_user_meta_data->>'last_name');

    -- Log successful insertion
    RAISE NOTICE 'User successfully inserted/updated in public.users';

    RETURN NEW;

EXCEPTION WHEN OTHERS THEN
    RAISE NOTICE 'Error in trigger: %', SQLERRM;
    RETURN NEW;
END;$$;
ALTER FUNCTION "public"."user_trigger_function"() OWNER TO "postgres";
SET default_tablespace = '';
SET default_table_access_method = "heap";
CREATE TABLE IF NOT EXISTS "public"."_prisma_migrations" (
    "id" character varying(36) NOT NULL,
    "checksum" character varying(64) NOT NULL,
    "finished_at" timestamp with time zone,
    "migration_name" character varying(255) NOT NULL,
    "logs" "text",
    "rolled_back_at" timestamp with time zone,
    "started_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "applied_steps_count" integer DEFAULT 0 NOT NULL
);
ALTER TABLE "public"."_prisma_migrations" OWNER TO "postgres";
CREATE TABLE IF NOT EXISTS "public"."activity_logs" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "created_at" timestamp(6) with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "user_id" "uuid",
    "user_email" "text",
    "entity_type" "text" NOT NULL,
    "entity_id" "text" NOT NULL,
    "action" "text" NOT NULL,
    "action_group" "text",
    "old_data" "jsonb",
    "new_data" "jsonb",
    "metadata" "jsonb",
    "ip_address" "text",
    "user_agent" "text"
);
ALTER TABLE "public"."activity_logs" OWNER TO "postgres";
CREATE TABLE IF NOT EXISTS "public"."availability_times" (
    "id" "text" DEFAULT "gen_random_uuid"() NOT NULL,
    "setting_id" "text" NOT NULL,
    "type" "public"."availability_type" NOT NULL,
    "available_from" time(6) without time zone NOT NULL,
    "available_to" time(6) without time zone NOT NULL
);
ALTER TABLE "public"."availability_times" OWNER TO "postgres";
CREATE TABLE IF NOT EXISTS "public"."function_logs" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "function_name" "text",
    "message" "text",
    "timestamp" timestamp with time zone DEFAULT "now"()
);
-- ALTER TABLE "public"."function_logs" OWNER TO "supabase_admin";
CREATE TABLE IF NOT EXISTS "public"."inmate_availability_settings" (
    "id" "text" DEFAULT "gen_random_uuid"() NOT NULL,
    "day" "public"."days" NOT NULL,
    "inmate_id" "text" NOT NULL
);
ALTER TABLE "public"."inmate_availability_settings" OWNER TO "postgres";
CREATE TABLE IF NOT EXISTS "public"."inmate_settings" (
    "id" "text" DEFAULT "gen_random_uuid"() NOT NULL,
    "inmate_id" "text" NOT NULL,
    "key" "text" NOT NULL,
    "value" "text" NOT NULL
);
ALTER TABLE "public"."inmate_settings" OWNER TO "postgres";
CREATE TABLE IF NOT EXISTS "public"."inmate_visitor_documents" (
    "id" "text" DEFAULT "gen_random_uuid"() NOT NULL,
    "inmate_visitor_id" "text" NOT NULL,
    "document_url" "text" NOT NULL,
    "document_name" "text" NOT NULL,
    "document_size" "text" NOT NULL
);
ALTER TABLE "public"."inmate_visitor_documents" OWNER TO "postgres";
CREATE TABLE IF NOT EXISTS "public"."inmate_visitors" (
    "id" "text" DEFAULT "gen_random_uuid"() NOT NULL,
    "inmate_id" "text" NOT NULL,
    "visitor_id" "text" NOT NULL,
    "is_white_list" boolean,
    "other" "text",
    "relation" "public"."inmate_visitor_relation",
    "deleted_at" timestamp(3) without time zone
);
ALTER TABLE "public"."inmate_visitors" OWNER TO "postgres";
CREATE TABLE IF NOT EXISTS "public"."inmates" (
    "id" "text" DEFAULT "gen_random_uuid"() NOT NULL,
    "full_name" "text" NOT NULL,
    "national_id" "text",
    "number_of_visits" integer NOT NULL,
    "section_id" "text" NOT NULL,
    "room_id" "text",
    "deleted_at" timestamp(3) without time zone,
    "is_building_default" boolean,
    "is_room_default" boolean,
    "inmate_code" "text",
    "nationality" "text",
    "status_id" integer DEFAULT 1 NOT NULL
);
ALTER TABLE "public"."inmates" OWNER TO "postgres";
CREATE TABLE IF NOT EXISTS "public"."inmates_status" (
    "id" integer NOT NULL,
    "name" "text" NOT NULL
);
ALTER TABLE "public"."inmates_status" OWNER TO "postgres";
CREATE SEQUENCE IF NOT EXISTS "public"."inmates_status_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER TABLE "public"."inmates_status_id_seq" OWNER TO "postgres";
ALTER SEQUENCE "public"."inmates_status_id_seq" OWNED BY "public"."inmates_status"."id";
CREATE TABLE IF NOT EXISTS "public"."meeting_feedbacks" (
    "id" "text" DEFAULT "gen_random_uuid"() NOT NULL,
    "visitor_id" "text" NOT NULL,
    "comment" "text",
    "rate" integer NOT NULL,
    "visit_request_id" "text" NOT NULL
);
ALTER TABLE "public"."meeting_feedbacks" OWNER TO "postgres";
CREATE TABLE IF NOT EXISTS "public"."meeting_records" (
    "id" "text" DEFAULT "gen_random_uuid"() NOT NULL,
    "session_id" "text" DEFAULT "gen_random_uuid"(),
    "recording_url" "text",
    "record_size" integer,
    "duration" integer,
    "created_at" timestamp(3) without time zone,
    "deleted_at" timestamp(3) without time zone,
    "archived" boolean DEFAULT false,
    "visit_request_id" "text" NOT NULL
);
ALTER TABLE "public"."meeting_records" OWNER TO "postgres";
CREATE TABLE IF NOT EXISTS "public"."notification_titles" (
    "id" integer NOT NULL,
    "name" "text" NOT NULL
);
ALTER TABLE "public"."notification_titles" OWNER TO "postgres";
CREATE TABLE IF NOT EXISTS "public"."notifications" (
    "id" "text" DEFAULT "gen_random_uuid"() NOT NULL,
    "datetime" timestamp(6) with time zone,
    "visitor_id" "text" NOT NULL,
    "visit_id" "text" NOT NULL,
    "message" "text" NOT NULL,
    "seen" boolean DEFAULT false
);
ALTER TABLE "public"."notifications" OWNER TO "postgres";
CREATE TABLE IF NOT EXISTS "public"."permissions" (
    "id" "text" DEFAULT "gen_random_uuid"() NOT NULL,
    "name" "text" NOT NULL,
    "category_id" integer NOT NULL
);
ALTER TABLE "public"."permissions" OWNER TO "postgres";
CREATE TABLE IF NOT EXISTS "public"."permissions_categories" (
    "id" integer NOT NULL,
    "name" "text" NOT NULL
);
ALTER TABLE "public"."permissions_categories" OWNER TO "postgres";
CREATE SEQUENCE IF NOT EXISTS "public"."permissions_categories_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER TABLE "public"."permissions_categories_id_seq" OWNER TO "postgres";
ALTER SEQUENCE "public"."permissions_categories_id_seq" OWNED BY "public"."permissions_categories"."id";
CREATE TABLE IF NOT EXISTS "public"."role_permissions" (
    "id" "text" DEFAULT "gen_random_uuid"() NOT NULL,
    "role_id" "text" NOT NULL,
    "permission_id" "text" NOT NULL
);
ALTER TABLE "public"."role_permissions" OWNER TO "postgres";
CREATE TABLE IF NOT EXISTS "public"."roles" (
    "id" "text" DEFAULT "gen_random_uuid"() NOT NULL,
    "name" "text" NOT NULL,
    "deleted_at" timestamp(3) without time zone
);
ALTER TABLE "public"."roles" OWNER TO "postgres";
CREATE TABLE IF NOT EXISTS "public"."room_availability_settings" (
    "id" "text" DEFAULT "gen_random_uuid"() NOT NULL,
    "day" "public"."days" NOT NULL,
    "room_id" "text" NOT NULL,
    "is_available" boolean DEFAULT true NOT NULL
);
ALTER TABLE "public"."room_availability_settings" OWNER TO "postgres";
CREATE TABLE IF NOT EXISTS "public"."room_slots" (
    "id" "text" DEFAULT "gen_random_uuid"() NOT NULL,
    "room_id" "text" NOT NULL,
    "slot_start" timestamp(3) without time zone NOT NULL,
    "slot_end" timestamp(3) without time zone NOT NULL,
    "created_at" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updated_at" timestamp(3) without time zone NOT NULL,
    "visit_request_id" "text" NOT NULL
);
ALTER TABLE "public"."room_slots" OWNER TO "postgres";
CREATE TABLE IF NOT EXISTS "public"."room_status" (
    "id" integer NOT NULL,
    "name" "text" NOT NULL
);
ALTER TABLE "public"."room_status" OWNER TO "postgres";
CREATE SEQUENCE IF NOT EXISTS "public"."room_status_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER TABLE "public"."room_status_id_seq" OWNER TO "postgres";
ALTER SEQUENCE "public"."room_status_id_seq" OWNED BY "public"."room_status"."id";
CREATE TABLE IF NOT EXISTS "public"."rooms" (
    "id" "text" DEFAULT "gen_random_uuid"() NOT NULL,
    "name" "text" NOT NULL,
    "active" boolean NOT NULL,
    "section_id" "text" NOT NULL,
    "is_building_default" boolean,
    "deleted_at" timestamp(3) without time zone,
    "status_id" integer DEFAULT 1 NOT NULL
);
ALTER TABLE "public"."rooms" OWNER TO "postgres";
CREATE TABLE IF NOT EXISTS "public"."section_availability_settings" (
    "id" "text" DEFAULT "gen_random_uuid"() NOT NULL,
    "section_id" "text" NOT NULL,
    "day" "public"."days" NOT NULL
);
ALTER TABLE "public"."section_availability_settings" OWNER TO "postgres";
CREATE TABLE IF NOT EXISTS "public"."sections" (
    "id" "text" DEFAULT "gen_random_uuid"() NOT NULL,
    "name" "text" NOT NULL,
    "is_available" boolean DEFAULT true NOT NULL,
    "deleted_at" timestamp(3) without time zone
);
ALTER TABLE "public"."sections" OWNER TO "postgres";
CREATE TABLE IF NOT EXISTS "public"."servers" (
    "id" "uuid" NOT NULL,
    "code" integer NOT NULL,
    "link" "text" NOT NULL,
    "isactive" boolean DEFAULT true NOT NULL
);
ALTER TABLE "public"."servers" OWNER TO "postgres";
CREATE TABLE IF NOT EXISTS "public"."settings" (
    "id" "text" DEFAULT "gen_random_uuid"() NOT NULL,
    "key" "text" NOT NULL,
    "value" "text" NOT NULL,
    "module" "text" NOT NULL
);
ALTER TABLE "public"."settings" OWNER TO "postgres";
CREATE TABLE IF NOT EXISTS "public"."user_permissions" (
    "id" "text" DEFAULT "gen_random_uuid"() NOT NULL,
    "user_id" "text" NOT NULL,
    "permission_id" "text" NOT NULL
);
ALTER TABLE "public"."user_permissions" OWNER TO "postgres";
CREATE TABLE IF NOT EXISTS "public"."users" (
    "id" "text" DEFAULT "gen_random_uuid"() NOT NULL,
    "role_id" "text" NOT NULL,
    "full_name" "text",
    "email" "text",
    "password" "text",
    "first_name" "text",
    "last_name" "text",
    "user_auth_id" "uuid",
    "deleted_at" timestamp(3) without time zone
);
ALTER TABLE "public"."users" OWNER TO "postgres";
CREATE TABLE IF NOT EXISTS "public"."users_notifications" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "visitor_id" "text" NOT NULL,
    "visit_id" "text" NOT NULL,
    "title_id" integer NOT NULL,
    "created_at" timestamp(6) with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "seen" timestamp(3) without time zone,
    "user_id" "uuid"
);
ALTER TABLE "public"."users_notifications" OWNER TO "postgres";
CREATE TABLE IF NOT EXISTS "public"."visit_requests" (
    "id" "text" DEFAULT "gen_random_uuid"() NOT NULL,
    "visitor_id" "text",
    "inmate_id" "text" NOT NULL,
    "datetime" timestamp(6) with time zone NOT NULL,
    "duration" integer NOT NULL,
    "user_id" "uuid" DEFAULT "gen_random_uuid"(),
    "visit_number" integer NOT NULL,
    "archived" boolean DEFAULT false NOT NULL,
    "requested_inmate_name" "text",
    "from" time(6) with time zone,
    "status_id" integer NOT NULL,
    "to" time(6) with time zone,
    "actual_duration" integer,
    "ended_at" timestamp(3) without time zone,
    "reason" "text",
    "started_at" timestamp(3) without time zone,
    "title" "text",
    "deleted_at" timestamp(3) without time zone,
    "created_at" timestamp(6) with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "first_response_time" timestamp(3) without time zone,
    "updated_at" timestamp(6) with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "meeting_url" "text",
    "room_id" "text"
);
ALTER TABLE "public"."visit_requests" OWNER TO "postgres";
CREATE TABLE IF NOT EXISTS "public"."visit_requests_status" (
    "id" integer NOT NULL,
    "name" "text" NOT NULL
);
ALTER TABLE "public"."visit_requests_status" OWNER TO "postgres";
CREATE SEQUENCE IF NOT EXISTS "public"."visit_requests_status_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER TABLE "public"."visit_requests_status_id_seq" OWNER TO "postgres";
ALTER SEQUENCE "public"."visit_requests_status_id_seq" OWNED BY "public"."visit_requests_status"."id";
CREATE TABLE IF NOT EXISTS "public"."visitors" (
    "id" "text" DEFAULT "gen_random_uuid"() NOT NULL,
    "full_name" "text" NOT NULL,
    "national_id" "text" NOT NULL,
    "blocked" boolean DEFAULT false NOT NULL,
    "phone_number" "text",
    "block_reason" "text",
    "deleted_at" timestamp(3) without time zone,
    "nationality" "text"
);
ALTER TABLE "public"."visitors" OWNER TO "postgres";
CREATE OR REPLACE VIEW "public"."visit_requests_view" AS
 SELECT "vr"."id",
    "vr"."visitor_id",
    "vr"."inmate_id",
    "vr"."datetime",
    "vr"."duration",
    "vr"."user_id",
    "vr"."visit_number",
    "vr"."archived",
    "vr"."requested_inmate_name",
    "vr"."from",
    "vr"."status_id",
    "vr"."to",
    "vr"."actual_duration",
    "vr"."ended_at",
    "vr"."reason",
    "vr"."started_at",
    "vr"."title",
    "vr"."deleted_at",
    "vr"."created_at",
    "vr"."first_response_time",
    "vr"."updated_at",
    "vr"."meeting_url",
    "vr"."room_id",
    "i"."full_name" AS "inmate_name",
    "v"."full_name" AS "visitor_name",
    "s"."name" AS "status_label",
    "u"."full_name" AS "user_name"
   FROM (((("public"."visit_requests" "vr"
     LEFT JOIN "public"."inmates" "i" ON (("vr"."inmate_id" = "i"."id")))
     LEFT JOIN "public"."visitors" "v" ON (("vr"."visitor_id" = "v"."id")))
     LEFT JOIN "public"."visit_requests_status" "s" ON (("vr"."status_id" = "s"."id")))
     LEFT JOIN "public"."users" "u" ON (("vr"."user_id" = "u"."user_auth_id")));
-- ALTER TABLE "public"."visit_requests_view" OWNER TO "supabase_admin";
CREATE SEQUENCE IF NOT EXISTS "public"."visit_requests_visit_number_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER TABLE "public"."visit_requests_visit_number_seq" OWNER TO "postgres";
ALTER SEQUENCE "public"."visit_requests_visit_number_seq" OWNED BY "public"."visit_requests"."visit_number";
ALTER TABLE ONLY "public"."inmates_status" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."inmates_status_id_seq"'::"regclass");
ALTER TABLE ONLY "public"."permissions_categories" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."permissions_categories_id_seq"'::"regclass");
ALTER TABLE ONLY "public"."room_status" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."room_status_id_seq"'::"regclass");
ALTER TABLE ONLY "public"."visit_requests" ALTER COLUMN "visit_number" SET DEFAULT "nextval"('"public"."visit_requests_visit_number_seq"'::"regclass");
ALTER TABLE ONLY "public"."visit_requests_status" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."visit_requests_status_id_seq"'::"regclass");
ALTER TABLE ONLY "public"."_prisma_migrations"
    ADD CONSTRAINT "_prisma_migrations_pkey" PRIMARY KEY ("id");
ALTER TABLE ONLY "public"."activity_logs"
    ADD CONSTRAINT "activity_logs_pkey" PRIMARY KEY ("id");
ALTER TABLE ONLY "public"."availability_times"
    ADD CONSTRAINT "availability_times_pkey" PRIMARY KEY ("id");
ALTER TABLE ONLY "public"."function_logs"
    ADD CONSTRAINT "function_logs_pkey" PRIMARY KEY ("id");
ALTER TABLE ONLY "public"."inmate_availability_settings"
    ADD CONSTRAINT "inmate_availability_settings_pkey" PRIMARY KEY ("id");
ALTER TABLE ONLY "public"."inmate_settings"
    ADD CONSTRAINT "inmate_settings_pkey" PRIMARY KEY ("id");
ALTER TABLE ONLY "public"."inmate_visitor_documents"
    ADD CONSTRAINT "inmate_visitor_documents_pkey" PRIMARY KEY ("id");
ALTER TABLE ONLY "public"."inmate_visitors"
    ADD CONSTRAINT "inmate_visitors_pkey" PRIMARY KEY ("id");
ALTER TABLE ONLY "public"."inmates"
    ADD CONSTRAINT "inmates_pkey" PRIMARY KEY ("id");
ALTER TABLE ONLY "public"."inmates_status"
    ADD CONSTRAINT "inmates_status_pkey" PRIMARY KEY ("id");
ALTER TABLE ONLY "public"."meeting_feedbacks"
    ADD CONSTRAINT "meeting_feedbacks_pkey" PRIMARY KEY ("id");
ALTER TABLE ONLY "public"."meeting_records"
    ADD CONSTRAINT "meeting_records_pkey" PRIMARY KEY ("id");
ALTER TABLE ONLY "public"."notification_titles"
    ADD CONSTRAINT "notification_titles_pkey" PRIMARY KEY ("id");
ALTER TABLE ONLY "public"."notifications"
    ADD CONSTRAINT "notifications_pkey" PRIMARY KEY ("id");
ALTER TABLE ONLY "public"."permissions_categories"
    ADD CONSTRAINT "permissions_categories_pkey" PRIMARY KEY ("id");
ALTER TABLE ONLY "public"."permissions"
    ADD CONSTRAINT "permissions_pkey" PRIMARY KEY ("id");
ALTER TABLE ONLY "public"."role_permissions"
    ADD CONSTRAINT "role_permissions_pkey" PRIMARY KEY ("id");
ALTER TABLE ONLY "public"."roles"
    ADD CONSTRAINT "roles_pkey" PRIMARY KEY ("id");
ALTER TABLE ONLY "public"."room_availability_settings"
    ADD CONSTRAINT "room_availability_settings_pkey" PRIMARY KEY ("id");
ALTER TABLE ONLY "public"."room_slots"
    ADD CONSTRAINT "room_slots_pkey" PRIMARY KEY ("id");
ALTER TABLE ONLY "public"."room_status"
    ADD CONSTRAINT "room_status_pkey" PRIMARY KEY ("id");
ALTER TABLE ONLY "public"."rooms"
    ADD CONSTRAINT "rooms_pkey" PRIMARY KEY ("id");
ALTER TABLE ONLY "public"."section_availability_settings"
    ADD CONSTRAINT "section_availability_settings_pkey" PRIMARY KEY ("id");
ALTER TABLE ONLY "public"."sections"
    ADD CONSTRAINT "sections_pkey" PRIMARY KEY ("id");
ALTER TABLE ONLY "public"."servers"
    ADD CONSTRAINT "servers_pkey" PRIMARY KEY ("id");
ALTER TABLE ONLY "public"."settings"
    ADD CONSTRAINT "settings_pkey" PRIMARY KEY ("id");
ALTER TABLE ONLY "public"."user_permissions"
    ADD CONSTRAINT "user_permissions_pkey" PRIMARY KEY ("id");
ALTER TABLE ONLY "public"."users_notifications"
    ADD CONSTRAINT "users_notifications_pkey" PRIMARY KEY ("id");
ALTER TABLE ONLY "public"."users"
    ADD CONSTRAINT "users_pkey" PRIMARY KEY ("id");
ALTER TABLE ONLY "public"."users"
    ADD CONSTRAINT "users_user_auth_id_unique" UNIQUE ("user_auth_id");
ALTER TABLE ONLY "public"."visit_requests"
    ADD CONSTRAINT "visit_requests_pkey" PRIMARY KEY ("id");
ALTER TABLE ONLY "public"."visit_requests_status"
    ADD CONSTRAINT "visit_requests_status_pkey" PRIMARY KEY ("id");
ALTER TABLE ONLY "public"."visitors"
    ADD CONSTRAINT "visitors_pkey" PRIMARY KEY ("id");
CREATE INDEX "activity_logs_action_idx" ON "public"."activity_logs" USING "btree" ("action");
CREATE INDEX "activity_logs_created_at_idx" ON "public"."activity_logs" USING "btree" ("created_at" DESC);
CREATE INDEX "activity_logs_entity_type_entity_id_idx" ON "public"."activity_logs" USING "btree" ("entity_type", "entity_id");
CREATE INDEX "activity_logs_entity_type_idx" ON "public"."activity_logs" USING "btree" ("entity_type");
CREATE INDEX "activity_logs_user_id_idx" ON "public"."activity_logs" USING "btree" ("user_id");
CREATE INDEX "idx_inmates_national_id" ON "public"."inmates" USING "hash" ("national_id");
CREATE UNIQUE INDEX "inmate_settings_inmate_id_key_key" ON "public"."inmate_settings" USING "btree" ("inmate_id", "key");
CREATE UNIQUE INDEX "inmates_inmate_code_key" ON "public"."inmates" USING "btree" ("inmate_code");
CREATE UNIQUE INDEX "inmates_status_name_key" ON "public"."inmates_status" USING "btree" ("name");
CREATE UNIQUE INDEX "room_status_name_key" ON "public"."room_status" USING "btree" ("name");
CREATE UNIQUE INDEX "servers_code_key" ON "public"."servers" USING "btree" ("code");
CREATE UNIQUE INDEX "servers_link_key" ON "public"."servers" USING "btree" ("link");
CREATE UNIQUE INDEX "unique_permission_name" ON "public"."permissions" USING "btree" ("name");
CREATE UNIQUE INDEX "unique_role_name" ON "public"."roles" USING "btree" ("name");
CREATE UNIQUE INDEX "unique_role_permission" ON "public"."role_permissions" USING "btree" ("role_id", "permission_id");
CREATE UNIQUE INDEX "users_email_key" ON "public"."users" USING "btree" ("email");
CREATE INDEX "users_notifications_seen_idx" ON "public"."users_notifications" USING "btree" ("seen");
CREATE INDEX "users_notifications_title_id_idx" ON "public"."users_notifications" USING "btree" ("title_id");
CREATE INDEX "users_notifications_visit_id_idx" ON "public"."users_notifications" USING "btree" ("visit_id");
CREATE INDEX "users_notifications_visitor_id_idx" ON "public"."users_notifications" USING "btree" ("visitor_id");
CREATE INDEX "users_notifications_visitor_id_visit_id_idx" ON "public"."users_notifications" USING "btree" ("visitor_id", "visit_id");
CREATE UNIQUE INDEX "users_user_auth_id_key" ON "public"."users" USING "btree" ("user_auth_id");
CREATE INDEX "visit_requests_created_at_idx" ON "public"."visit_requests" USING "btree" ("created_at");
CREATE INDEX "visit_requests_inmate_id_idx" ON "public"."visit_requests" USING "btree" ("inmate_id");
CREATE INDEX "visit_requests_status_id_idx" ON "public"."visit_requests" USING "btree" ("status_id");
CREATE UNIQUE INDEX "visit_requests_status_name_key" ON "public"."visit_requests_status" USING "btree" ("name");
CREATE INDEX "visit_requests_visitor_id_idx" ON "public"."visit_requests" USING "btree" ("visitor_id");
CREATE UNIQUE INDEX "visitors_national_id_key" ON "public"."visitors" USING "btree" ("national_id");
CREATE OR REPLACE TRIGGER "log_availability_times_changes" AFTER INSERT OR DELETE OR UPDATE ON "public"."availability_times" FOR EACH ROW EXECUTE FUNCTION "public"."trigger_log_changes"();
CREATE OR REPLACE TRIGGER "log_inmate_availability_settings_changes" AFTER INSERT OR DELETE OR UPDATE ON "public"."inmate_availability_settings" FOR EACH ROW EXECUTE FUNCTION "public"."trigger_log_changes"();
CREATE OR REPLACE TRIGGER "log_inmate_settings_changes" AFTER INSERT OR DELETE OR UPDATE ON "public"."inmate_settings" FOR EACH ROW EXECUTE FUNCTION "public"."trigger_log_changes"();
CREATE OR REPLACE TRIGGER "log_inmate_visitor_documents_changes" AFTER INSERT OR DELETE OR UPDATE ON "public"."inmate_visitor_documents" FOR EACH ROW EXECUTE FUNCTION "public"."trigger_log_changes"();
CREATE OR REPLACE TRIGGER "log_inmate_visitors_changes" AFTER INSERT OR DELETE OR UPDATE ON "public"."inmate_visitors" FOR EACH ROW EXECUTE FUNCTION "public"."trigger_log_changes"();
CREATE OR REPLACE TRIGGER "log_inmates_changes" AFTER INSERT OR DELETE OR UPDATE ON "public"."inmates" FOR EACH ROW EXECUTE FUNCTION "public"."trigger_log_changes"();
CREATE OR REPLACE TRIGGER "log_inmates_status_changes" AFTER INSERT OR DELETE OR UPDATE ON "public"."inmates_status" FOR EACH ROW EXECUTE FUNCTION "public"."trigger_log_changes"();
CREATE OR REPLACE TRIGGER "log_meeting_feedbacks_changes" AFTER INSERT OR DELETE OR UPDATE ON "public"."meeting_feedbacks" FOR EACH ROW EXECUTE FUNCTION "public"."trigger_log_changes"();
CREATE OR REPLACE TRIGGER "log_meeting_records_changes" AFTER INSERT OR DELETE OR UPDATE ON "public"."meeting_records" FOR EACH ROW EXECUTE FUNCTION "public"."trigger_log_changes"();
CREATE OR REPLACE TRIGGER "log_notifications_changes" AFTER INSERT OR DELETE OR UPDATE ON "public"."notifications" FOR EACH ROW EXECUTE FUNCTION "public"."trigger_log_changes"();
CREATE OR REPLACE TRIGGER "log_permissions_changes" AFTER INSERT OR DELETE OR UPDATE ON "public"."permissions" FOR EACH ROW EXECUTE FUNCTION "public"."trigger_log_changes"();
CREATE OR REPLACE TRIGGER "log_role_permissions_changes" AFTER INSERT OR DELETE OR UPDATE ON "public"."role_permissions" FOR EACH ROW EXECUTE FUNCTION "public"."trigger_log_changes"();
CREATE OR REPLACE TRIGGER "log_roles_changes" AFTER INSERT OR DELETE OR UPDATE ON "public"."roles" FOR EACH ROW EXECUTE FUNCTION "public"."trigger_log_changes"();
CREATE OR REPLACE TRIGGER "log_room_availability_settings_changes" AFTER INSERT OR DELETE OR UPDATE ON "public"."room_availability_settings" FOR EACH ROW EXECUTE FUNCTION "public"."trigger_log_changes"();
CREATE OR REPLACE TRIGGER "log_room_slots_changes" AFTER INSERT OR DELETE OR UPDATE ON "public"."room_slots" FOR EACH ROW EXECUTE FUNCTION "public"."trigger_log_changes"();
CREATE OR REPLACE TRIGGER "log_room_status_changes" AFTER INSERT OR DELETE OR UPDATE ON "public"."room_status" FOR EACH ROW EXECUTE FUNCTION "public"."trigger_log_changes"();
CREATE OR REPLACE TRIGGER "log_rooms_changes" AFTER INSERT OR DELETE OR UPDATE ON "public"."rooms" FOR EACH ROW EXECUTE FUNCTION "public"."trigger_log_changes"();
CREATE OR REPLACE TRIGGER "log_section_availability_settings_changes" AFTER INSERT OR DELETE OR UPDATE ON "public"."section_availability_settings" FOR EACH ROW EXECUTE FUNCTION "public"."trigger_log_changes"();
CREATE OR REPLACE TRIGGER "log_sections_changes" AFTER INSERT OR DELETE OR UPDATE ON "public"."sections" FOR EACH ROW EXECUTE FUNCTION "public"."trigger_log_changes"();
CREATE OR REPLACE TRIGGER "log_settings_changes" AFTER INSERT OR DELETE OR UPDATE ON "public"."settings" FOR EACH ROW EXECUTE FUNCTION "public"."trigger_log_changes"();
CREATE OR REPLACE TRIGGER "log_user_permissions_changes" AFTER INSERT OR DELETE OR UPDATE ON "public"."user_permissions" FOR EACH ROW EXECUTE FUNCTION "public"."trigger_log_changes"();
CREATE OR REPLACE TRIGGER "log_users_changes" AFTER INSERT OR DELETE OR UPDATE ON "public"."users" FOR EACH ROW EXECUTE FUNCTION "public"."trigger_log_changes"();
CREATE OR REPLACE TRIGGER "log_visit_requests_changes" AFTER INSERT OR DELETE OR UPDATE ON "public"."visit_requests" FOR EACH ROW EXECUTE FUNCTION "public"."trigger_log_changes"();
CREATE OR REPLACE TRIGGER "log_visit_requests_status_changes" AFTER INSERT OR DELETE OR UPDATE ON "public"."visit_requests_status" FOR EACH ROW EXECUTE FUNCTION "public"."trigger_log_changes"();
CREATE OR REPLACE TRIGGER "log_visitors_changes" AFTER INSERT OR DELETE OR UPDATE ON "public"."visitors" FOR EACH ROW EXECUTE FUNCTION "public"."trigger_log_changes"();
CREATE OR REPLACE TRIGGER "quotes" AFTER INSERT OR UPDATE ON "public"."users" FOR EACH ROW EXECUTE FUNCTION "public"."quotes"();
CREATE OR REPLACE TRIGGER "request_status_trigger" AFTER INSERT OR UPDATE OF "status_id" ON "public"."visit_requests" FOR EACH ROW EXECUTE FUNCTION "public"."generate_user_notification"();
CREATE OR REPLACE TRIGGER "trigger_disable_auth_user" AFTER UPDATE OF "deleted_at" ON "public"."users" FOR EACH ROW WHEN (("new"."deleted_at" IS NOT NULL)) EXECUTE FUNCTION "public"."disable_auth_user_on_delete"();
CREATE OR REPLACE TRIGGER "trigger_disable_auth_user_and_remove_identity" AFTER UPDATE OF "deleted_at" ON "public"."users" FOR EACH ROW WHEN (("new"."deleted_at" IS NOT NULL)) EXECUTE FUNCTION "public"."disable_auth_user_and_remove_identity"();
CREATE OR REPLACE TRIGGER "trigger_remove_quotes" BEFORE INSERT OR UPDATE ON "public"."users" FOR EACH ROW EXECUTE FUNCTION "public"."remove_quotes_from_name"();
CREATE OR REPLACE TRIGGER "trigger_set_banned_until" AFTER UPDATE ON "public"."users" FOR EACH ROW WHEN ((("old"."deleted_at" IS NULL) AND ("new"."deleted_at" IS NOT NULL))) EXECUTE FUNCTION "public"."set_banned_until"();
CREATE OR REPLACE TRIGGER "trigger_update_first_response_time" BEFORE UPDATE OF "status_id" ON "public"."visit_requests" FOR EACH ROW WHEN (("old"."status_id" IS DISTINCT FROM "new"."status_id")) EXECUTE FUNCTION "public"."set_first_response_time"();
CREATE OR REPLACE TRIGGER "trigger_update_from_to" BEFORE INSERT OR UPDATE ON "public"."visit_requests" FOR EACH ROW EXECUTE FUNCTION "public"."update_from_to"();
CREATE OR REPLACE TRIGGER "trigger_update_visit" BEFORE UPDATE ON "public"."visit_requests" FOR EACH ROW WHEN (("old"."status_id" IS DISTINCT FROM "new"."status_id")) EXECUTE FUNCTION "public"."update_visit_title_and_status"();
CREATE OR REPLACE TRIGGER "trigger_visit_status_change" AFTER UPDATE ON "public"."visit_requests" FOR EACH ROW WHEN (("old"."status_id" IS DISTINCT FROM "new"."status_id")) EXECUTE FUNCTION "public"."notify_visit_status_change"();
ALTER TABLE ONLY "public"."activity_logs"
    ADD CONSTRAINT "activity_logs_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("user_auth_id");
ALTER TABLE ONLY "public"."visit_requests"
    ADD CONSTRAINT "fk_visit_requests_rooms" FOREIGN KEY ("room_id") REFERENCES "public"."rooms"("id");
ALTER TABLE ONLY "public"."inmate_availability_settings"
    ADD CONSTRAINT "inmate_availability_settings_inmate_id_fkey" FOREIGN KEY ("inmate_id") REFERENCES "public"."inmates"("id") ON UPDATE CASCADE ON DELETE RESTRICT;
ALTER TABLE ONLY "public"."inmate_settings"
    ADD CONSTRAINT "inmate_settings_inmate_id_fkey" FOREIGN KEY ("inmate_id") REFERENCES "public"."inmates"("id") ON UPDATE CASCADE ON DELETE RESTRICT;
ALTER TABLE ONLY "public"."inmate_visitor_documents"
    ADD CONSTRAINT "inmate_visitor_documents_inmate_visitor_id_fkey" FOREIGN KEY ("inmate_visitor_id") REFERENCES "public"."inmate_visitors"("id") ON UPDATE CASCADE ON DELETE RESTRICT;
ALTER TABLE ONLY "public"."inmate_visitors"
    ADD CONSTRAINT "inmate_visitors_inmate_id_fkey" FOREIGN KEY ("inmate_id") REFERENCES "public"."inmates"("id") ON UPDATE CASCADE ON DELETE RESTRICT;
ALTER TABLE ONLY "public"."inmate_visitors"
    ADD CONSTRAINT "inmate_visitors_visitor_id_fkey" FOREIGN KEY ("visitor_id") REFERENCES "public"."visitors"("id") ON UPDATE CASCADE ON DELETE RESTRICT;
ALTER TABLE ONLY "public"."inmates"
    ADD CONSTRAINT "inmates_room_id_fkey" FOREIGN KEY ("room_id") REFERENCES "public"."rooms"("id") ON UPDATE CASCADE ON DELETE SET NULL;
ALTER TABLE ONLY "public"."inmates"
    ADD CONSTRAINT "inmates_section_id_fkey" FOREIGN KEY ("section_id") REFERENCES "public"."sections"("id") ON UPDATE CASCADE ON DELETE RESTRICT;
ALTER TABLE ONLY "public"."inmates"
    ADD CONSTRAINT "inmates_status_id_fkey" FOREIGN KEY ("status_id") REFERENCES "public"."inmates_status"("id") ON UPDATE CASCADE ON DELETE RESTRICT;
ALTER TABLE ONLY "public"."meeting_feedbacks"
    ADD CONSTRAINT "meeting_feedbacks_visit_request_id_fkey" FOREIGN KEY ("visit_request_id") REFERENCES "public"."visit_requests"("id") ON UPDATE CASCADE ON DELETE RESTRICT;
ALTER TABLE ONLY "public"."meeting_feedbacks"
    ADD CONSTRAINT "meeting_feedbacks_visitor_id_fkey" FOREIGN KEY ("visitor_id") REFERENCES "public"."visitors"("id") ON UPDATE CASCADE ON DELETE RESTRICT;
ALTER TABLE ONLY "public"."meeting_records"
    ADD CONSTRAINT "meeting_records_visit_request_id_fkey" FOREIGN KEY ("visit_request_id") REFERENCES "public"."visit_requests"("id") ON UPDATE CASCADE ON DELETE RESTRICT;
ALTER TABLE ONLY "public"."notifications"
    ADD CONSTRAINT "notifications_visit_id_fkey" FOREIGN KEY ("visit_id") REFERENCES "public"."visit_requests"("id") ON UPDATE CASCADE ON DELETE RESTRICT;
ALTER TABLE ONLY "public"."notifications"
    ADD CONSTRAINT "notifications_visitor_id_fkey" FOREIGN KEY ("visitor_id") REFERENCES "public"."visitors"("id") ON UPDATE CASCADE ON DELETE RESTRICT;
ALTER TABLE ONLY "public"."permissions"
    ADD CONSTRAINT "permissions_category_id_fkey" FOREIGN KEY ("category_id") REFERENCES "public"."permissions_categories"("id") ON UPDATE CASCADE ON DELETE RESTRICT;
ALTER TABLE ONLY "public"."users_notifications"
    ADD CONSTRAINT "public_users_notifications_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("user_auth_id") ON UPDATE CASCADE;
ALTER TABLE ONLY "public"."role_permissions"
    ADD CONSTRAINT "role_permissions_permission_id_fkey" FOREIGN KEY ("permission_id") REFERENCES "public"."permissions"("id") ON UPDATE CASCADE ON DELETE RESTRICT;
ALTER TABLE ONLY "public"."role_permissions"
    ADD CONSTRAINT "role_permissions_role_id_fkey" FOREIGN KEY ("role_id") REFERENCES "public"."roles"("id") ON UPDATE CASCADE ON DELETE RESTRICT;
ALTER TABLE ONLY "public"."room_availability_settings"
    ADD CONSTRAINT "room_availability_settings_room_id_fkey" FOREIGN KEY ("room_id") REFERENCES "public"."rooms"("id") ON UPDATE CASCADE ON DELETE RESTRICT;
ALTER TABLE ONLY "public"."room_slots"
    ADD CONSTRAINT "room_slots_room_id_fkey" FOREIGN KEY ("room_id") REFERENCES "public"."rooms"("id") ON UPDATE CASCADE ON DELETE RESTRICT;
ALTER TABLE ONLY "public"."room_slots"
    ADD CONSTRAINT "room_slots_visit_request_id_fkey" FOREIGN KEY ("visit_request_id") REFERENCES "public"."visit_requests"("id") ON UPDATE CASCADE ON DELETE RESTRICT;
ALTER TABLE ONLY "public"."rooms"
    ADD CONSTRAINT "rooms_section_id_fkey" FOREIGN KEY ("section_id") REFERENCES "public"."sections"("id") ON UPDATE CASCADE ON DELETE RESTRICT;
ALTER TABLE ONLY "public"."rooms"
    ADD CONSTRAINT "rooms_status_id_fkey" FOREIGN KEY ("status_id") REFERENCES "public"."room_status"("id") ON UPDATE CASCADE ON DELETE RESTRICT;
ALTER TABLE ONLY "public"."section_availability_settings"
    ADD CONSTRAINT "section_availability_settings_section_id_fkey" FOREIGN KEY ("section_id") REFERENCES "public"."sections"("id") ON UPDATE CASCADE ON DELETE RESTRICT;
ALTER TABLE ONLY "public"."user_permissions"
    ADD CONSTRAINT "user_permissions_permission_id_fkey" FOREIGN KEY ("permission_id") REFERENCES "public"."permissions"("id") ON UPDATE CASCADE ON DELETE RESTRICT;
ALTER TABLE ONLY "public"."user_permissions"
    ADD CONSTRAINT "user_permissions_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON UPDATE CASCADE ON DELETE RESTRICT;
ALTER TABLE ONLY "public"."users_notifications"
    ADD CONSTRAINT "users_notifications_title_id_fkey" FOREIGN KEY ("title_id") REFERENCES "public"."notification_titles"("id") ON UPDATE CASCADE ON DELETE RESTRICT;
ALTER TABLE ONLY "public"."users_notifications"
    ADD CONSTRAINT "users_notifications_visit_id_fkey" FOREIGN KEY ("visit_id") REFERENCES "public"."visit_requests"("id") ON UPDATE CASCADE ON DELETE RESTRICT;
ALTER TABLE ONLY "public"."users_notifications"
    ADD CONSTRAINT "users_notifications_visitor_id_fkey" FOREIGN KEY ("visitor_id") REFERENCES "public"."visitors"("id") ON UPDATE CASCADE ON DELETE RESTRICT;
ALTER TABLE ONLY "public"."users"
    ADD CONSTRAINT "users_role_id_fkey" FOREIGN KEY ("role_id") REFERENCES "public"."roles"("id") ON UPDATE CASCADE ON DELETE RESTRICT;
ALTER TABLE ONLY "public"."visit_requests"
    ADD CONSTRAINT "visit_requests_inmate_id_fkey" FOREIGN KEY ("inmate_id") REFERENCES "public"."inmates"("id") ON UPDATE CASCADE ON DELETE RESTRICT;
ALTER TABLE ONLY "public"."visit_requests"
    ADD CONSTRAINT "visit_requests_status_id_fkey" FOREIGN KEY ("status_id") REFERENCES "public"."visit_requests_status"("id") ON UPDATE CASCADE ON DELETE RESTRICT;
ALTER TABLE ONLY "public"."visit_requests"
    ADD CONSTRAINT "visit_requests_visitor_id_fkey" FOREIGN KEY ("visitor_id") REFERENCES "public"."visitors"("id") ON UPDATE CASCADE ON DELETE SET NULL;
-- CREATE PUBLICATION "logflare_pub" WITH (publish = 'insert, update, delete, truncate');
-- ALTER PUBLICATION "logflare_pub" OWNER TO "supabase_admin";
ALTER PUBLICATION "supabase_realtime" OWNER TO "postgres";
REVOKE USAGE ON SCHEMA "public" FROM PUBLIC;
GRANT ALL ON SCHEMA "public" TO "anon";
GRANT USAGE ON SCHEMA "public" TO "authenticated";
-- GRANT USAGE ON SCHEMA "public" TO "supabase_admin";
GRANT USAGE ON SCHEMA "public" TO "supabase_replication_admin";
GRANT USAGE ON SCHEMA "public" TO "supabase_read_only_user";
GRANT USAGE ON SCHEMA "public" TO "dashboard_user";
GRANT USAGE ON SCHEMA "public" TO "authenticator";
GRANT USAGE ON SCHEMA "public" TO "supabase_auth_admin";
GRANT USAGE ON SCHEMA "public" TO "supabase_functions_admin";
GRANT USAGE ON SCHEMA "public" TO "supabase_storage_admin";
GRANT USAGE ON SCHEMA "public" TO "service_role";
GRANT ALL ON FUNCTION "public"."approve_visit_if_whitelisted"() TO "supabase_replication_admin";
GRANT ALL ON FUNCTION "public"."approve_visit_if_whitelisted"() TO "supabase_read_only_user";
GRANT ALL ON FUNCTION "public"."approve_visit_if_whitelisted"() TO "dashboard_user";
GRANT ALL ON FUNCTION "public"."approve_visit_if_whitelisted"() TO "authenticator";
GRANT ALL ON FUNCTION "public"."approve_visit_if_whitelisted"() TO "supabase_auth_admin";
GRANT ALL ON FUNCTION "public"."approve_visit_if_whitelisted"() TO "supabase_functions_admin";
GRANT ALL ON FUNCTION "public"."approve_visit_if_whitelisted"() TO "supabase_storage_admin";
-- GRANT ALL ON FUNCTION "public"."approve_visit_if_whitelisted"() TO "supabase_admin";
GRANT ALL ON FUNCTION "public"."approve_visit_if_whitelisted"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."approve_visit_if_whitelisted"() TO "anon";
GRANT ALL ON FUNCTION "public"."approve_visit_if_whitelisted"() TO "service_role";
GRANT ALL ON FUNCTION "public"."assign_room_to_visit"() TO "supabase_replication_admin";
GRANT ALL ON FUNCTION "public"."assign_room_to_visit"() TO "supabase_read_only_user";
GRANT ALL ON FUNCTION "public"."assign_room_to_visit"() TO "dashboard_user";
GRANT ALL ON FUNCTION "public"."assign_room_to_visit"() TO "authenticator";
GRANT ALL ON FUNCTION "public"."assign_room_to_visit"() TO "supabase_auth_admin";
GRANT ALL ON FUNCTION "public"."assign_room_to_visit"() TO "supabase_functions_admin";
GRANT ALL ON FUNCTION "public"."assign_room_to_visit"() TO "supabase_storage_admin";
-- GRANT ALL ON FUNCTION "public"."assign_room_to_visit"() TO "supabase_admin";
GRANT ALL ON FUNCTION "public"."assign_room_to_visit"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."assign_room_to_visit"() TO "anon";
GRANT ALL ON FUNCTION "public"."assign_room_to_visit"() TO "service_role";
GRANT ALL ON FUNCTION "public"."delete_user_from_public"() TO "authenticator";
GRANT ALL ON FUNCTION "public"."delete_user_from_public"() TO "service_role";
GRANT ALL ON FUNCTION "public"."delete_user_from_public"() TO "supabase_replication_admin";
GRANT ALL ON FUNCTION "public"."delete_user_from_public"() TO "supabase_read_only_user";
GRANT ALL ON FUNCTION "public"."delete_user_from_public"() TO "dashboard_user";
GRANT ALL ON FUNCTION "public"."delete_user_from_public"() TO "supabase_auth_admin";
GRANT ALL ON FUNCTION "public"."delete_user_from_public"() TO "supabase_functions_admin";
GRANT ALL ON FUNCTION "public"."delete_user_from_public"() TO "supabase_storage_admin";
-- GRANT ALL ON FUNCTION "public"."delete_user_from_public"() TO "supabase_admin";
GRANT ALL ON FUNCTION "public"."delete_user_from_public"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."delete_user_from_public"() TO "anon";
GRANT ALL ON FUNCTION "public"."disable_auth_user_and_remove_identity"() TO "supabase_replication_admin";
GRANT ALL ON FUNCTION "public"."disable_auth_user_and_remove_identity"() TO "supabase_read_only_user";
GRANT ALL ON FUNCTION "public"."disable_auth_user_and_remove_identity"() TO "dashboard_user";
GRANT ALL ON FUNCTION "public"."disable_auth_user_and_remove_identity"() TO "authenticator";
GRANT ALL ON FUNCTION "public"."disable_auth_user_and_remove_identity"() TO "supabase_auth_admin";
GRANT ALL ON FUNCTION "public"."disable_auth_user_and_remove_identity"() TO "supabase_functions_admin";
GRANT ALL ON FUNCTION "public"."disable_auth_user_and_remove_identity"() TO "supabase_storage_admin";
-- GRANT ALL ON FUNCTION "public"."disable_auth_user_and_remove_identity"() TO "supabase_admin";
GRANT ALL ON FUNCTION "public"."disable_auth_user_and_remove_identity"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."disable_auth_user_and_remove_identity"() TO "anon";
GRANT ALL ON FUNCTION "public"."disable_auth_user_and_remove_identity"() TO "service_role";
GRANT ALL ON FUNCTION "public"."disable_auth_user_on_delete"() TO "supabase_replication_admin";
GRANT ALL ON FUNCTION "public"."disable_auth_user_on_delete"() TO "supabase_read_only_user";
GRANT ALL ON FUNCTION "public"."disable_auth_user_on_delete"() TO "dashboard_user";
GRANT ALL ON FUNCTION "public"."disable_auth_user_on_delete"() TO "authenticator";
GRANT ALL ON FUNCTION "public"."disable_auth_user_on_delete"() TO "supabase_auth_admin";
GRANT ALL ON FUNCTION "public"."disable_auth_user_on_delete"() TO "supabase_functions_admin";
GRANT ALL ON FUNCTION "public"."disable_auth_user_on_delete"() TO "supabase_storage_admin";
-- GRANT ALL ON FUNCTION "public"."disable_auth_user_on_delete"() TO "supabase_admin";
GRANT ALL ON FUNCTION "public"."disable_auth_user_on_delete"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."disable_auth_user_on_delete"() TO "anon";
GRANT ALL ON FUNCTION "public"."disable_auth_user_on_delete"() TO "service_role";
GRANT ALL ON FUNCTION "public"."generate_user_notification"() TO "supabase_replication_admin";
GRANT ALL ON FUNCTION "public"."generate_user_notification"() TO "supabase_read_only_user";
GRANT ALL ON FUNCTION "public"."generate_user_notification"() TO "dashboard_user";
GRANT ALL ON FUNCTION "public"."generate_user_notification"() TO "authenticator";
GRANT ALL ON FUNCTION "public"."generate_user_notification"() TO "supabase_auth_admin";
GRANT ALL ON FUNCTION "public"."generate_user_notification"() TO "supabase_functions_admin";
GRANT ALL ON FUNCTION "public"."generate_user_notification"() TO "supabase_storage_admin";
-- GRANT ALL ON FUNCTION "public"."generate_user_notification"() TO "supabase_admin";
GRANT ALL ON FUNCTION "public"."generate_user_notification"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."generate_user_notification"() TO "anon";
GRANT ALL ON FUNCTION "public"."generate_user_notification"() TO "service_role";
GRANT ALL ON FUNCTION "public"."get_admin_requests_with_meeting_records"() TO "supabase_replication_admin";
GRANT ALL ON FUNCTION "public"."get_admin_requests_with_meeting_records"() TO "supabase_read_only_user";
GRANT ALL ON FUNCTION "public"."get_admin_requests_with_meeting_records"() TO "dashboard_user";
GRANT ALL ON FUNCTION "public"."get_admin_requests_with_meeting_records"() TO "authenticator";
GRANT ALL ON FUNCTION "public"."get_admin_requests_with_meeting_records"() TO "supabase_auth_admin";
GRANT ALL ON FUNCTION "public"."get_admin_requests_with_meeting_records"() TO "supabase_functions_admin";
GRANT ALL ON FUNCTION "public"."get_admin_requests_with_meeting_records"() TO "supabase_storage_admin";
-- GRANT ALL ON FUNCTION "public"."get_admin_requests_with_meeting_records"() TO "supabase_admin";
GRANT ALL ON FUNCTION "public"."get_admin_requests_with_meeting_records"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_admin_requests_with_meeting_records"() TO "anon";
GRANT ALL ON FUNCTION "public"."get_admin_requests_with_meeting_records"() TO "service_role";
GRANT ALL ON FUNCTION "public"."get_admin_requests_with_meeting_records_not_archived"() TO "supabase_replication_admin";
GRANT ALL ON FUNCTION "public"."get_admin_requests_with_meeting_records_not_archived"() TO "supabase_read_only_user";
GRANT ALL ON FUNCTION "public"."get_admin_requests_with_meeting_records_not_archived"() TO "dashboard_user";
GRANT ALL ON FUNCTION "public"."get_admin_requests_with_meeting_records_not_archived"() TO "authenticator";
GRANT ALL ON FUNCTION "public"."get_admin_requests_with_meeting_records_not_archived"() TO "supabase_auth_admin";
GRANT ALL ON FUNCTION "public"."get_admin_requests_with_meeting_records_not_archived"() TO "supabase_functions_admin";
GRANT ALL ON FUNCTION "public"."get_admin_requests_with_meeting_records_not_archived"() TO "supabase_storage_admin";
-- GRANT ALL ON FUNCTION "public"."get_admin_requests_with_meeting_records_not_archived"() TO "supabase_admin";
GRANT ALL ON FUNCTION "public"."get_admin_requests_with_meeting_records_not_archived"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_admin_requests_with_meeting_records_not_archived"() TO "anon";
GRANT ALL ON FUNCTION "public"."get_admin_requests_with_meeting_records_not_archived"() TO "service_role";
GRANT ALL ON FUNCTION "public"."get_visitor_requests_with_meeting_records"() TO "supabase_replication_admin";
GRANT ALL ON FUNCTION "public"."get_visitor_requests_with_meeting_records"() TO "supabase_read_only_user";
GRANT ALL ON FUNCTION "public"."get_visitor_requests_with_meeting_records"() TO "dashboard_user";
GRANT ALL ON FUNCTION "public"."get_visitor_requests_with_meeting_records"() TO "authenticator";
GRANT ALL ON FUNCTION "public"."get_visitor_requests_with_meeting_records"() TO "supabase_auth_admin";
GRANT ALL ON FUNCTION "public"."get_visitor_requests_with_meeting_records"() TO "supabase_functions_admin";
GRANT ALL ON FUNCTION "public"."get_visitor_requests_with_meeting_records"() TO "supabase_storage_admin";
-- GRANT ALL ON FUNCTION "public"."get_visitor_requests_with_meeting_records"() TO "supabase_admin";
GRANT ALL ON FUNCTION "public"."get_visitor_requests_with_meeting_records"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_visitor_requests_with_meeting_records"() TO "anon";
GRANT ALL ON FUNCTION "public"."get_visitor_requests_with_meeting_records"() TO "service_role";
GRANT ALL ON FUNCTION "public"."get_visitor_requests_with_meeting_records_not_archived"() TO "supabase_replication_admin";
GRANT ALL ON FUNCTION "public"."get_visitor_requests_with_meeting_records_not_archived"() TO "supabase_read_only_user";
GRANT ALL ON FUNCTION "public"."get_visitor_requests_with_meeting_records_not_archived"() TO "dashboard_user";
GRANT ALL ON FUNCTION "public"."get_visitor_requests_with_meeting_records_not_archived"() TO "authenticator";
GRANT ALL ON FUNCTION "public"."get_visitor_requests_with_meeting_records_not_archived"() TO "supabase_auth_admin";
GRANT ALL ON FUNCTION "public"."get_visitor_requests_with_meeting_records_not_archived"() TO "supabase_functions_admin";
GRANT ALL ON FUNCTION "public"."get_visitor_requests_with_meeting_records_not_archived"() TO "supabase_storage_admin";
-- GRANT ALL ON FUNCTION "public"."get_visitor_requests_with_meeting_records_not_archived"() TO "supabase_admin";
GRANT ALL ON FUNCTION "public"."get_visitor_requests_with_meeting_records_not_archived"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_visitor_requests_with_meeting_records_not_archived"() TO "anon";
GRANT ALL ON FUNCTION "public"."get_visitor_requests_with_meeting_records_not_archived"() TO "service_role";
GRANT ALL ON FUNCTION "public"."get_visitor_requests_with_relation"("visitor_id_input" "uuid") TO "supabase_replication_admin";
GRANT ALL ON FUNCTION "public"."get_visitor_requests_with_relation"("visitor_id_input" "uuid") TO "supabase_read_only_user";
GRANT ALL ON FUNCTION "public"."get_visitor_requests_with_relation"("visitor_id_input" "uuid") TO "dashboard_user";
GRANT ALL ON FUNCTION "public"."get_visitor_requests_with_relation"("visitor_id_input" "uuid") TO "authenticator";
GRANT ALL ON FUNCTION "public"."get_visitor_requests_with_relation"("visitor_id_input" "uuid") TO "supabase_auth_admin";
GRANT ALL ON FUNCTION "public"."get_visitor_requests_with_relation"("visitor_id_input" "uuid") TO "supabase_functions_admin";
GRANT ALL ON FUNCTION "public"."get_visitor_requests_with_relation"("visitor_id_input" "uuid") TO "supabase_storage_admin";
-- GRANT ALL ON FUNCTION "public"."get_visitor_requests_with_relation"("visitor_id_input" "uuid") TO "supabase_admin";
GRANT ALL ON FUNCTION "public"."get_visitor_requests_with_relation"("visitor_id_input" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_visitor_requests_with_relation"("visitor_id_input" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."get_visitor_requests_with_relation"("visitor_id_input" "uuid") TO "service_role";
GRANT ALL ON FUNCTION "public"."get_visitor_visit_requests_with_meeting_records_archived"() TO "supabase_replication_admin";
GRANT ALL ON FUNCTION "public"."get_visitor_visit_requests_with_meeting_records_archived"() TO "supabase_read_only_user";
GRANT ALL ON FUNCTION "public"."get_visitor_visit_requests_with_meeting_records_archived"() TO "dashboard_user";
GRANT ALL ON FUNCTION "public"."get_visitor_visit_requests_with_meeting_records_archived"() TO "authenticator";
GRANT ALL ON FUNCTION "public"."get_visitor_visit_requests_with_meeting_records_archived"() TO "supabase_auth_admin";
GRANT ALL ON FUNCTION "public"."get_visitor_visit_requests_with_meeting_records_archived"() TO "supabase_functions_admin";
GRANT ALL ON FUNCTION "public"."get_visitor_visit_requests_with_meeting_records_archived"() TO "supabase_storage_admin";
-- GRANT ALL ON FUNCTION "public"."get_visitor_visit_requests_with_meeting_records_archived"() TO "supabase_admin";
GRANT ALL ON FUNCTION "public"."get_visitor_visit_requests_with_meeting_records_archived"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_visitor_visit_requests_with_meeting_records_archived"() TO "anon";
GRANT ALL ON FUNCTION "public"."get_visitor_visit_requests_with_meeting_records_archived"() TO "service_role";
GRANT ALL ON FUNCTION "public"."get_visitor_visit_requests_with_meeting_records_not_archived"() TO "supabase_replication_admin";
GRANT ALL ON FUNCTION "public"."get_visitor_visit_requests_with_meeting_records_not_archived"() TO "supabase_read_only_user";
GRANT ALL ON FUNCTION "public"."get_visitor_visit_requests_with_meeting_records_not_archived"() TO "dashboard_user";
GRANT ALL ON FUNCTION "public"."get_visitor_visit_requests_with_meeting_records_not_archived"() TO "authenticator";
GRANT ALL ON FUNCTION "public"."get_visitor_visit_requests_with_meeting_records_not_archived"() TO "supabase_auth_admin";
GRANT ALL ON FUNCTION "public"."get_visitor_visit_requests_with_meeting_records_not_archived"() TO "supabase_functions_admin";
GRANT ALL ON FUNCTION "public"."get_visitor_visit_requests_with_meeting_records_not_archived"() TO "supabase_storage_admin";
-- GRANT ALL ON FUNCTION "public"."get_visitor_visit_requests_with_meeting_records_not_archived"() TO "supabase_admin";
GRANT ALL ON FUNCTION "public"."get_visitor_visit_requests_with_meeting_records_not_archived"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_visitor_visit_requests_with_meeting_records_not_archived"() TO "anon";
GRANT ALL ON FUNCTION "public"."get_visitor_visit_requests_with_meeting_records_not_archived"() TO "service_role";
GRANT ALL ON FUNCTION "public"."handle_new_user"() TO "supabase_replication_admin";
GRANT ALL ON FUNCTION "public"."handle_new_user"() TO "supabase_read_only_user";
GRANT ALL ON FUNCTION "public"."handle_new_user"() TO "dashboard_user";
GRANT ALL ON FUNCTION "public"."handle_new_user"() TO "authenticator";
GRANT ALL ON FUNCTION "public"."handle_new_user"() TO "supabase_auth_admin";
GRANT ALL ON FUNCTION "public"."handle_new_user"() TO "supabase_functions_admin";
GRANT ALL ON FUNCTION "public"."handle_new_user"() TO "supabase_storage_admin";
-- GRANT ALL ON FUNCTION "public"."handle_new_user"() TO "supabase_admin";
GRANT ALL ON FUNCTION "public"."handle_new_user"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."handle_new_user"() TO "anon";
GRANT ALL ON FUNCTION "public"."handle_new_user"() TO "service_role";
GRANT ALL ON FUNCTION "public"."handle_public_user"() TO "supabase_replication_admin";
GRANT ALL ON FUNCTION "public"."handle_public_user"() TO "supabase_read_only_user";
GRANT ALL ON FUNCTION "public"."handle_public_user"() TO "dashboard_user";
GRANT ALL ON FUNCTION "public"."handle_public_user"() TO "authenticator";
GRANT ALL ON FUNCTION "public"."handle_public_user"() TO "supabase_auth_admin";
GRANT ALL ON FUNCTION "public"."handle_public_user"() TO "supabase_functions_admin";
GRANT ALL ON FUNCTION "public"."handle_public_user"() TO "supabase_storage_admin";
-- GRANT ALL ON FUNCTION "public"."handle_public_user"() TO "supabase_admin";
GRANT ALL ON FUNCTION "public"."handle_public_user"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."handle_public_user"() TO "anon";
GRANT ALL ON FUNCTION "public"."handle_public_user"() TO "service_role";
GRANT ALL ON FUNCTION "public"."log_activity"("p_user_id" "uuid", "p_user_email" "text", "p_entity_type" "text", "p_entity_id" "text", "p_action" "text", "p_action_group" "text", "p_old_data" "jsonb", "p_new_data" "jsonb", "p_metadata" "jsonb", "p_ip_address" "text", "p_user_agent" "text") TO "supabase_replication_admin";
GRANT ALL ON FUNCTION "public"."log_activity"("p_user_id" "uuid", "p_user_email" "text", "p_entity_type" "text", "p_entity_id" "text", "p_action" "text", "p_action_group" "text", "p_old_data" "jsonb", "p_new_data" "jsonb", "p_metadata" "jsonb", "p_ip_address" "text", "p_user_agent" "text") TO "supabase_read_only_user";
GRANT ALL ON FUNCTION "public"."log_activity"("p_user_id" "uuid", "p_user_email" "text", "p_entity_type" "text", "p_entity_id" "text", "p_action" "text", "p_action_group" "text", "p_old_data" "jsonb", "p_new_data" "jsonb", "p_metadata" "jsonb", "p_ip_address" "text", "p_user_agent" "text") TO "dashboard_user";
GRANT ALL ON FUNCTION "public"."log_activity"("p_user_id" "uuid", "p_user_email" "text", "p_entity_type" "text", "p_entity_id" "text", "p_action" "text", "p_action_group" "text", "p_old_data" "jsonb", "p_new_data" "jsonb", "p_metadata" "jsonb", "p_ip_address" "text", "p_user_agent" "text") TO "authenticator";
GRANT ALL ON FUNCTION "public"."log_activity"("p_user_id" "uuid", "p_user_email" "text", "p_entity_type" "text", "p_entity_id" "text", "p_action" "text", "p_action_group" "text", "p_old_data" "jsonb", "p_new_data" "jsonb", "p_metadata" "jsonb", "p_ip_address" "text", "p_user_agent" "text") TO "supabase_auth_admin";
GRANT ALL ON FUNCTION "public"."log_activity"("p_user_id" "uuid", "p_user_email" "text", "p_entity_type" "text", "p_entity_id" "text", "p_action" "text", "p_action_group" "text", "p_old_data" "jsonb", "p_new_data" "jsonb", "p_metadata" "jsonb", "p_ip_address" "text", "p_user_agent" "text") TO "supabase_functions_admin";
GRANT ALL ON FUNCTION "public"."log_activity"("p_user_id" "uuid", "p_user_email" "text", "p_entity_type" "text", "p_entity_id" "text", "p_action" "text", "p_action_group" "text", "p_old_data" "jsonb", "p_new_data" "jsonb", "p_metadata" "jsonb", "p_ip_address" "text", "p_user_agent" "text") TO "supabase_storage_admin";
-- GRANT ALL ON FUNCTION "public"."log_activity"("p_user_id" "uuid", "p_user_email" "text", "p_entity_type" "text", "p_entity_id" "text", "p_action" "text", "p_action_group" "text", "p_old_data" "jsonb", "p_new_data" "jsonb", "p_metadata" "jsonb", "p_ip_address" "text", "p_user_agent" "text") TO "supabase_admin";
GRANT ALL ON FUNCTION "public"."log_activity"("p_user_id" "uuid", "p_user_email" "text", "p_entity_type" "text", "p_entity_id" "text", "p_action" "text", "p_action_group" "text", "p_old_data" "jsonb", "p_new_data" "jsonb", "p_metadata" "jsonb", "p_ip_address" "text", "p_user_agent" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."log_activity"("p_user_id" "uuid", "p_user_email" "text", "p_entity_type" "text", "p_entity_id" "text", "p_action" "text", "p_action_group" "text", "p_old_data" "jsonb", "p_new_data" "jsonb", "p_metadata" "jsonb", "p_ip_address" "text", "p_user_agent" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."log_activity"("p_user_id" "uuid", "p_user_email" "text", "p_entity_type" "text", "p_entity_id" "text", "p_action" "text", "p_action_group" "text", "p_old_data" "jsonb", "p_new_data" "jsonb", "p_metadata" "jsonb", "p_ip_address" "text", "p_user_agent" "text") TO "service_role";
GRANT ALL ON FUNCTION "public"."log_custom_activity"("p_entity_type" "text", "p_entity_id" "text", "p_action" "text", "p_action_group" "text", "p_metadata" "jsonb") TO "supabase_replication_admin";
GRANT ALL ON FUNCTION "public"."log_custom_activity"("p_entity_type" "text", "p_entity_id" "text", "p_action" "text", "p_action_group" "text", "p_metadata" "jsonb") TO "supabase_read_only_user";
GRANT ALL ON FUNCTION "public"."log_custom_activity"("p_entity_type" "text", "p_entity_id" "text", "p_action" "text", "p_action_group" "text", "p_metadata" "jsonb") TO "dashboard_user";
GRANT ALL ON FUNCTION "public"."log_custom_activity"("p_entity_type" "text", "p_entity_id" "text", "p_action" "text", "p_action_group" "text", "p_metadata" "jsonb") TO "authenticator";
GRANT ALL ON FUNCTION "public"."log_custom_activity"("p_entity_type" "text", "p_entity_id" "text", "p_action" "text", "p_action_group" "text", "p_metadata" "jsonb") TO "supabase_auth_admin";
GRANT ALL ON FUNCTION "public"."log_custom_activity"("p_entity_type" "text", "p_entity_id" "text", "p_action" "text", "p_action_group" "text", "p_metadata" "jsonb") TO "supabase_functions_admin";
GRANT ALL ON FUNCTION "public"."log_custom_activity"("p_entity_type" "text", "p_entity_id" "text", "p_action" "text", "p_action_group" "text", "p_metadata" "jsonb") TO "supabase_storage_admin";
-- GRANT ALL ON FUNCTION "public"."log_custom_activity"("p_entity_type" "text", "p_entity_id" "text", "p_action" "text", "p_action_group" "text", "p_metadata" "jsonb") TO "supabase_admin";
GRANT ALL ON FUNCTION "public"."log_custom_activity"("p_entity_type" "text", "p_entity_id" "text", "p_action" "text", "p_action_group" "text", "p_metadata" "jsonb") TO "authenticated";
GRANT ALL ON FUNCTION "public"."log_custom_activity"("p_entity_type" "text", "p_entity_id" "text", "p_action" "text", "p_action_group" "text", "p_metadata" "jsonb") TO "anon";
GRANT ALL ON FUNCTION "public"."log_custom_activity"("p_entity_type" "text", "p_entity_id" "text", "p_action" "text", "p_action_group" "text", "p_metadata" "jsonb") TO "service_role";
GRANT ALL ON FUNCTION "public"."notify_visit_status_change"() TO "supabase_replication_admin";
GRANT ALL ON FUNCTION "public"."notify_visit_status_change"() TO "supabase_read_only_user";
GRANT ALL ON FUNCTION "public"."notify_visit_status_change"() TO "dashboard_user";
GRANT ALL ON FUNCTION "public"."notify_visit_status_change"() TO "authenticator";
GRANT ALL ON FUNCTION "public"."notify_visit_status_change"() TO "supabase_auth_admin";
GRANT ALL ON FUNCTION "public"."notify_visit_status_change"() TO "supabase_functions_admin";
GRANT ALL ON FUNCTION "public"."notify_visit_status_change"() TO "supabase_storage_admin";
-- GRANT ALL ON FUNCTION "public"."notify_visit_status_change"() TO "supabase_admin";
GRANT ALL ON FUNCTION "public"."notify_visit_status_change"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."notify_visit_status_change"() TO "anon";
GRANT ALL ON FUNCTION "public"."notify_visit_status_change"() TO "service_role";
GRANT ALL ON FUNCTION "public"."quotes"() TO "supabase_replication_admin";
GRANT ALL ON FUNCTION "public"."quotes"() TO "supabase_read_only_user";
GRANT ALL ON FUNCTION "public"."quotes"() TO "dashboard_user";
GRANT ALL ON FUNCTION "public"."quotes"() TO "authenticator";
GRANT ALL ON FUNCTION "public"."quotes"() TO "supabase_auth_admin";
GRANT ALL ON FUNCTION "public"."quotes"() TO "supabase_functions_admin";
GRANT ALL ON FUNCTION "public"."quotes"() TO "supabase_storage_admin";
-- GRANT ALL ON FUNCTION "public"."quotes"() TO "supabase_admin";
GRANT ALL ON FUNCTION "public"."quotes"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."quotes"() TO "anon";
GRANT ALL ON FUNCTION "public"."quotes"() TO "service_role";
GRANT ALL ON FUNCTION "public"."remove_quotes_from_name"() TO "supabase_replication_admin";
GRANT ALL ON FUNCTION "public"."remove_quotes_from_name"() TO "supabase_read_only_user";
GRANT ALL ON FUNCTION "public"."remove_quotes_from_name"() TO "dashboard_user";
GRANT ALL ON FUNCTION "public"."remove_quotes_from_name"() TO "authenticator";
GRANT ALL ON FUNCTION "public"."remove_quotes_from_name"() TO "supabase_auth_admin";
GRANT ALL ON FUNCTION "public"."remove_quotes_from_name"() TO "supabase_functions_admin";
GRANT ALL ON FUNCTION "public"."remove_quotes_from_name"() TO "supabase_storage_admin";
-- GRANT ALL ON FUNCTION "public"."remove_quotes_from_name"() TO "supabase_admin";
GRANT ALL ON FUNCTION "public"."remove_quotes_from_name"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."remove_quotes_from_name"() TO "anon";
GRANT ALL ON FUNCTION "public"."remove_quotes_from_name"() TO "service_role";
GRANT ALL ON FUNCTION "public"."remove_quotes_from_names"() TO "supabase_replication_admin";
GRANT ALL ON FUNCTION "public"."remove_quotes_from_names"() TO "supabase_read_only_user";
GRANT ALL ON FUNCTION "public"."remove_quotes_from_names"() TO "dashboard_user";
GRANT ALL ON FUNCTION "public"."remove_quotes_from_names"() TO "authenticator";
GRANT ALL ON FUNCTION "public"."remove_quotes_from_names"() TO "supabase_auth_admin";
GRANT ALL ON FUNCTION "public"."remove_quotes_from_names"() TO "supabase_functions_admin";
GRANT ALL ON FUNCTION "public"."remove_quotes_from_names"() TO "supabase_storage_admin";
-- GRANT ALL ON FUNCTION "public"."remove_quotes_from_names"() TO "supabase_admin";
GRANT ALL ON FUNCTION "public"."remove_quotes_from_names"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."remove_quotes_from_names"() TO "anon";
GRANT ALL ON FUNCTION "public"."remove_quotes_from_names"() TO "service_role";
GRANT ALL ON FUNCTION "public"."revoke_user_sessions"() TO "supabase_replication_admin";
GRANT ALL ON FUNCTION "public"."revoke_user_sessions"() TO "supabase_read_only_user";
GRANT ALL ON FUNCTION "public"."revoke_user_sessions"() TO "dashboard_user";
GRANT ALL ON FUNCTION "public"."revoke_user_sessions"() TO "authenticator";
GRANT ALL ON FUNCTION "public"."revoke_user_sessions"() TO "supabase_auth_admin";
GRANT ALL ON FUNCTION "public"."revoke_user_sessions"() TO "supabase_functions_admin";
GRANT ALL ON FUNCTION "public"."revoke_user_sessions"() TO "supabase_storage_admin";
-- GRANT ALL ON FUNCTION "public"."revoke_user_sessions"() TO "supabase_admin";
GRANT ALL ON FUNCTION "public"."revoke_user_sessions"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."revoke_user_sessions"() TO "anon";
GRANT ALL ON FUNCTION "public"."revoke_user_sessions"() TO "service_role";
GRANT ALL ON FUNCTION "public"."set_banned_until"() TO "supabase_replication_admin";
GRANT ALL ON FUNCTION "public"."set_banned_until"() TO "supabase_read_only_user";
GRANT ALL ON FUNCTION "public"."set_banned_until"() TO "dashboard_user";
GRANT ALL ON FUNCTION "public"."set_banned_until"() TO "authenticator";
GRANT ALL ON FUNCTION "public"."set_banned_until"() TO "supabase_auth_admin";
GRANT ALL ON FUNCTION "public"."set_banned_until"() TO "supabase_functions_admin";
GRANT ALL ON FUNCTION "public"."set_banned_until"() TO "supabase_storage_admin";
-- GRANT ALL ON FUNCTION "public"."set_banned_until"() TO "supabase_admin";
GRANT ALL ON FUNCTION "public"."set_banned_until"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."set_banned_until"() TO "anon";
GRANT ALL ON FUNCTION "public"."set_banned_until"() TO "service_role";
GRANT ALL ON FUNCTION "public"."set_first_response_time"() TO "supabase_replication_admin";
GRANT ALL ON FUNCTION "public"."set_first_response_time"() TO "supabase_read_only_user";
GRANT ALL ON FUNCTION "public"."set_first_response_time"() TO "dashboard_user";
GRANT ALL ON FUNCTION "public"."set_first_response_time"() TO "authenticator";
GRANT ALL ON FUNCTION "public"."set_first_response_time"() TO "supabase_auth_admin";
GRANT ALL ON FUNCTION "public"."set_first_response_time"() TO "supabase_functions_admin";
GRANT ALL ON FUNCTION "public"."set_first_response_time"() TO "supabase_storage_admin";
-- GRANT ALL ON FUNCTION "public"."set_first_response_time"() TO "supabase_admin";
GRANT ALL ON FUNCTION "public"."set_first_response_time"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."set_first_response_time"() TO "anon";
GRANT ALL ON FUNCTION "public"."set_first_response_time"() TO "service_role";
GRANT ALL ON FUNCTION "public"."trigger_log_changes"() TO "supabase_replication_admin";
GRANT ALL ON FUNCTION "public"."trigger_log_changes"() TO "supabase_read_only_user";
GRANT ALL ON FUNCTION "public"."trigger_log_changes"() TO "dashboard_user";
GRANT ALL ON FUNCTION "public"."trigger_log_changes"() TO "authenticator";
GRANT ALL ON FUNCTION "public"."trigger_log_changes"() TO "supabase_auth_admin";
GRANT ALL ON FUNCTION "public"."trigger_log_changes"() TO "supabase_functions_admin";
GRANT ALL ON FUNCTION "public"."trigger_log_changes"() TO "supabase_storage_admin";
-- GRANT ALL ON FUNCTION "public"."trigger_log_changes"() TO "supabase_admin";
GRANT ALL ON FUNCTION "public"."trigger_log_changes"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."trigger_log_changes"() TO "anon";
GRANT ALL ON FUNCTION "public"."trigger_log_changes"() TO "service_role";
GRANT ALL ON FUNCTION "public"."update_from_to"() TO "supabase_replication_admin";
GRANT ALL ON FUNCTION "public"."update_from_to"() TO "supabase_read_only_user";
GRANT ALL ON FUNCTION "public"."update_from_to"() TO "dashboard_user";
GRANT ALL ON FUNCTION "public"."update_from_to"() TO "authenticator";
GRANT ALL ON FUNCTION "public"."update_from_to"() TO "supabase_auth_admin";
GRANT ALL ON FUNCTION "public"."update_from_to"() TO "supabase_functions_admin";
GRANT ALL ON FUNCTION "public"."update_from_to"() TO "supabase_storage_admin";
-- GRANT ALL ON FUNCTION "public"."update_from_to"() TO "supabase_admin";
GRANT ALL ON FUNCTION "public"."update_from_to"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_from_to"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_from_to"() TO "service_role";
GRANT ALL ON FUNCTION "public"."update_visit_title_and_status"() TO "supabase_replication_admin";
GRANT ALL ON FUNCTION "public"."update_visit_title_and_status"() TO "supabase_read_only_user";
GRANT ALL ON FUNCTION "public"."update_visit_title_and_status"() TO "dashboard_user";
GRANT ALL ON FUNCTION "public"."update_visit_title_and_status"() TO "authenticator";
GRANT ALL ON FUNCTION "public"."update_visit_title_and_status"() TO "supabase_auth_admin";
GRANT ALL ON FUNCTION "public"."update_visit_title_and_status"() TO "supabase_functions_admin";
GRANT ALL ON FUNCTION "public"."update_visit_title_and_status"() TO "supabase_storage_admin";
-- GRANT ALL ON FUNCTION "public"."update_visit_title_and_status"() TO "supabase_admin";
GRANT ALL ON FUNCTION "public"."update_visit_title_and_status"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_visit_title_and_status"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_visit_title_and_status"() TO "service_role";
GRANT ALL ON FUNCTION "public"."user_trigger_function"() TO "service_role";
GRANT ALL ON FUNCTION "public"."user_trigger_function"() TO "supabase_replication_admin";
GRANT ALL ON FUNCTION "public"."user_trigger_function"() TO "supabase_read_only_user";
GRANT ALL ON FUNCTION "public"."user_trigger_function"() TO "dashboard_user";
GRANT ALL ON FUNCTION "public"."user_trigger_function"() TO "authenticator";
GRANT ALL ON FUNCTION "public"."user_trigger_function"() TO "supabase_auth_admin";
GRANT ALL ON FUNCTION "public"."user_trigger_function"() TO "supabase_functions_admin";
GRANT ALL ON FUNCTION "public"."user_trigger_function"() TO "supabase_storage_admin";
-- GRANT ALL ON FUNCTION "public"."user_trigger_function"() TO "supabase_admin";
GRANT ALL ON FUNCTION "public"."user_trigger_function"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."user_trigger_function"() TO "anon";
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "public"."_prisma_migrations" TO "anon";
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "public"."_prisma_migrations" TO "authenticated";
-- GRANT ALL ON TABLE "public"."_prisma_migrations" TO "supabase_admin";
GRANT SELECT ON TABLE "public"."_prisma_migrations" TO "supabase_replication_admin";
GRANT SELECT ON TABLE "public"."_prisma_migrations" TO "supabase_read_only_user";
GRANT ALL ON TABLE "public"."_prisma_migrations" TO "dashboard_user";
GRANT SELECT ON TABLE "public"."_prisma_migrations" TO "authenticator";
GRANT SELECT ON TABLE "public"."_prisma_migrations" TO "supabase_auth_admin";
GRANT SELECT ON TABLE "public"."_prisma_migrations" TO "supabase_functions_admin";
GRANT ALL ON TABLE "public"."_prisma_migrations" TO "supabase_storage_admin";
GRANT ALL ON TABLE "public"."_prisma_migrations" TO "service_role";
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "public"."activity_logs" TO "anon";
GRANT SELECT,INSERT,UPDATE ON TABLE "public"."activity_logs" TO "authenticated";
GRANT SELECT ON TABLE "public"."activity_logs" TO "supabase_replication_admin";
GRANT SELECT ON TABLE "public"."activity_logs" TO "supabase_read_only_user";
GRANT ALL ON TABLE "public"."activity_logs" TO "dashboard_user";
GRANT SELECT ON TABLE "public"."activity_logs" TO "authenticator";
GRANT SELECT ON TABLE "public"."activity_logs" TO "supabase_auth_admin";
GRANT SELECT ON TABLE "public"."activity_logs" TO "supabase_functions_admin";
GRANT ALL ON TABLE "public"."activity_logs" TO "supabase_storage_admin";
-- GRANT ALL ON TABLE "public"."activity_logs" TO "supabase_admin";
GRANT ALL ON TABLE "public"."activity_logs" TO "service_role";
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "public"."availability_times" TO "authenticated";
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "public"."availability_times" TO "anon";
-- GRANT ALL ON TABLE "public"."availability_times" TO "supabase_admin";
GRANT SELECT ON TABLE "public"."availability_times" TO "supabase_replication_admin";
GRANT SELECT ON TABLE "public"."availability_times" TO "supabase_read_only_user";
GRANT ALL ON TABLE "public"."availability_times" TO "dashboard_user";
GRANT SELECT ON TABLE "public"."availability_times" TO "authenticator";
GRANT SELECT ON TABLE "public"."availability_times" TO "supabase_auth_admin";
GRANT SELECT ON TABLE "public"."availability_times" TO "supabase_functions_admin";
GRANT ALL ON TABLE "public"."availability_times" TO "supabase_storage_admin";
GRANT ALL ON TABLE "public"."availability_times" TO "service_role";
GRANT SELECT,INSERT,DELETE ON TABLE "public"."function_logs" TO "anon";
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "public"."inmate_availability_settings" TO "authenticated";
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "public"."inmate_availability_settings" TO "anon";
-- GRANT ALL ON TABLE "public"."inmate_availability_settings" TO "supabase_admin";
GRANT SELECT ON TABLE "public"."inmate_availability_settings" TO "supabase_replication_admin";
GRANT SELECT ON TABLE "public"."inmate_availability_settings" TO "supabase_read_only_user";
GRANT ALL ON TABLE "public"."inmate_availability_settings" TO "dashboard_user";
GRANT SELECT ON TABLE "public"."inmate_availability_settings" TO "authenticator";
GRANT SELECT ON TABLE "public"."inmate_availability_settings" TO "supabase_auth_admin";
GRANT SELECT ON TABLE "public"."inmate_availability_settings" TO "supabase_functions_admin";
GRANT ALL ON TABLE "public"."inmate_availability_settings" TO "supabase_storage_admin";
GRANT ALL ON TABLE "public"."inmate_availability_settings" TO "service_role";
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "public"."inmate_settings" TO "authenticated";
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "public"."inmate_settings" TO "anon";
-- GRANT ALL ON TABLE "public"."inmate_settings" TO "supabase_admin";
GRANT SELECT ON TABLE "public"."inmate_settings" TO "supabase_replication_admin";
GRANT SELECT ON TABLE "public"."inmate_settings" TO "supabase_read_only_user";
GRANT ALL ON TABLE "public"."inmate_settings" TO "dashboard_user";
GRANT SELECT ON TABLE "public"."inmate_settings" TO "authenticator";
GRANT SELECT ON TABLE "public"."inmate_settings" TO "supabase_auth_admin";
GRANT SELECT ON TABLE "public"."inmate_settings" TO "supabase_functions_admin";
GRANT ALL ON TABLE "public"."inmate_settings" TO "supabase_storage_admin";
GRANT ALL ON TABLE "public"."inmate_settings" TO "service_role";
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "public"."inmate_visitor_documents" TO "authenticated";
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "public"."inmate_visitor_documents" TO "anon";
-- GRANT ALL ON TABLE "public"."inmate_visitor_documents" TO "supabase_admin";
GRANT SELECT ON TABLE "public"."inmate_visitor_documents" TO "supabase_replication_admin";
GRANT SELECT ON TABLE "public"."inmate_visitor_documents" TO "supabase_read_only_user";
GRANT ALL ON TABLE "public"."inmate_visitor_documents" TO "dashboard_user";
GRANT SELECT ON TABLE "public"."inmate_visitor_documents" TO "authenticator";
GRANT SELECT ON TABLE "public"."inmate_visitor_documents" TO "supabase_auth_admin";
GRANT SELECT ON TABLE "public"."inmate_visitor_documents" TO "supabase_functions_admin";
GRANT ALL ON TABLE "public"."inmate_visitor_documents" TO "supabase_storage_admin";
GRANT ALL ON TABLE "public"."inmate_visitor_documents" TO "service_role";
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "public"."inmate_visitors" TO "authenticated";
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "public"."inmate_visitors" TO "anon";
-- GRANT ALL ON TABLE "public"."inmate_visitors" TO "supabase_admin";
GRANT SELECT ON TABLE "public"."inmate_visitors" TO "supabase_replication_admin";
GRANT SELECT ON TABLE "public"."inmate_visitors" TO "supabase_read_only_user";
GRANT ALL ON TABLE "public"."inmate_visitors" TO "dashboard_user";
GRANT SELECT ON TABLE "public"."inmate_visitors" TO "authenticator";
GRANT SELECT ON TABLE "public"."inmate_visitors" TO "supabase_auth_admin";
GRANT SELECT ON TABLE "public"."inmate_visitors" TO "supabase_functions_admin";
GRANT ALL ON TABLE "public"."inmate_visitors" TO "supabase_storage_admin";
GRANT ALL ON TABLE "public"."inmate_visitors" TO "service_role";
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "public"."inmates" TO "authenticated";
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "public"."inmates" TO "anon";
-- GRANT ALL ON TABLE "public"."inmates" TO "supabase_admin";
GRANT SELECT ON TABLE "public"."inmates" TO "supabase_replication_admin";
GRANT SELECT ON TABLE "public"."inmates" TO "supabase_read_only_user";
GRANT ALL ON TABLE "public"."inmates" TO "dashboard_user";
GRANT SELECT ON TABLE "public"."inmates" TO "authenticator";
GRANT SELECT ON TABLE "public"."inmates" TO "supabase_auth_admin";
GRANT SELECT ON TABLE "public"."inmates" TO "supabase_functions_admin";
GRANT ALL ON TABLE "public"."inmates" TO "supabase_storage_admin";
GRANT ALL ON TABLE "public"."inmates" TO "service_role";
-- GRANT ALL ON TABLE "public"."inmates_status" TO "supabase_admin";
GRANT SELECT,INSERT,UPDATE ON TABLE "public"."inmates_status" TO "anon";
GRANT SELECT,INSERT,UPDATE ON TABLE "public"."inmates_status" TO "authenticated";
GRANT SELECT ON TABLE "public"."inmates_status" TO "supabase_replication_admin";
GRANT SELECT ON TABLE "public"."inmates_status" TO "supabase_read_only_user";
GRANT ALL ON TABLE "public"."inmates_status" TO "dashboard_user";
GRANT SELECT ON TABLE "public"."inmates_status" TO "authenticator";
GRANT SELECT ON TABLE "public"."inmates_status" TO "supabase_auth_admin";
GRANT SELECT ON TABLE "public"."inmates_status" TO "supabase_functions_admin";
GRANT ALL ON TABLE "public"."inmates_status" TO "supabase_storage_admin";
GRANT ALL ON TABLE "public"."inmates_status" TO "service_role";
-- GRANT SELECT,USAGE ON SEQUENCE "public"."inmates_status_id_seq" TO "supabase_admin";
GRANT ALL ON SEQUENCE "public"."inmates_status_id_seq" TO "service_role";
GRANT SELECT,USAGE ON SEQUENCE "public"."inmates_status_id_seq" TO "anon";
GRANT SELECT,USAGE ON SEQUENCE "public"."inmates_status_id_seq" TO "authenticated";
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "public"."meeting_feedbacks" TO "authenticated";
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "public"."meeting_feedbacks" TO "anon";
-- GRANT ALL ON TABLE "public"."meeting_feedbacks" TO "supabase_admin";
GRANT SELECT ON TABLE "public"."meeting_feedbacks" TO "supabase_replication_admin";
GRANT SELECT ON TABLE "public"."meeting_feedbacks" TO "supabase_read_only_user";
GRANT ALL ON TABLE "public"."meeting_feedbacks" TO "dashboard_user";
GRANT SELECT ON TABLE "public"."meeting_feedbacks" TO "authenticator";
GRANT SELECT ON TABLE "public"."meeting_feedbacks" TO "supabase_auth_admin";
GRANT SELECT ON TABLE "public"."meeting_feedbacks" TO "supabase_functions_admin";
GRANT ALL ON TABLE "public"."meeting_feedbacks" TO "supabase_storage_admin";
GRANT ALL ON TABLE "public"."meeting_feedbacks" TO "service_role";
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "public"."meeting_records" TO "authenticated";
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "public"."meeting_records" TO "anon";
-- GRANT ALL ON TABLE "public"."meeting_records" TO "supabase_admin";
GRANT SELECT ON TABLE "public"."meeting_records" TO "supabase_replication_admin";
GRANT SELECT ON TABLE "public"."meeting_records" TO "supabase_read_only_user";
GRANT ALL ON TABLE "public"."meeting_records" TO "dashboard_user";
GRANT SELECT ON TABLE "public"."meeting_records" TO "authenticator";
GRANT SELECT ON TABLE "public"."meeting_records" TO "supabase_auth_admin";
GRANT SELECT ON TABLE "public"."meeting_records" TO "supabase_functions_admin";
GRANT ALL ON TABLE "public"."meeting_records" TO "supabase_storage_admin";
GRANT ALL ON TABLE "public"."meeting_records" TO "service_role";
GRANT SELECT,INSERT,UPDATE ON TABLE "public"."notification_titles" TO "authenticated";
GRANT SELECT,INSERT,UPDATE ON TABLE "public"."notification_titles" TO "anon";
GRANT SELECT ON TABLE "public"."notification_titles" TO "supabase_replication_admin";
GRANT SELECT ON TABLE "public"."notification_titles" TO "supabase_read_only_user";
GRANT ALL ON TABLE "public"."notification_titles" TO "dashboard_user";
GRANT SELECT ON TABLE "public"."notification_titles" TO "authenticator";
GRANT SELECT ON TABLE "public"."notification_titles" TO "supabase_auth_admin";
GRANT SELECT ON TABLE "public"."notification_titles" TO "supabase_functions_admin";
GRANT ALL ON TABLE "public"."notification_titles" TO "supabase_storage_admin";
-- GRANT ALL ON TABLE "public"."notification_titles" TO "supabase_admin";
GRANT ALL ON TABLE "public"."notification_titles" TO "service_role";
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "public"."notifications" TO "authenticated";
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "public"."notifications" TO "anon";
-- GRANT ALL ON TABLE "public"."notifications" TO "supabase_admin";
GRANT SELECT ON TABLE "public"."notifications" TO "supabase_replication_admin";
GRANT SELECT ON TABLE "public"."notifications" TO "supabase_read_only_user";
GRANT ALL ON TABLE "public"."notifications" TO "dashboard_user";
GRANT SELECT ON TABLE "public"."notifications" TO "authenticator";
GRANT SELECT ON TABLE "public"."notifications" TO "supabase_auth_admin";
GRANT SELECT ON TABLE "public"."notifications" TO "supabase_functions_admin";
GRANT ALL ON TABLE "public"."notifications" TO "supabase_storage_admin";
GRANT ALL ON TABLE "public"."notifications" TO "service_role";
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "public"."permissions" TO "authenticated";
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "public"."permissions" TO "anon";
-- GRANT ALL ON TABLE "public"."permissions" TO "supabase_admin";
GRANT SELECT ON TABLE "public"."permissions" TO "supabase_replication_admin";
GRANT SELECT ON TABLE "public"."permissions" TO "supabase_read_only_user";
GRANT ALL ON TABLE "public"."permissions" TO "dashboard_user";
GRANT SELECT ON TABLE "public"."permissions" TO "authenticator";
GRANT SELECT ON TABLE "public"."permissions" TO "supabase_auth_admin";
GRANT SELECT ON TABLE "public"."permissions" TO "supabase_functions_admin";
GRANT ALL ON TABLE "public"."permissions" TO "supabase_storage_admin";
GRANT ALL ON TABLE "public"."permissions" TO "service_role";
GRANT SELECT,INSERT,UPDATE ON TABLE "public"."permissions_categories" TO "anon";
GRANT SELECT,INSERT,UPDATE ON TABLE "public"."permissions_categories" TO "authenticated";
GRANT SELECT ON TABLE "public"."permissions_categories" TO "supabase_replication_admin";
GRANT SELECT ON TABLE "public"."permissions_categories" TO "supabase_read_only_user";
GRANT ALL ON TABLE "public"."permissions_categories" TO "dashboard_user";
GRANT SELECT ON TABLE "public"."permissions_categories" TO "authenticator";
GRANT SELECT ON TABLE "public"."permissions_categories" TO "supabase_auth_admin";
GRANT SELECT ON TABLE "public"."permissions_categories" TO "supabase_functions_admin";
GRANT ALL ON TABLE "public"."permissions_categories" TO "supabase_storage_admin";
-- GRANT ALL ON TABLE "public"."permissions_categories" TO "supabase_admin";
GRANT ALL ON TABLE "public"."permissions_categories" TO "service_role";
GRANT ALL ON SEQUENCE "public"."permissions_categories_id_seq" TO "service_role";
GRANT SELECT,USAGE ON SEQUENCE "public"."permissions_categories_id_seq" TO "anon";
GRANT SELECT,USAGE ON SEQUENCE "public"."permissions_categories_id_seq" TO "authenticated";
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "public"."role_permissions" TO "authenticated";
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "public"."role_permissions" TO "anon";
-- GRANT ALL ON TABLE "public"."role_permissions" TO "supabase_admin";
GRANT SELECT ON TABLE "public"."role_permissions" TO "supabase_replication_admin";
GRANT SELECT ON TABLE "public"."role_permissions" TO "supabase_read_only_user";
GRANT ALL ON TABLE "public"."role_permissions" TO "dashboard_user";
GRANT SELECT ON TABLE "public"."role_permissions" TO "authenticator";
GRANT SELECT ON TABLE "public"."role_permissions" TO "supabase_auth_admin";
GRANT SELECT ON TABLE "public"."role_permissions" TO "supabase_functions_admin";
GRANT ALL ON TABLE "public"."role_permissions" TO "supabase_storage_admin";
GRANT ALL ON TABLE "public"."role_permissions" TO "service_role";
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "public"."roles" TO "authenticated";
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "public"."roles" TO "anon";
-- GRANT ALL ON TABLE "public"."roles" TO "supabase_admin";
GRANT ALL ON TABLE "public"."roles" TO "service_role";
GRANT SELECT ON TABLE "public"."roles" TO "supabase_replication_admin";
GRANT SELECT ON TABLE "public"."roles" TO "supabase_read_only_user";
GRANT ALL ON TABLE "public"."roles" TO "dashboard_user";
GRANT SELECT ON TABLE "public"."roles" TO "authenticator";
GRANT SELECT ON TABLE "public"."roles" TO "supabase_auth_admin";
GRANT SELECT ON TABLE "public"."roles" TO "supabase_functions_admin";
GRANT ALL ON TABLE "public"."roles" TO "supabase_storage_admin";
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "public"."room_availability_settings" TO "authenticated";
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "public"."room_availability_settings" TO "anon";
-- GRANT ALL ON TABLE "public"."room_availability_settings" TO "supabase_admin";
GRANT SELECT ON TABLE "public"."room_availability_settings" TO "supabase_replication_admin";
GRANT SELECT ON TABLE "public"."room_availability_settings" TO "supabase_read_only_user";
GRANT ALL ON TABLE "public"."room_availability_settings" TO "dashboard_user";
GRANT SELECT ON TABLE "public"."room_availability_settings" TO "authenticator";
GRANT SELECT ON TABLE "public"."room_availability_settings" TO "supabase_auth_admin";
GRANT SELECT ON TABLE "public"."room_availability_settings" TO "supabase_functions_admin";
GRANT ALL ON TABLE "public"."room_availability_settings" TO "supabase_storage_admin";
GRANT ALL ON TABLE "public"."room_availability_settings" TO "service_role";
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "public"."room_slots" TO "authenticated";
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "public"."room_slots" TO "anon";
-- GRANT ALL ON TABLE "public"."room_slots" TO "supabase_admin";
GRANT SELECT ON TABLE "public"."room_slots" TO "supabase_replication_admin";
GRANT SELECT ON TABLE "public"."room_slots" TO "supabase_read_only_user";
GRANT ALL ON TABLE "public"."room_slots" TO "dashboard_user";
GRANT SELECT ON TABLE "public"."room_slots" TO "authenticator";
GRANT SELECT ON TABLE "public"."room_slots" TO "supabase_auth_admin";
GRANT SELECT ON TABLE "public"."room_slots" TO "supabase_functions_admin";
GRANT ALL ON TABLE "public"."room_slots" TO "supabase_storage_admin";
GRANT ALL ON TABLE "public"."room_slots" TO "service_role";
-- GRANT ALL ON TABLE "public"."room_status" TO "supabase_admin";
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "public"."room_status" TO "anon";
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "public"."room_status" TO "authenticated";
GRANT SELECT ON TABLE "public"."room_status" TO "supabase_replication_admin";
GRANT SELECT ON TABLE "public"."room_status" TO "supabase_read_only_user";
GRANT ALL ON TABLE "public"."room_status" TO "dashboard_user";
GRANT SELECT ON TABLE "public"."room_status" TO "authenticator";
GRANT SELECT ON TABLE "public"."room_status" TO "supabase_auth_admin";
GRANT SELECT ON TABLE "public"."room_status" TO "supabase_functions_admin";
GRANT ALL ON TABLE "public"."room_status" TO "supabase_storage_admin";
GRANT ALL ON TABLE "public"."room_status" TO "service_role";
-- GRANT SELECT,USAGE ON SEQUENCE "public"."room_status_id_seq" TO "supabase_admin";
GRANT ALL ON SEQUENCE "public"."room_status_id_seq" TO "service_role";
GRANT SELECT,USAGE ON SEQUENCE "public"."room_status_id_seq" TO "anon";
GRANT SELECT,USAGE ON SEQUENCE "public"."room_status_id_seq" TO "authenticated";
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "public"."rooms" TO "authenticated";
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "public"."rooms" TO "anon";
-- GRANT ALL ON TABLE "public"."rooms" TO "supabase_admin";
GRANT SELECT ON TABLE "public"."rooms" TO "supabase_replication_admin";
GRANT SELECT ON TABLE "public"."rooms" TO "supabase_read_only_user";
GRANT ALL ON TABLE "public"."rooms" TO "dashboard_user";
GRANT SELECT ON TABLE "public"."rooms" TO "authenticator";
GRANT SELECT ON TABLE "public"."rooms" TO "supabase_auth_admin";
GRANT SELECT ON TABLE "public"."rooms" TO "supabase_functions_admin";
GRANT ALL ON TABLE "public"."rooms" TO "supabase_storage_admin";
GRANT ALL ON TABLE "public"."rooms" TO "service_role";
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "public"."section_availability_settings" TO "authenticated";
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "public"."section_availability_settings" TO "anon";
-- GRANT ALL ON TABLE "public"."section_availability_settings" TO "supabase_admin";
GRANT SELECT ON TABLE "public"."section_availability_settings" TO "supabase_replication_admin";
GRANT SELECT ON TABLE "public"."section_availability_settings" TO "supabase_read_only_user";
GRANT ALL ON TABLE "public"."section_availability_settings" TO "dashboard_user";
GRANT SELECT ON TABLE "public"."section_availability_settings" TO "authenticator";
GRANT SELECT ON TABLE "public"."section_availability_settings" TO "supabase_auth_admin";
GRANT SELECT ON TABLE "public"."section_availability_settings" TO "supabase_functions_admin";
GRANT ALL ON TABLE "public"."section_availability_settings" TO "supabase_storage_admin";
GRANT ALL ON TABLE "public"."section_availability_settings" TO "service_role";
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "public"."sections" TO "authenticated";
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "public"."sections" TO "anon";
-- GRANT ALL ON TABLE "public"."sections" TO "supabase_admin";
GRANT SELECT ON TABLE "public"."sections" TO "supabase_replication_admin";
GRANT SELECT ON TABLE "public"."sections" TO "supabase_read_only_user";
GRANT ALL ON TABLE "public"."sections" TO "dashboard_user";
GRANT SELECT ON TABLE "public"."sections" TO "authenticator";
GRANT SELECT ON TABLE "public"."sections" TO "supabase_auth_admin";
GRANT SELECT ON TABLE "public"."sections" TO "supabase_functions_admin";
GRANT ALL ON TABLE "public"."sections" TO "supabase_storage_admin";
GRANT ALL ON TABLE "public"."sections" TO "service_role";
GRANT SELECT ON TABLE "public"."servers" TO "supabase_replication_admin";
GRANT SELECT ON TABLE "public"."servers" TO "supabase_read_only_user";
GRANT ALL ON TABLE "public"."servers" TO "dashboard_user";
GRANT SELECT ON TABLE "public"."servers" TO "authenticator";
GRANT SELECT ON TABLE "public"."servers" TO "supabase_auth_admin";
GRANT SELECT ON TABLE "public"."servers" TO "supabase_functions_admin";
GRANT ALL ON TABLE "public"."servers" TO "supabase_storage_admin";
-- GRANT ALL ON TABLE "public"."servers" TO "supabase_admin";
GRANT SELECT,INSERT,UPDATE ON TABLE "public"."servers" TO "authenticated";
GRANT SELECT,INSERT,UPDATE ON TABLE "public"."servers" TO "anon";
GRANT ALL ON TABLE "public"."servers" TO "service_role";
GRANT ALL ON TABLE "public"."settings" TO "anon";
-- GRANT ALL ON TABLE "public"."settings" TO "supabase_admin";
GRANT ALL ON TABLE "public"."settings" TO "authenticated";
GRANT SELECT ON TABLE "public"."settings" TO "supabase_replication_admin";
GRANT SELECT ON TABLE "public"."settings" TO "supabase_read_only_user";
GRANT ALL ON TABLE "public"."settings" TO "dashboard_user";
GRANT SELECT ON TABLE "public"."settings" TO "authenticator";
GRANT SELECT ON TABLE "public"."settings" TO "supabase_auth_admin";
GRANT SELECT ON TABLE "public"."settings" TO "supabase_functions_admin";
GRANT ALL ON TABLE "public"."settings" TO "supabase_storage_admin";
GRANT ALL ON TABLE "public"."settings" TO "service_role";
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "public"."user_permissions" TO "authenticated";
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "public"."user_permissions" TO "anon";
-- GRANT ALL ON TABLE "public"."user_permissions" TO "supabase_admin";
GRANT SELECT ON TABLE "public"."user_permissions" TO "supabase_replication_admin";
GRANT SELECT ON TABLE "public"."user_permissions" TO "supabase_read_only_user";
GRANT ALL ON TABLE "public"."user_permissions" TO "dashboard_user";
GRANT SELECT ON TABLE "public"."user_permissions" TO "authenticator";
GRANT SELECT ON TABLE "public"."user_permissions" TO "supabase_auth_admin";
GRANT SELECT ON TABLE "public"."user_permissions" TO "supabase_functions_admin";
GRANT ALL ON TABLE "public"."user_permissions" TO "supabase_storage_admin";
GRANT ALL ON TABLE "public"."user_permissions" TO "service_role";
GRANT SELECT,DELETE ON TABLE "public"."users" TO "authenticator";
GRANT ALL ON TABLE "public"."users" TO "service_role";
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "public"."users" TO "authenticated";
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "public"."users" TO "anon";
-- GRANT ALL ON TABLE "public"."users" TO "supabase_admin";
GRANT SELECT ON TABLE "public"."users" TO "supabase_replication_admin";
GRANT SELECT ON TABLE "public"."users" TO "supabase_read_only_user";
GRANT ALL ON TABLE "public"."users" TO "dashboard_user";
GRANT SELECT ON TABLE "public"."users" TO "supabase_auth_admin";
GRANT SELECT ON TABLE "public"."users" TO "supabase_functions_admin";
GRANT ALL ON TABLE "public"."users" TO "supabase_storage_admin";
-- GRANT ALL ON TABLE "public"."users_notifications" TO "supabase_admin";
GRANT ALL ON TABLE "public"."users_notifications" TO "anon";
GRANT SELECT,INSERT,UPDATE ON TABLE "public"."users_notifications" TO "authenticated";
GRANT SELECT ON TABLE "public"."users_notifications" TO "supabase_replication_admin";
GRANT SELECT ON TABLE "public"."users_notifications" TO "supabase_read_only_user";
GRANT ALL ON TABLE "public"."users_notifications" TO "dashboard_user";
GRANT SELECT ON TABLE "public"."users_notifications" TO "authenticator";
GRANT SELECT ON TABLE "public"."users_notifications" TO "supabase_auth_admin";
GRANT SELECT ON TABLE "public"."users_notifications" TO "supabase_functions_admin";
GRANT ALL ON TABLE "public"."users_notifications" TO "supabase_storage_admin";
GRANT ALL ON TABLE "public"."users_notifications" TO "service_role";
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "public"."visit_requests" TO "authenticated";
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "public"."visit_requests" TO "anon";
-- GRANT ALL ON TABLE "public"."visit_requests" TO "supabase_admin";
GRANT SELECT ON TABLE "public"."visit_requests" TO "supabase_replication_admin";
GRANT SELECT ON TABLE "public"."visit_requests" TO "supabase_read_only_user";
GRANT ALL ON TABLE "public"."visit_requests" TO "dashboard_user";
GRANT SELECT ON TABLE "public"."visit_requests" TO "authenticator";
GRANT SELECT ON TABLE "public"."visit_requests" TO "supabase_auth_admin";
GRANT SELECT ON TABLE "public"."visit_requests" TO "supabase_functions_admin";
GRANT ALL ON TABLE "public"."visit_requests" TO "supabase_storage_admin";
GRANT ALL ON TABLE "public"."visit_requests" TO "service_role";
-- GRANT ALL ON TABLE "public"."visit_requests_status" TO "supabase_admin";
GRANT SELECT,INSERT,UPDATE ON TABLE "public"."visit_requests_status" TO "anon";
GRANT SELECT,INSERT,UPDATE ON TABLE "public"."visit_requests_status" TO "authenticated";
GRANT SELECT ON TABLE "public"."visit_requests_status" TO "supabase_replication_admin";
GRANT SELECT ON TABLE "public"."visit_requests_status" TO "supabase_read_only_user";
GRANT ALL ON TABLE "public"."visit_requests_status" TO "dashboard_user";
GRANT SELECT ON TABLE "public"."visit_requests_status" TO "authenticator";
GRANT SELECT ON TABLE "public"."visit_requests_status" TO "supabase_auth_admin";
GRANT SELECT ON TABLE "public"."visit_requests_status" TO "supabase_functions_admin";
GRANT ALL ON TABLE "public"."visit_requests_status" TO "supabase_storage_admin";
GRANT ALL ON TABLE "public"."visit_requests_status" TO "service_role";
-- GRANT SELECT,USAGE ON SEQUENCE "public"."visit_requests_status_id_seq" TO "supabase_admin";
GRANT ALL ON SEQUENCE "public"."visit_requests_status_id_seq" TO "service_role";
GRANT SELECT,USAGE ON SEQUENCE "public"."visit_requests_status_id_seq" TO "anon";
GRANT SELECT,USAGE ON SEQUENCE "public"."visit_requests_status_id_seq" TO "authenticated";
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "public"."visitors" TO "authenticated";
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "public"."visitors" TO "anon";
-- GRANT ALL ON TABLE "public"."visitors" TO "supabase_admin";
GRANT SELECT ON TABLE "public"."visitors" TO "supabase_replication_admin";
GRANT SELECT ON TABLE "public"."visitors" TO "supabase_read_only_user";
GRANT ALL ON TABLE "public"."visitors" TO "dashboard_user";
GRANT SELECT ON TABLE "public"."visitors" TO "authenticator";
GRANT SELECT ON TABLE "public"."visitors" TO "supabase_auth_admin";
GRANT SELECT ON TABLE "public"."visitors" TO "supabase_functions_admin";
GRANT ALL ON TABLE "public"."visitors" TO "supabase_storage_admin";
GRANT ALL ON TABLE "public"."visitors" TO "service_role";
-- GRANT SELECT,USAGE ON SEQUENCE "public"."visit_requests_visit_number_seq" TO "supabase_admin";
GRANT ALL ON SEQUENCE "public"."visit_requests_visit_number_seq" TO "service_role";
GRANT SELECT,USAGE ON SEQUENCE "public"."visit_requests_visit_number_seq" TO "anon";
GRANT SELECT,USAGE ON SEQUENCE "public"."visit_requests_visit_number_seq" TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT SELECT ON SEQUENCES  TO PUBLIC;
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO PUBLIC;
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT SELECT ON TABLES  TO PUBLIC;
RESET ALL;
