drop trigger if exists "trigger_disable_auth_user" on "public"."users";

drop trigger if exists "trigger_disable_auth_user_and_remove_identity" on "public"."users";

drop function if exists "public"."disable_auth_user_and_remove_identity"();

drop function if exists "public"."disable_auth_user_on_delete"();

-- drop function if exists "public"."revoke_user_sessions"();

drop view if exists "public"."visit_requests_view";

drop index if exists "public"."unique_role_name";

alter type "public"."inmate_visitor_relation" rename to "inmate_visitor_relation__old_version_to_be_dropped";

create type "public"."inmate_visitor_relation" as enum ('MOTHER', 'FATHER', 'BROTHER', 'SISTER', 'SON', 'DAUGHTER', 'G<PERSON><PERSON>FATHER', 'G<PERSON><PERSON>MOTHER', 'G<PERSON><PERSON>SON', '<PERSON><PERSON><PERSON><PERSON>UGHTER', '<PERSON>CLE', 'AUNT', 'NEPHEW', 'NIECE', 'COUSIN', '<PERSON><PERSON><PERSON>_UNCLE', 'G<PERSON>AT_AUNT', 'GREAT_NEPHEW', 'GREAT_NIECE', 'HUSBAND', 'WIFE', 'LAWYER', '<PERSON>EG<PERSON>_REPRESENTATIVE', 'GOVERNMENT_OFFICIAL', 'SOCIAL_WORKER', 'OTHER');

create table "public"."schema_migrations" (
    "version" bigint not null,
    "inserted_at" timestamp(0) without time zone
);


create table "public"."tenants" (
    "id" uuid not null default gen_random_uuid(),
    "external_id" text not null,
    "name" text not null,
    "jwt_secret" text not null,
    "postgres_cdc_default" boolean default true,
    "inserted_at" timestamp without time zone default now(),
    "updated_at" timestamp without time zone default now(),
    "max_concurrent_users" integer default 200,
    "max_events_per_second" integer default 100,
    "max_bytes_per_second" integer default 100000,
    "max_channels_per_client" integer default 100,
    "max_joins_per_second" integer default 100,
    "suspend" boolean default false
);


alter table "public"."inmate_visitors" alter column relation type "public"."inmate_visitor_relation" using relation::text::"public"."inmate_visitor_relation";

drop type "public"."inmate_visitor_relation__old_version_to_be_dropped";

alter table "public"."function_logs" alter column "timestamp" set data type timestamp without time zone using "timestamp"::timestamp without time zone;

alter table "public"."inmate_visitors" add column "requested_inmate_name" text;

alter table "public"."notifications" alter column "datetime" set data type timestamp without time zone using "datetime"::timestamp without time zone;

alter table "public"."users_notifications" alter column "created_at" set data type timestamp without time zone using "created_at"::timestamp without time zone;

alter table "public"."visit_requests" alter column "created_at" set default now();

alter table "public"."visit_requests" alter column "created_at" set data type timestamp without time zone using "created_at"::timestamp without time zone;

alter table "public"."visit_requests" alter column "datetime" set data type timestamp without time zone using "datetime"::timestamp without time zone;

alter table "public"."visit_requests" alter column "from" set data type time without time zone using "from"::time without time zone;

alter table "public"."visit_requests" alter column "to" set data type time without time zone using "to"::time without time zone;

alter table "public"."visit_requests" alter column "updated_at" set data type timestamp without time zone using "updated_at"::timestamp without time zone;

alter table "public"."visit_requests" alter column "user_id" drop default;

alter table "public"."visitors" add column "first_name" text;

alter table "public"."visitors" add column "last_name" text;

CREATE UNIQUE INDEX schema_migrations_pkey ON public.schema_migrations USING btree (version);

CREATE UNIQUE INDEX tenants_pkey ON public.tenants USING btree (id);

alter table "public"."schema_migrations" add constraint "schema_migrations_pkey" PRIMARY KEY using index "schema_migrations_pkey";

alter table "public"."tenants" add constraint "tenants_pkey" PRIMARY KEY using index "tenants_pkey";

alter table "public"."visit_requests" add constraint "visit_requests_user_id_fkey" FOREIGN KEY (user_id) REFERENCES users(user_auth_id) not valid;

alter table "public"."visit_requests" validate constraint "visit_requests_user_id_fkey";

set check_function_bodies = off;

create or replace view "public"."role_permissions_view" as  SELECT rp.id AS role_permission_id,
    r.id AS role_id,
    r.name AS role_name,
    p.id AS permission_id,
    p.name AS permission_name
   FROM ((role_permissions rp
     JOIN roles r ON ((rp.role_id = r.id)))
     JOIN permissions p ON ((rp.permission_id = p.id)));


CREATE OR REPLACE FUNCTION public.update_visit_request_status(request_id uuid, status_name text)
 RETURNS void
 LANGUAGE plpgsql
AS $function$
begin
  update visit_requests
  set status_id = s.id,
      started_at = now()
  from visit_requests_status s
  where visit_requests.id = request_id
    and s.name = name;
end;
$function$
;

CREATE OR REPLACE FUNCTION public.update_visit_request_status_vtext(visit_request_id text, status_name text)
 RETURNS void
 LANGUAGE plpgsql
AS $function$
begin
    -- Update the visit request status by finding the correct status ID
    update visit_requests
    set status_id = (
            select id
            from visit_requests_status
            where name = status_name
            limit 1
    ),
    -- Set 'started_at' when the status is 'running'
    started_at = case
        when status_name = 'running' then current_timestamp
        else started_at
    end,
    -- Set 'ended_at' when the status is 'completed'
    ended_at = case
        when status_name = 'completed' then current_timestamp
        else ended_at
    end
    where id = visit_request_id;
end;
$function$
;

CREATE OR REPLACE FUNCTION public.delete_user_from_public()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
BEGIN
-- Delete the user from public.users where the email matches
DELETE FROM public."users" WHERE email = OLD.email;
RETURN OLD;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.generate_user_notification()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
DECLARE
    title_id_var int; -- Changed variable name to avoid ambiguity
    sla_due interval;
BEGIN
    -- Handle new visitation request on INSERT
    IF TG_OP = 'INSERT' THEN
        title_id_var := 1;
    ELSE
        -- Handle status updates on UPDATE
        CASE NEW.status_id
            WHEN 2 THEN
                -- Check if the SLA is exceeded
                SELECT value::interval INTO sla_due
                FROM settings
                WHERE settings.module = 'sla' AND settings.key = 'request_response_due';

                IF NOW() - NEW.created_at > sla_due THEN
                    title_id_var := 14; -- SLA exceeded
                ELSE
                    title_id_var := 2; -- Normal status update
                END IF;
            WHEN 10 THEN
                title_id_var := 10;
            WHEN 9 THEN
                title_id_var := 9;
            WHEN 8 THEN
                title_id_var := 8;
            WHEN 6 THEN
                IF NEW.datetime + INTERVAL '1 minute' * NEW.duration > now()
                THEN
                    title_id_var := 55; -- Visit in progress
                ELSE
                    title_id_var := 6; -- Visit ended
                END IF;
            WHEN 5 THEN
                title_id_var := 5;
            WHEN 3 THEN
                title_id_var := 3;
            ELSE
                -- If status_id is not one of the specified values, do nothing
                RETURN NEW;
        END CASE;
        
        -- Check if the status has actually changed
        IF OLD.status_id = NEW.status_id THEN
            -- Status didn't change, don't create notification
            RETURN NEW;
        END IF;
    END IF;

    -- Insert notifications for all users (with duplicate prevention)
    INSERT INTO users_notifications (user_id, visitor_id, visit_id, title_id, created_at, seen)
    SELECT user_auth_id, NEW.visitor_id, NEW.id, title_id_var, NOW(), NULL
    FROM users u
    WHERE u.deleted_at IS NULL
    AND NOT EXISTS (
        SELECT 1 
        FROM users_notifications un 
        WHERE un.user_id = u.user_auth_id 
          AND un.visit_id = NEW.id 
          AND un.title_id = title_id_var
    );

    RETURN NEW;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.notify_visit_status_change()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
DECLARE
status_name TEXT;
BEGIN
    -- Get the name of the status
SELECT name INTO status_name FROM visit_requests_status WHERE id = NEW.status_id;

-- Insert a notification only if the status_id has changed
IF OLD.status_id IS DISTINCT FROM NEW.status_id THEN
        INSERT INTO notifications (id, datetime, visitor_id, visit_id, message, seen)
        VALUES (
            gen_random_uuid(),
            NOW(),
            NEW.visitor_id,
            NEW.id,
            status_name,
            FALSE
        );
END IF;

RETURN NEW;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.set_banned_until()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$BEGIN
    -- Update the auth.users table to set banned_until when deleted_at is set
    UPDATE auth.users 
    SET banned_until = (NOW() + INTERVAL '1 year')
    WHERE id = NEW.user_auth_id;

    RETURN NEW;
END;$function$
;

grant delete on table "public"."_prisma_migrations" to "supabase_admin";

grant insert on table "public"."_prisma_migrations" to "supabase_admin";

grant references on table "public"."_prisma_migrations" to "supabase_admin";

grant select on table "public"."_prisma_migrations" to "supabase_admin";

grant trigger on table "public"."_prisma_migrations" to "supabase_admin";

grant truncate on table "public"."_prisma_migrations" to "supabase_admin";

grant update on table "public"."_prisma_migrations" to "supabase_admin";

grant delete on table "public"."activity_logs" to "supabase_admin";

grant insert on table "public"."activity_logs" to "supabase_admin";

grant references on table "public"."activity_logs" to "supabase_admin";

grant select on table "public"."activity_logs" to "supabase_admin";

grant trigger on table "public"."activity_logs" to "supabase_admin";

grant truncate on table "public"."activity_logs" to "supabase_admin";

grant update on table "public"."activity_logs" to "supabase_admin";

grant delete on table "public"."availability_times" to "supabase_admin";

grant insert on table "public"."availability_times" to "supabase_admin";

grant references on table "public"."availability_times" to "supabase_admin";

grant select on table "public"."availability_times" to "supabase_admin";

grant trigger on table "public"."availability_times" to "supabase_admin";

grant truncate on table "public"."availability_times" to "supabase_admin";

grant update on table "public"."availability_times" to "supabase_admin";

grant delete on table "public"."inmate_availability_settings" to "supabase_admin";

grant insert on table "public"."inmate_availability_settings" to "supabase_admin";

grant references on table "public"."inmate_availability_settings" to "supabase_admin";

grant select on table "public"."inmate_availability_settings" to "supabase_admin";

grant trigger on table "public"."inmate_availability_settings" to "supabase_admin";

grant truncate on table "public"."inmate_availability_settings" to "supabase_admin";

grant update on table "public"."inmate_availability_settings" to "supabase_admin";

grant delete on table "public"."inmate_settings" to "supabase_admin";

grant insert on table "public"."inmate_settings" to "supabase_admin";

grant references on table "public"."inmate_settings" to "supabase_admin";

grant select on table "public"."inmate_settings" to "supabase_admin";

grant trigger on table "public"."inmate_settings" to "supabase_admin";

grant truncate on table "public"."inmate_settings" to "supabase_admin";

grant update on table "public"."inmate_settings" to "supabase_admin";

grant delete on table "public"."inmate_visitor_documents" to "supabase_admin";

grant insert on table "public"."inmate_visitor_documents" to "supabase_admin";

grant references on table "public"."inmate_visitor_documents" to "supabase_admin";

grant select on table "public"."inmate_visitor_documents" to "supabase_admin";

grant trigger on table "public"."inmate_visitor_documents" to "supabase_admin";

grant truncate on table "public"."inmate_visitor_documents" to "supabase_admin";

grant update on table "public"."inmate_visitor_documents" to "supabase_admin";

grant delete on table "public"."inmate_visitors" to "supabase_admin";

grant insert on table "public"."inmate_visitors" to "supabase_admin";

grant references on table "public"."inmate_visitors" to "supabase_admin";

grant select on table "public"."inmate_visitors" to "supabase_admin";

grant trigger on table "public"."inmate_visitors" to "supabase_admin";

grant truncate on table "public"."inmate_visitors" to "supabase_admin";

grant update on table "public"."inmate_visitors" to "supabase_admin";

grant delete on table "public"."inmates" to "supabase_admin";

grant insert on table "public"."inmates" to "supabase_admin";

grant references on table "public"."inmates" to "supabase_admin";

grant select on table "public"."inmates" to "supabase_admin";

grant trigger on table "public"."inmates" to "supabase_admin";

grant truncate on table "public"."inmates" to "supabase_admin";

grant update on table "public"."inmates" to "supabase_admin";

grant delete on table "public"."inmates_status" to "supabase_admin";

grant insert on table "public"."inmates_status" to "supabase_admin";

grant references on table "public"."inmates_status" to "supabase_admin";

grant select on table "public"."inmates_status" to "supabase_admin";

grant trigger on table "public"."inmates_status" to "supabase_admin";

grant truncate on table "public"."inmates_status" to "supabase_admin";

grant update on table "public"."inmates_status" to "supabase_admin";

grant delete on table "public"."meeting_feedbacks" to "supabase_admin";

grant insert on table "public"."meeting_feedbacks" to "supabase_admin";

grant references on table "public"."meeting_feedbacks" to "supabase_admin";

grant select on table "public"."meeting_feedbacks" to "supabase_admin";

grant trigger on table "public"."meeting_feedbacks" to "supabase_admin";

grant truncate on table "public"."meeting_feedbacks" to "supabase_admin";

grant update on table "public"."meeting_feedbacks" to "supabase_admin";

grant delete on table "public"."meeting_records" to "supabase_admin";

grant insert on table "public"."meeting_records" to "supabase_admin";

grant references on table "public"."meeting_records" to "supabase_admin";

grant select on table "public"."meeting_records" to "supabase_admin";

grant trigger on table "public"."meeting_records" to "supabase_admin";

grant truncate on table "public"."meeting_records" to "supabase_admin";

grant update on table "public"."meeting_records" to "supabase_admin";

grant delete on table "public"."notification_titles" to "supabase_admin";

grant insert on table "public"."notification_titles" to "supabase_admin";

grant references on table "public"."notification_titles" to "supabase_admin";

grant select on table "public"."notification_titles" to "supabase_admin";

grant trigger on table "public"."notification_titles" to "supabase_admin";

grant truncate on table "public"."notification_titles" to "supabase_admin";

grant update on table "public"."notification_titles" to "supabase_admin";

grant delete on table "public"."notifications" to "supabase_admin";

grant insert on table "public"."notifications" to "supabase_admin";

grant references on table "public"."notifications" to "supabase_admin";

grant select on table "public"."notifications" to "supabase_admin";

grant trigger on table "public"."notifications" to "supabase_admin";

grant truncate on table "public"."notifications" to "supabase_admin";

grant update on table "public"."notifications" to "supabase_admin";

grant delete on table "public"."permissions" to "supabase_admin";

grant insert on table "public"."permissions" to "supabase_admin";

grant references on table "public"."permissions" to "supabase_admin";

grant select on table "public"."permissions" to "supabase_admin";

grant trigger on table "public"."permissions" to "supabase_admin";

grant truncate on table "public"."permissions" to "supabase_admin";

grant update on table "public"."permissions" to "supabase_admin";

grant delete on table "public"."permissions_categories" to "supabase_admin";

grant insert on table "public"."permissions_categories" to "supabase_admin";

grant references on table "public"."permissions_categories" to "supabase_admin";

grant select on table "public"."permissions_categories" to "supabase_admin";

grant trigger on table "public"."permissions_categories" to "supabase_admin";

grant truncate on table "public"."permissions_categories" to "supabase_admin";

grant update on table "public"."permissions_categories" to "supabase_admin";

grant delete on table "public"."role_permissions" to "supabase_admin";

grant insert on table "public"."role_permissions" to "supabase_admin";

grant references on table "public"."role_permissions" to "supabase_admin";

grant select on table "public"."role_permissions" to "supabase_admin";

grant trigger on table "public"."role_permissions" to "supabase_admin";

grant truncate on table "public"."role_permissions" to "supabase_admin";

grant update on table "public"."role_permissions" to "supabase_admin";

grant delete on table "public"."roles" to "supabase_admin";

grant insert on table "public"."roles" to "supabase_admin";

grant references on table "public"."roles" to "supabase_admin";

grant select on table "public"."roles" to "supabase_admin";

grant trigger on table "public"."roles" to "supabase_admin";

grant truncate on table "public"."roles" to "supabase_admin";

grant update on table "public"."roles" to "supabase_admin";

grant delete on table "public"."room_availability_settings" to "supabase_admin";

grant insert on table "public"."room_availability_settings" to "supabase_admin";

grant references on table "public"."room_availability_settings" to "supabase_admin";

grant select on table "public"."room_availability_settings" to "supabase_admin";

grant trigger on table "public"."room_availability_settings" to "supabase_admin";

grant truncate on table "public"."room_availability_settings" to "supabase_admin";

grant update on table "public"."room_availability_settings" to "supabase_admin";

grant delete on table "public"."room_slots" to "supabase_admin";

grant insert on table "public"."room_slots" to "supabase_admin";

grant references on table "public"."room_slots" to "supabase_admin";

grant select on table "public"."room_slots" to "supabase_admin";

grant trigger on table "public"."room_slots" to "supabase_admin";

grant truncate on table "public"."room_slots" to "supabase_admin";

grant update on table "public"."room_slots" to "supabase_admin";

grant delete on table "public"."room_status" to "supabase_admin";

grant insert on table "public"."room_status" to "supabase_admin";

grant references on table "public"."room_status" to "supabase_admin";

grant select on table "public"."room_status" to "supabase_admin";

grant trigger on table "public"."room_status" to "supabase_admin";

grant truncate on table "public"."room_status" to "supabase_admin";

grant update on table "public"."room_status" to "supabase_admin";

grant delete on table "public"."rooms" to "supabase_admin";

grant insert on table "public"."rooms" to "supabase_admin";

grant references on table "public"."rooms" to "supabase_admin";

grant select on table "public"."rooms" to "supabase_admin";

grant trigger on table "public"."rooms" to "supabase_admin";

grant truncate on table "public"."rooms" to "supabase_admin";

grant update on table "public"."rooms" to "supabase_admin";

grant delete on table "public"."schema_migrations" to "anon";

grant insert on table "public"."schema_migrations" to "anon";

grant references on table "public"."schema_migrations" to "anon";

grant select on table "public"."schema_migrations" to "anon";

grant trigger on table "public"."schema_migrations" to "anon";

grant truncate on table "public"."schema_migrations" to "anon";

grant update on table "public"."schema_migrations" to "anon";

grant delete on table "public"."schema_migrations" to "authenticated";

grant insert on table "public"."schema_migrations" to "authenticated";

grant references on table "public"."schema_migrations" to "authenticated";

grant select on table "public"."schema_migrations" to "authenticated";

grant trigger on table "public"."schema_migrations" to "authenticated";

grant truncate on table "public"."schema_migrations" to "authenticated";

grant update on table "public"."schema_migrations" to "authenticated";

grant delete on table "public"."schema_migrations" to "service_role";

grant insert on table "public"."schema_migrations" to "service_role";

grant references on table "public"."schema_migrations" to "service_role";

grant select on table "public"."schema_migrations" to "service_role";

grant trigger on table "public"."schema_migrations" to "service_role";

grant truncate on table "public"."schema_migrations" to "service_role";

grant update on table "public"."schema_migrations" to "service_role";

grant delete on table "public"."section_availability_settings" to "supabase_admin";

grant insert on table "public"."section_availability_settings" to "supabase_admin";

grant references on table "public"."section_availability_settings" to "supabase_admin";

grant select on table "public"."section_availability_settings" to "supabase_admin";

grant trigger on table "public"."section_availability_settings" to "supabase_admin";

grant truncate on table "public"."section_availability_settings" to "supabase_admin";

grant update on table "public"."section_availability_settings" to "supabase_admin";

grant delete on table "public"."sections" to "supabase_admin";

grant insert on table "public"."sections" to "supabase_admin";

grant references on table "public"."sections" to "supabase_admin";

grant select on table "public"."sections" to "supabase_admin";

grant trigger on table "public"."sections" to "supabase_admin";

grant truncate on table "public"."sections" to "supabase_admin";

grant update on table "public"."sections" to "supabase_admin";

grant delete on table "public"."servers" to "supabase_admin";

grant insert on table "public"."servers" to "supabase_admin";

grant references on table "public"."servers" to "supabase_admin";

grant select on table "public"."servers" to "supabase_admin";

grant trigger on table "public"."servers" to "supabase_admin";

grant truncate on table "public"."servers" to "supabase_admin";

grant update on table "public"."servers" to "supabase_admin";

grant delete on table "public"."settings" to "supabase_admin";

grant insert on table "public"."settings" to "supabase_admin";

grant references on table "public"."settings" to "supabase_admin";

grant select on table "public"."settings" to "supabase_admin";

grant trigger on table "public"."settings" to "supabase_admin";

grant truncate on table "public"."settings" to "supabase_admin";

grant update on table "public"."settings" to "supabase_admin";

-- grant select on table "public"."tenants" to "PUBLIC";

grant delete on table "public"."tenants" to "anon";

grant insert on table "public"."tenants" to "anon";

grant references on table "public"."tenants" to "anon";

grant select on table "public"."tenants" to "anon";

grant trigger on table "public"."tenants" to "anon";

grant truncate on table "public"."tenants" to "anon";

grant update on table "public"."tenants" to "anon";

grant delete on table "public"."tenants" to "authenticated";

grant insert on table "public"."tenants" to "authenticated";

grant references on table "public"."tenants" to "authenticated";

grant select on table "public"."tenants" to "authenticated";

grant trigger on table "public"."tenants" to "authenticated";

grant truncate on table "public"."tenants" to "authenticated";

grant update on table "public"."tenants" to "authenticated";

grant delete on table "public"."tenants" to "service_role";

grant insert on table "public"."tenants" to "service_role";

grant references on table "public"."tenants" to "service_role";

grant select on table "public"."tenants" to "service_role";

grant trigger on table "public"."tenants" to "service_role";

grant truncate on table "public"."tenants" to "service_role";

grant update on table "public"."tenants" to "service_role";

grant delete on table "public"."user_permissions" to "supabase_admin";

grant insert on table "public"."user_permissions" to "supabase_admin";

grant references on table "public"."user_permissions" to "supabase_admin";

grant select on table "public"."user_permissions" to "supabase_admin";

grant trigger on table "public"."user_permissions" to "supabase_admin";

grant truncate on table "public"."user_permissions" to "supabase_admin";

grant update on table "public"."user_permissions" to "supabase_admin";

grant delete on table "public"."users" to "supabase_admin";

grant insert on table "public"."users" to "supabase_admin";

grant references on table "public"."users" to "supabase_admin";

grant select on table "public"."users" to "supabase_admin";

grant trigger on table "public"."users" to "supabase_admin";

grant truncate on table "public"."users" to "supabase_admin";

grant update on table "public"."users" to "supabase_admin";

grant delete on table "public"."users_notifications" to "supabase_admin";

grant insert on table "public"."users_notifications" to "supabase_admin";

grant references on table "public"."users_notifications" to "supabase_admin";

grant select on table "public"."users_notifications" to "supabase_admin";

grant trigger on table "public"."users_notifications" to "supabase_admin";

grant truncate on table "public"."users_notifications" to "supabase_admin";

grant update on table "public"."users_notifications" to "supabase_admin";

grant delete on table "public"."visit_requests" to "supabase_admin";

grant insert on table "public"."visit_requests" to "supabase_admin";

grant references on table "public"."visit_requests" to "supabase_admin";

grant select on table "public"."visit_requests" to "supabase_admin";

grant trigger on table "public"."visit_requests" to "supabase_admin";

grant truncate on table "public"."visit_requests" to "supabase_admin";

grant update on table "public"."visit_requests" to "supabase_admin";

grant delete on table "public"."visit_requests_status" to "supabase_admin";

grant insert on table "public"."visit_requests_status" to "supabase_admin";

grant references on table "public"."visit_requests_status" to "supabase_admin";

grant select on table "public"."visit_requests_status" to "supabase_admin";

grant trigger on table "public"."visit_requests_status" to "supabase_admin";

grant truncate on table "public"."visit_requests_status" to "supabase_admin";

grant update on table "public"."visit_requests_status" to "supabase_admin";

grant delete on table "public"."visitors" to "supabase_admin";

grant insert on table "public"."visitors" to "supabase_admin";

grant references on table "public"."visitors" to "supabase_admin";

grant select on table "public"."visitors" to "supabase_admin";

grant trigger on table "public"."visitors" to "supabase_admin";

grant truncate on table "public"."visitors" to "supabase_admin";

grant update on table "public"."visitors" to "supabase_admin";


