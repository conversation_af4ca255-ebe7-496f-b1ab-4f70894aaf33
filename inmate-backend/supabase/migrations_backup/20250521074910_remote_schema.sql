set check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.get_visitor_requests_with_meeting_records_not_archived()
 RETURNS TABLE(request_id text, request_status text, request_number text, inmate_full_name text, inmate_id text, visitor_name text, request_date timestamp without time zone, "from" time without time zone, "to" time without time zone, relation text, meeting_status text, meeting_date_time timestamp without time zone, record_id text, recording_url text, record_duration integer, archived boolean, record_size integer, feedback_rate integer, feedback_comment text)
 LANGUAGE plpgsql
AS $function$
BEGIN
  RETURN QUERY
  SELECT DISTINCT ON (mrec.id)
    vr.id,
    vr.status_id::TEXT,
    vr.visit_number::TEXT,
    inm.full_name AS inmate_full_name,
    vr.inmate_id,
    vis.full_name AS visitor_name,
    vr.datetime::timestamp WITHOUT TIME ZONE AS request_date,
    vr."from"::time WITHOUT TIME ZONE AS "from",
    vr."to"::time WITHOUT TIME ZONE AS "to",
    iv.relation::TEXT,
    vr.status_id::TEXT AS meeting_status,
    vr.datetime::timestamp WITHOUT TIME ZONE AS meeting_date_time,
    mrec.id,
    mrec.recording_url,
    mrec.duration AS record_duration,
    mrec.archived,
    mrec.record_size,
    feedback.rate,
    feedback.comment
  FROM public.visit_requests vr
    LEFT JOIN public.inmate_visitors iv
      ON vr.inmate_id = iv.inmate_id AND vr.visitor_id = iv.visitor_id
    LEFT JOIN public.inmates inm
      ON inm.id = vr.inmate_id
    JOIN public.visitors vis
      ON vis.id = vr.visitor_id
    JOIN public.meeting_records mrec
      ON mrec.visit_request_id = vr.id
    LEFT JOIN public.meeting_feedbacks feedback
      ON mrec.visit_request_id = feedback.visit_request_id
  WHERE mrec.deleted_at IS NULL
    AND mrec.archived = FALSE;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.get_visitor_visit_requests_with_meeting_records_not_archived()
 RETURNS TABLE(request_id text, request_status text, request_number text, inmate_full_name text, inmate_id text, visitor_name text, request_date timestamp without time zone, "from" time without time zone, "to" time without time zone, relation text, meeting_status text, meeting_date_time timestamp without time zone, record_id text, recording_url text, record_duration integer, archived boolean, record_size integer, feedback_rate integer, feedback_comment text)
 LANGUAGE plpgsql
AS $function$
BEGIN
  RETURN QUERY
  SELECT DISTINCT ON (mrec.id)
      vr.id::text,
      vr.status_id::text,
      vr.visit_number::text,
      inm.full_name,
      vr.inmate_id::text AS inmate_full_name,
      vis.full_name AS visitor_name,
      vr.datetime::timestamp without time zone AS request_date,  -- Explicit cast (optional)
      vr."from"::time AS "from",
      vr."to"::time AS "to",
      iv.relation::text,
      vr.status_id::text AS meeting_status,
      vr.datetime::timestamp without time zone AS meeting_date_time,  -- Explicit cast (optional)
      mrec.id::text,
      mrec.recording_url,
      mrec.duration AS record_duration,
      mrec.archived,
      mrec.record_size,
      feedback.rate,
      feedback.comment
  FROM public.visit_requests vr
      LEFT JOIN public.inmate_visitors iv
        ON vr.inmate_id = iv.inmate_id AND vr.visitor_id = iv.visitor_id
      LEFT JOIN public.inmates inm
        ON inm.id = vr.inmate_id
      JOIN public.visitors vis
        ON vis.id = vr.visitor_id
      JOIN public.meeting_records mrec
        ON mrec.visit_request_id = vr.id
      LEFT JOIN public.meeting_feedbacks feedback
        ON mrec.visit_request_id = feedback.visit_request_id
  WHERE mrec.deleted_at IS NULL
    AND mrec.archived = FALSE;
END;
$function$
;

grant delete on table "public"."overrides" to "postgres";

grant insert on table "public"."overrides" to "postgres";

grant references on table "public"."overrides" to "postgres";

grant select on table "public"."overrides" to "postgres";

grant trigger on table "public"."overrides" to "postgres";

grant truncate on table "public"."overrides" to "postgres";

grant update on table "public"."overrides" to "postgres";

grant delete on table "public"."overrides_timeslots" to "postgres";

grant insert on table "public"."overrides_timeslots" to "postgres";

grant references on table "public"."overrides_timeslots" to "postgres";

grant select on table "public"."overrides_timeslots" to "postgres";

grant trigger on table "public"."overrides_timeslots" to "postgres";

grant truncate on table "public"."overrides_timeslots" to "postgres";

grant update on table "public"."overrides_timeslots" to "postgres";

grant delete on table "public"."section_overrides" to "postgres";

grant insert on table "public"."section_overrides" to "postgres";

grant references on table "public"."section_overrides" to "postgres";

grant select on table "public"."section_overrides" to "postgres";

grant trigger on table "public"."section_overrides" to "postgres";

grant truncate on table "public"."section_overrides" to "postgres";

grant update on table "public"."section_overrides" to "postgres";

grant delete on table "public"."section_overrides_timeslots" to "postgres";

grant insert on table "public"."section_overrides_timeslots" to "postgres";

grant references on table "public"."section_overrides_timeslots" to "postgres";

grant select on table "public"."section_overrides_timeslots" to "postgres";

grant trigger on table "public"."section_overrides_timeslots" to "postgres";

grant truncate on table "public"."section_overrides_timeslots" to "postgres";

grant update on table "public"."section_overrides_timeslots" to "postgres";


