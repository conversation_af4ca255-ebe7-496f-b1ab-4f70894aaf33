

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;


CREATE SCHEMA IF NOT EXISTS "auth";


ALTER SCHEMA "auth" OWNER TO "supabase_admin";


CREATE SCHEMA IF NOT EXISTS "public";


ALTER SCHEMA "public" OWNER TO "postgres";


CREATE TYPE "auth"."aal_level" AS ENUM (
    'aal1',
    'aal2',
    'aal3'
);


ALTER TYPE "auth"."aal_level" OWNER TO "supabase_auth_admin";


CREATE TYPE "auth"."code_challenge_method" AS ENUM (
    's256',
    'plain'
);


ALTER TYPE "auth"."code_challenge_method" OWNER TO "supabase_auth_admin";


CREATE TYPE "auth"."factor_status" AS ENUM (
    'unverified',
    'verified'
);


ALTER TYPE "auth"."factor_status" OWNER TO "supabase_auth_admin";


CREATE TYPE "auth"."factor_type" AS ENUM (
    'totp',
    'webauthn',
    'phone'
);


ALTER TYPE "auth"."factor_type" OWNER TO "supabase_auth_admin";


CREATE TYPE "auth"."one_time_token_type" AS ENUM (
    'confirmation_token',
    'reauthentication_token',
    'recovery_token',
    'email_change_token_new',
    'email_change_token_current',
    'phone_change_token'
);


ALTER TYPE "auth"."one_time_token_type" OWNER TO "supabase_auth_admin";


CREATE TYPE "public"."InmateVitorRelationDegree" AS ENUM (
    'FIRST',
    'SECOND',
    'OTHERS',
    'THIRD'
);


ALTER TYPE "public"."InmateVitorRelationDegree" OWNER TO "postgres";


CREATE TYPE "public"."RoleName" AS ENUM (
    'SUPERADMIN',
    'ADMIN'
);


ALTER TYPE "public"."RoleName" OWNER TO "postgres";


CREATE TYPE "public"."availability_type" AS ENUM (
    'room',
    'section',
    'inmate'
);


ALTER TYPE "public"."availability_type" OWNER TO "postgres";


CREATE TYPE "public"."days" AS ENUM (
    'sunday',
    'monday',
    'tuesday',
    'wednesday',
    'thursday',
    'friday',
    'saturday'
);


ALTER TYPE "public"."days" OWNER TO "postgres";


CREATE TYPE "public"."inmate_status" AS ENUM (
    'AVAILABLE',
    'UNAVAILABLE',
    'SUSPENDED'
);


ALTER TYPE "public"."inmate_status" OWNER TO "postgres";


CREATE TYPE "public"."inmate_visitor_relation" AS ENUM (
    'MOTHER',
    'FATHER',
    'BROTHER',
    'SISTER',
    'SON',
    'DAUGHTER',
    'GRANDFATHER',
    'GRANDMOTHER',
    'GRANDSON',
    'GRANDDAUGHTER',
    'UNCLE',
    'AUNT',
    'NEPHEW',
    'NIECE',
    'COUSIN',
    'GREAT_UNCLE',
    'GREAT_AUNT',
    'GREAT_NEPHEW',
    'GREAT_NIECE',
    'HUSBAND',
    'WIFE',
    'LAWYER',
    'LEGAL_REPRESENTATIVE',
    'GOVERNMENT_OFFICIAL',
    'SOCIAL_WORKER',
    'OTHER'
);


ALTER TYPE "public"."inmate_visitor_relation" OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "auth"."email"() RETURNS "text"
    LANGUAGE "sql" STABLE
    AS $$
  select 
  coalesce(
    nullif(current_setting('request.jwt.claim.email', true), ''),
    (nullif(current_setting('request.jwt.claims', true), '')::jsonb ->> 'email')
  )::text
$$;


ALTER FUNCTION "auth"."email"() OWNER TO "supabase_auth_admin";


COMMENT ON FUNCTION "auth"."email"() IS 'Deprecated. Use auth.jwt() -> ''email'' instead.';



CREATE OR REPLACE FUNCTION "auth"."jwt"() RETURNS "jsonb"
    LANGUAGE "sql" STABLE
    AS $$
  select 
    coalesce(
        nullif(current_setting('request.jwt.claim', true), ''),
        nullif(current_setting('request.jwt.claims', true), '')
    )::jsonb
$$;


ALTER FUNCTION "auth"."jwt"() OWNER TO "supabase_auth_admin";


CREATE OR REPLACE FUNCTION "auth"."role"() RETURNS "text"
    LANGUAGE "sql" STABLE
    AS $$
  select 
  coalesce(
    nullif(current_setting('request.jwt.claim.role', true), ''),
    (nullif(current_setting('request.jwt.claims', true), '')::jsonb ->> 'role')
  )::text
$$;


ALTER FUNCTION "auth"."role"() OWNER TO "supabase_auth_admin";


COMMENT ON FUNCTION "auth"."role"() IS 'Deprecated. Use auth.jwt() -> ''role'' instead.';



CREATE OR REPLACE FUNCTION "auth"."uid"() RETURNS "uuid"
    LANGUAGE "sql" STABLE
    AS $$
  select 
  coalesce(
    nullif(current_setting('request.jwt.claim.sub', true), ''),
    (nullif(current_setting('request.jwt.claims', true), '')::jsonb ->> 'sub')
  )::uuid
$$;


ALTER FUNCTION "auth"."uid"() OWNER TO "supabase_auth_admin";


COMMENT ON FUNCTION "auth"."uid"() IS 'Deprecated. Use auth.jwt() -> ''sub'' instead.';



CREATE OR REPLACE FUNCTION "public"."approve_visit_if_whitelisted"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    IF EXISTS (
        SELECT 1
        FROM "Inmate_Visitor"
        WHERE inmate_id = NEW.inmate_id
        AND visitor_id = NEW.visitor_id
        AND is_white_list = TRUE
    ) THEN
        NEW.status := 'APPROVED';
    END IF;
    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."approve_visit_if_whitelisted"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."assign_room_to_visit"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
  -- Assign room_id based on criteria
  SELECT r.id
  INTO NEW.room_id
  FROM public."Room" r
  JOIN public."Inmate" i ON i.sectionid = r.sectionid
  WHERE r.active = true
    AND i.id = NEW.inmate_id
    AND NOT EXISTS (
      SELECT 1
      FROM public."Visit_Request" vr
      WHERE vr.room_id = r.id
        AND vr.datetime <= NEW.datetime + interval '1 minute' * NEW.duration
        AND vr.datetime + interval '1 minute' * vr.duration >= NEW.datetime
        AND (vr.status = 'APPROVED' OR vr.status = 'STARTS_SOON')
        AND vr."isDeleted" IS NULL
    )
  ORDER BY random() -- Randomly pick any available room
  LIMIT 1;

  -- If no room is found, raise an error
  IF NEW.room_id IS NULL THEN
    RAISE EXCEPTION 'No available room for the requested time slot.';
  END IF;

  RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."assign_room_to_visit"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."delete_user_from_public"() RETURNS "trigger"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
DELETE FROM public."users" WHERE email = OLD.email;
RETURN OLD;
END;
$$;


ALTER FUNCTION "public"."delete_user_from_public"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."generate_user_notification"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
DECLARE
    title_id_var int; -- Changed variable name to avoid ambiguity
    sla_due interval;
BEGIN
    -- Handle new visitation request on INSERT
    IF TG_OP = 'INSERT' THEN
        title_id_var := 1;
    ELSE
        -- Handle status updates on UPDATE
        CASE NEW.status_id
            WHEN 2 THEN
                -- Check if the SLA is exceeded
                SELECT (value || ' minutes')::interval INTO sla_due
                FROM settings
                WHERE settings.module = 'sla' AND settings.key = 'request_response_due';

                IF NOW() - NEW.created_at > sla_due THEN
                    title_id_var := 14; -- SLA exceeded
                ELSE
                    title_id_var := 2; -- Normal status update
                END IF;
            WHEN 10 THEN
                title_id_var := 10;
            WHEN 9 THEN
                title_id_var := 9;
            WHEN 8 THEN
                title_id_var := 8;
            WHEN 6 THEN
                IF NEW.datetime + INTERVAL '1 minute' * NEW.duration > now()
                THEN
                    title_id_var := 55; -- Visit in progress
                ELSE
                    title_id_var := 6; -- Visit ended
                END IF;
            WHEN 5 THEN
                title_id_var := 5;
            WHEN 3 THEN
                title_id_var := 3;
            ELSE
                -- If status_id is not one of the specified values, do nothing
                RETURN NEW;
        END CASE;
        
        -- Check if the status has actually changed
        IF OLD.status_id = NEW.status_id THEN
            -- Status didn't change, don't create notification
            RETURN NEW;
        END IF;
    END IF;

    -- Insert notifications for all users (with duplicate prevention)
    INSERT INTO users_notifications (user_id, visitor_id, visit_id, title_id, created_at, seen)
    SELECT user_auth_id, NEW.visitor_id, NEW.id, title_id_var, NOW(), NULL
    FROM users u
    WHERE u.deleted_at IS NULL
    AND NOT EXISTS (
        SELECT 1 
        FROM users_notifications un 
        WHERE un.user_id = u.user_auth_id 
          AND un.visit_id = NEW.id 
          AND un.title_id = title_id_var
    );

    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."generate_user_notification"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_admin_requests_with_meeting_records"() RETURNS TABLE("request_id" "text", "request_status" "text", "request_number" "text", "inmate_full_name" "text", "inmate_id" "text", "admin_id" "text", "admin_name" "text", "request_date" timestamp without time zone, "meeting_status" "text", "meeting_date_time" timestamp without time zone, "record_id" "text", "recording_url" "text", "record_duration" integer, "archived" boolean, "record_size" integer, "feedback_rate" integer, "feedback_comment" "text")
    LANGUAGE "plpgsql"
    AS $$
BEGIN
RETURN QUERY
SELECT DISTINCT ON (vr.id)
    vr.id,
    vr.status::TEXT,
    vr.visit_number::TEXT,
    inm.full_name AS inmate_full_name,
    vr.inmate_id,
    admin.id as admin_id,
    admin.full_name AS admin_name,
    vr.datetime AS request_date,
    mtg.status::TEXT AS meeting_status,
    mtg.meeting_date_time,
    mrec.id,
    mrec.recording_url,
    mrec.duration AS record_duration,
    mrec.archived,
    mrec.record_size,
    feedback.rate,
    feedback.comment
FROM public."visit_requests" vr

    LEFT JOIN public."inmates" inm ON inm.id = vr.inmate_id
    JOIN public."users" admin ON admin.id = vr.user_id
    LEFT JOIN public."meetings" mtg ON mtg.id = vr.meeting_id
    JOIN public."meeting_records" mrec ON mrec.meeting_id = mtg.id
    LEFT JOIN public."meeting_feedbacks" feedback ON mrec.meeting_id = feedback.meeting_id
WHERE mrec."deleted_at" IS NULL AND mrec.archived = TRUE AND vr.visitor_id IS NULL;
END;
$$;


ALTER FUNCTION "public"."get_admin_requests_with_meeting_records"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_admin_requests_with_meeting_records_not_archived"() RETURNS TABLE("request_id" "text", "request_status" "text", "request_number" "text", "inmate_full_name" "text", "inmate_id" "text", "admin_id" "text", "admin_name" "text", "request_date" timestamp without time zone, "meeting_status" "text", "meeting_date_time" timestamp without time zone, "record_id" "text", "recording_url" "text", "record_duration" integer, "archived" boolean, "record_size" integer, "feedback_rate" integer, "feedback_comment" "text")
    LANGUAGE "plpgsql"
    AS $$
BEGIN
RETURN QUERY
SELECT DISTINCT ON (vr.id)
    vr.id,
    vr.status::TEXT,
    vr.visit_number::TEXT,
    inm.full_name AS inmate_full_name,
    vr.inmate_id,
    admin.id as admin_id,
    admin.full_name AS admin_name,
    vr.datetime AS request_date,
    mtg.status::TEXT AS meeting_status,
    mtg.meeting_date_time,
    mrec.id,
    mrec.recording_url,
    mrec.duration AS record_duration,
    mrec.archived,
    mrec.record_size,
    feedback.rate,
    feedback.comment
FROM public."visit_requests" vr

    LEFT JOIN public."inmates" inm ON inm.id = vr.inmate_id
    JOIN public."users" admin ON admin.id = vr.user_id
    LEFT JOIN public."meetings" mtg ON mtg.id = vr.meeting_id
    JOIN public."meeting_records" mrec ON mrec.meeting_id = mtg.id
    LEFT JOIN public."meeting_feedbacks" feedback ON mrec.meeting_id = feedback.meeting_id
WHERE mrec."deleted_at" IS NULL AND mrec.archived = FALSE AND vr.visitor_id IS NULL;
END;
$$;


ALTER FUNCTION "public"."get_admin_requests_with_meeting_records_not_archived"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_visitor_requests_with_meeting_records"() RETURNS TABLE("request_id" "text", "request_status" "text", "request_number" "text", "inmate_full_name" "text", "inmate_id" "text", "visitor_name" "text", "request_date" timestamp with time zone, "from" time with time zone, "to" time with time zone, "relation" "text", "meeting_status" "text", "meeting_date_time" timestamp with time zone, "record_id" "text", "recording_url" "text", "record_duration" integer, "archived" boolean, "record_size" integer, "feedback_rate" integer, "feedback_comment" "text")
    LANGUAGE "plpgsql"
    AS $$BEGIN
RETURN QUERY
SELECT DISTINCT ON (mrec.id)
    vr.id,
    vr.status_id::TEXT,
    vr.visit_number::TEXT,
    inm.full_name AS inmate_full_name,
    vr.inmate_id,
    vis.full_name AS visitor_name,
    vr.datetime::timestamptz AS request_date,
    vr."from"::timetz AS "from", -- New field
    vr."to"::timetz AS "to", -- New field
    iv.relation::TEXT,
    vr.status_id::TEXT AS meeting_status, -- Previously from meetings table
    vr.datetime::timestamptz AS meeting_date_time, -- Previously from meetings table
    mrec.id,
    mrec.recording_url,
    mrec.duration AS record_duration,
    mrec.archived,
    mrec.record_size,
    feedback.rate,
    feedback.comment
FROM public.visit_requests vr
    LEFT JOIN public.inmate_visitors iv
ON vr.inmate_id = iv.inmate_id
    AND vr.visitor_id = iv.visitor_id
    LEFT JOIN public.inmates inm
    ON inm.id = vr.inmate_id
    JOIN public.visitors vis
    ON vis.id = vr.visitor_id
    JOIN public.meeting_records mrec
    ON mrec.visit_request_id = vr.id
    LEFT JOIN public.meeting_feedbacks feedback
    ON mrec.visit_request_id = feedback.visit_request_id
WHERE mrec.deleted_at IS NULL
  AND mrec.archived = TRUE;
END;$$;


ALTER FUNCTION "public"."get_visitor_requests_with_meeting_records"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_visitor_requests_with_meeting_records_not_archived"() RETURNS TABLE("request_id" "text", "request_status" "text", "request_number" "text", "inmate_full_name" "text", "inmate_id" "text", "visitor_name" "text", "request_date" timestamp without time zone, "from" time without time zone, "to" time without time zone, "relation" "text", "meeting_status" "text", "meeting_date_time" timestamp without time zone, "record_id" "text", "recording_url" "text", "record_duration" integer, "archived" boolean, "record_size" integer, "feedback_rate" integer, "feedback_comment" "text")
    LANGUAGE "plpgsql"
    AS $$
BEGIN
  RETURN QUERY
  SELECT DISTINCT ON (mrec.id)
    vr.id,
    vr.status_id::TEXT,
    vr.visit_number::TEXT,
    inm.full_name AS inmate_full_name,
    vr.inmate_id,
    vis.full_name AS visitor_name,
    vr.datetime::timestamp WITHOUT TIME ZONE AS request_date,
    vr."from"::time WITHOUT TIME ZONE AS "from",
    vr."to"::time WITHOUT TIME ZONE AS "to",
    iv.relation::TEXT,
    vr.status_id::TEXT AS meeting_status,
    vr.datetime::timestamp WITHOUT TIME ZONE AS meeting_date_time,
    mrec.id,
    mrec.recording_url,
    mrec.duration AS record_duration,
    mrec.archived,
    mrec.record_size,
    feedback.rate,
    feedback.comment
  FROM public.visit_requests vr
    LEFT JOIN public.inmate_visitors iv
      ON vr.inmate_id = iv.inmate_id AND vr.visitor_id = iv.visitor_id
    LEFT JOIN public.inmates inm
      ON inm.id = vr.inmate_id
    JOIN public.visitors vis
      ON vis.id = vr.visitor_id
    JOIN public.meeting_records mrec
      ON mrec.visit_request_id = vr.id
    LEFT JOIN public.meeting_feedbacks feedback
      ON mrec.visit_request_id = feedback.visit_request_id
  WHERE mrec.deleted_at IS NULL
    AND mrec.archived = FALSE;
END;
$$;


ALTER FUNCTION "public"."get_visitor_requests_with_meeting_records_not_archived"() OWNER TO "supabase_admin";


CREATE OR REPLACE FUNCTION "public"."get_visitor_requests_with_relation"("visitor_id_input" "uuid") RETURNS TABLE("request_id" "text", "request_status" "text", "request_number" "text", "inamte_full_name" "text", "inmate_id" "text", "visitor_id" "text", "request_date" timestamp without time zone, "relation" "text")
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    RETURN QUERY
    SELECT vr.id,vr.status::TEXT ,vr.visit_number::TEXT , inm.full_name,vr.inmate_id, vr.visitor_id, vr.datetime, iv.relation::TEXT 
    FROM public."Visit_Request" vr
    JOIN public."Inmate_Visitor" iv ON vr.inmate_id = iv.inmate_id AND vr.visitor_id = iv.visitor_id
    JOIN public."Inmate" inm
    ON inm.id = vr.inmate_id
    WHERE vr.visitor_id = visitor_id_input::TEXT AND vr.status IN ('CANCELED','DENIED','APPROVED','PENDING') AND iv."isDeleted" IS null;
END;
$$;


ALTER FUNCTION "public"."get_visitor_requests_with_relation"("visitor_id_input" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_visitor_visit_requests_with_meeting_records_archived"() RETURNS TABLE("request_id" "text", "request_status" "text", "request_number" "text", "visitor_full_name" "text", "visitor_id" "text", "inmate_name" "text", "request_date" timestamp with time zone, "from" time with time zone, "to" time with time zone, "relation" "text", "meeting_status" "text", "meeting_date_time" timestamp with time zone, "record_id" "text", "recording_url" "text", "record_duration" integer, "archived" boolean, "record_size" integer, "feedback_rate" integer, "feedback_comment" "text")
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    RETURN QUERY
    SELECT DISTINCT ON (mrec.id)
        vr.id,
        vr.status_id::TEXT,
        vr.visit_number::TEXT,
        vis.full_name AS visitor_full_name,
        vr.visitor_id,
        inm.full_name AS inmate_name,
        vr.datetime::timestamptz AS request_date,
        vr."from"::timetz AS "from", 
        vr."to"::timetz AS "to", 
        iv.relation::TEXT,
        vr.status_id::TEXT AS meeting_status, 
        vr.datetime::timestamptz AS meeting_date_time, 
        mrec.id,
        mrec.recording_url,
        mrec.duration AS record_duration,
        mrec.archived,
        mrec.record_size,
        feedback.rate,
        feedback.comment
    FROM public.visit_requests vr
    
    LEFT JOIN public.inmate_visitors iv
    ON vr.visitor_id = iv.visitor_id
    AND vr.inmate_id = iv.inmate_id
    
    LEFT JOIN public.visitors vis
    ON vis.id = vr.visitor_id
    
    JOIN public.inmates inm
    ON inm.id = vr.inmate_id
    
    JOIN public.meeting_records mrec
    ON mrec.visit_request_id = vr.id
    
    LEFT JOIN public.meeting_feedbacks feedback
    ON mrec.visit_request_id = feedback.visit_request_id
    
    WHERE mrec.deleted_at IS NULL
    AND mrec.archived = TRUE;
END;
$$;


ALTER FUNCTION "public"."get_visitor_visit_requests_with_meeting_records_archived"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_visitor_visit_requests_with_meeting_records_not_archived"() RETURNS TABLE("request_id" "text", "request_status" "text", "request_number" "text", "inmate_full_name" "text", "inmate_id" "text", "visitor_name" "text", "request_date" timestamp without time zone, "from" time without time zone, "to" time without time zone, "relation" "text", "meeting_status" "text", "meeting_date_time" timestamp without time zone, "record_id" "text", "recording_url" "text", "record_duration" integer, "archived" boolean, "record_size" integer, "feedback_rate" integer, "feedback_comment" "text")
    LANGUAGE "plpgsql"
    AS $$
BEGIN
  RETURN QUERY
  SELECT DISTINCT ON (mrec.id)
      vr.id::text,
      vr.status_id::text,
      vr.visit_number::text,
      inm.full_name,
      vr.inmate_id::text AS inmate_full_name,
      vis.full_name AS visitor_name,
      vr.datetime::timestamp without time zone AS request_date,  -- Explicit cast (optional)
      vr."from"::time AS "from",
      vr."to"::time AS "to",
      iv.relation::text,
      vr.status_id::text AS meeting_status,
      vr.datetime::timestamp without time zone AS meeting_date_time,  -- Explicit cast (optional)
      mrec.id::text,
      mrec.recording_url,
      mrec.duration AS record_duration,
      mrec.archived,
      mrec.record_size,
      feedback.rate,
      feedback.comment
  FROM public.visit_requests vr
      LEFT JOIN public.inmate_visitors iv
        ON vr.inmate_id = iv.inmate_id AND vr.visitor_id = iv.visitor_id
      LEFT JOIN public.inmates inm
        ON inm.id = vr.inmate_id
      JOIN public.visitors vis
        ON vis.id = vr.visitor_id
      JOIN public.meeting_records mrec
        ON mrec.visit_request_id = vr.id
      LEFT JOIN public.meeting_feedbacks feedback
        ON mrec.visit_request_id = feedback.visit_request_id
  WHERE mrec.deleted_at IS NULL
    AND mrec.archived = FALSE;
END;
$$;


ALTER FUNCTION "public"."get_visitor_visit_requests_with_meeting_records_not_archived"() OWNER TO "supabase_admin";


CREATE OR REPLACE FUNCTION "public"."handle_new_user"() RETURNS "trigger"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    default_role_id UUID;
BEGIN
    -- Get the default role ID
    SELECT id INTO default_role_id
    FROM public.roles
    WHERE name = 'default_role'
    LIMIT 1;

    -- If not found, skip processing
    IF default_role_id IS NULL THEN
        RAISE NOTICE 'Default role not found';
        RETURN NEW;
    END IF;

    -- Insert or update the user record
    INSERT INTO public.users (user_auth_id, email, role_id, first_name, last_name)
    VALUES (
        NEW.id,
        NEW.email,
        default_role_id,
        NEW.raw_user_meta_data->>'first_name',
        NEW.raw_user_meta_data->>'last_name'
    )
    ON CONFLICT (email) DO UPDATE SET
        user_auth_id = NEW.id,
        role_id = default_role_id,
        first_name = EXCLUDED.first_name,
        last_name = EXCLUDED.last_name;

    RETURN NEW;
EXCEPTION WHEN OTHERS THEN
    RAISE NOTICE 'Error in handle_new_user(): %', SQLERRM;
    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."handle_new_user"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."handle_public_user"() RETURNS "trigger"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$DECLARE
    default_role_id UUID;
    user_name TEXT;
BEGIN
    -- Log the start of the function
    RAISE NOTICE 'Trigger started for user ID: %, email: %', NEW.id, NEW.email;

    -- Fetch the default role ID
    SELECT id INTO default_role_id
    FROM public.roles
    WHERE name = 'default_role'  -- Ensure this matches the actual role name
    LIMIT 1;

    IF default_role_id IS NULL THEN
        RAISE NOTICE 'No default role found. Exiting trigger.';
        RETURN NEW;
    END IF;

    -- Extract username from email
    user_name := split_part(NEW.email, '@', 1);

    -- Insert or update the user in public.users table
    INSERT INTO public."users" (user_auth_id, email, role_id, first_name, last_name)
    VALUES (
        NEW.id, 
        NEW.email, 
        default_role_id, 
        NEW.raw_user_meta_data->>'first_name', 
        NEW.raw_user_meta_data->>'last_name'
    )
    ON CONFLICT (email)
    DO UPDATE SET 
        user_auth_id = NEW.id,
        role_id = default_role_id,
        first_name = NEW.raw_user_meta_data->>'first_name',
        last_name = NEW.raw_user_meta_data->>'last_name';

    -- Log successful insertion
    RAISE NOTICE 'User successfully inserted/updated in public.users';

    RETURN NEW;
EXCEPTION WHEN OTHERS THEN
    RAISE NOTICE 'Error in trigger: %', SQLERRM;
    RETURN NEW;
END;$$;


ALTER FUNCTION "public"."handle_public_user"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."log_activity"("p_user_id" "uuid", "p_user_email" "text", "p_entity_type" "text", "p_entity_id" "text", "p_action" "text", "p_action_group" "text" DEFAULT NULL::"text", "p_old_data" "jsonb" DEFAULT NULL::"jsonb", "p_new_data" "jsonb" DEFAULT NULL::"jsonb", "p_metadata" "jsonb" DEFAULT NULL::"jsonb", "p_ip_address" "text" DEFAULT NULL::"text", "p_user_agent" "text" DEFAULT NULL::"text") RETURNS "uuid"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
  v_log_id UUID;
BEGIN
  INSERT INTO activity_logs (
    user_id,
    user_email,
    entity_type,
    entity_id,
    action,
    action_group,
    old_data,
    new_data,
    metadata,
    ip_address,
    user_agent
  ) VALUES (
    p_user_id,
    p_user_email,
    p_entity_type,
    p_entity_id,
    p_action,
    p_action_group,
    p_old_data,
    p_new_data,
    p_metadata,
    p_ip_address,
    p_user_agent
  )
  RETURNING id INTO v_log_id;
  
  RETURN v_log_id;
END;
$$;


ALTER FUNCTION "public"."log_activity"("p_user_id" "uuid", "p_user_email" "text", "p_entity_type" "text", "p_entity_id" "text", "p_action" "text", "p_action_group" "text", "p_old_data" "jsonb", "p_new_data" "jsonb", "p_metadata" "jsonb", "p_ip_address" "text", "p_user_agent" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."log_custom_activity"("p_entity_type" "text", "p_entity_id" "text", "p_action" "text", "p_action_group" "text" DEFAULT NULL::"text", "p_metadata" "jsonb" DEFAULT NULL::"jsonb") RETURNS "uuid"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
  v_user_id UUID := auth.uid();
  v_log_id UUID;
BEGIN
  INSERT INTO activity_logs (
    user_id,
    entity_type,
    entity_id,
    action,
    action_group,
    metadata
  ) VALUES (
    v_user_id,
    p_entity_type,
    p_entity_id,
    p_action,
    p_action_group,
    p_metadata
  )
  RETURNING id INTO v_log_id;
  
  RETURN v_log_id;
END;
$$;


ALTER FUNCTION "public"."log_custom_activity"("p_entity_type" "text", "p_entity_id" "text", "p_action" "text", "p_action_group" "text", "p_metadata" "jsonb") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."notify_on_visit_request_insert"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
DECLARE
    status_name TEXT;
BEGIN
    -- Get the name of the status
    SELECT name INTO status_name FROM visit_requests_status WHERE id = NEW.status_id;

    -- Insert notification
    INSERT INTO notifications (id, datetime, visitor_id, visit_id, message, seen)
    VALUES (
        gen_random_uuid(),
        NOW(),
        NEW.visitor_id,
        NEW.id,
        status_name,
        FALSE
    );

    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."notify_on_visit_request_insert"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."notify_visit_status_change"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
DECLARE
status_name TEXT;
BEGIN
    -- Get the name of the status
SELECT name INTO status_name FROM visit_requests_status WHERE id = NEW.status_id;

IF OLD.status_id IS DISTINCT FROM NEW.status_id THEN
        INSERT INTO notifications (id, datetime, visitor_id, visit_id, message, seen)
        VALUES (
            gen_random_uuid(),
            NOW(),
            NEW.visitor_id,
            NEW.id,
            status_name,
            FALSE
        );
END IF;

RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."notify_visit_status_change"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."quotes"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$BEGIN
  NEW.first_name := REPLACE(NEW.first_name, '"', '');
  NEW.last_name := REPLACE(NEW.last_name, '"', '');
RETURN NEW;
END;$$;


ALTER FUNCTION "public"."quotes"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."remove_quotes_from_name"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    -- Remove single and double quotes from first_name
    NEW.first_name = REPLACE(REPLACE(NEW.first_name, '''', ''), '"', '');
    
    -- Remove single and double quotes from last_name
    NEW.last_name = REPLACE(REPLACE(NEW.last_name, '''', ''), '"', '');
    
    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."remove_quotes_from_name"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."remove_quotes_from_names"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
NEW.first_name := REPLACE(NEW.first_name, '"', '');
NEW.last_name := REPLACE(NEW.last_name, '"', '');
RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."remove_quotes_from_names"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."set_banned_until"() RETURNS "trigger"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$BEGIN
    -- Update the auth.users table to set banned_until when deleted_at is set
    UPDATE auth.users 
    SET banned_until = (NOW() + INTERVAL '1 year')
    WHERE id = NEW.user_auth_id;

    RETURN NEW;
END;$$;


ALTER FUNCTION "public"."set_banned_until"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."set_first_response_time"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
  -- Only update first_response_time if it's NULL
  IF NEW.first_response_time IS NULL THEN
    NEW.first_response_time := NOW();
  END IF;
  RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."set_first_response_time"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."sync_all_auth_users"() RETURNS "void"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$DECLARE
    auth_user RECORD;
    default_role_id UUID;
    full_name TEXT;
BEGIN
    -- Get the default role ID
    SELECT id INTO default_role_id
    FROM public.roles
    WHERE name = 'default_role'
    LIMIT 1;

    IF default_role_id IS NULL THEN
        RAISE EXCEPTION 'Default role not found in public.roles';
    END IF;

    -- Loop through all auth users
    FOR auth_user IN
        SELECT * FROM auth.users
    LOOP
        -- Construct full_name from first_name and last_name
        full_name := (auth_user.raw_user_meta_data ->> 'first_name') || ' ' || (auth_user.raw_user_meta_data ->> 'last_name');

        -- Insert or update the public.users table
        INSERT INTO public.users (
            user_auth_id, email, role_id, first_name, last_name, full_name
        )
        VALUES (
            auth_user.id,
            auth_user.email,
            default_role_id,
            auth_user.raw_user_meta_data ->> 'first_name',
            auth_user.raw_user_meta_data ->> 'last_name',
            full_name
        )
        ON CONFLICT (user_auth_id) DO UPDATE SET
            email = EXCLUDED.email,
            role_id = EXCLUDED.role_id,
            first_name = EXCLUDED.first_name,
            last_name = EXCLUDED.last_name,
            full_name = EXCLUDED.full_name;
    END LOOP;
END;$$;


ALTER FUNCTION "public"."sync_all_auth_users"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."trigger_log_changes"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
DECLARE
    v_user_id UUID := NULL; -- Will be set from JWT or app context
    v_user_email TEXT := NULL; -- Optional, can be fetched from auth.users
    v_old_data JSONB := NULL; -- For UPDATE/DELETE
    v_new_data JSONB := NULL; -- For INSERT/UPDATE
    v_action TEXT := TG_OP; -- INSERT, UPDATE, or DELETE
    v_entity_type TEXT := TG_TABLE_NAME; -- Name of the table
    v_entity_id TEXT; -- ID of the affected row
BEGIN
    -- Extract user ID from JWT (if available)
    v_user_id := NULLIF((current_setting('request.jwt.claims', TRUE)::jsonb)->>'sub', '')::UUID;

    -- Prepare old and new data
    IF TG_OP = 'UPDATE' THEN
        v_old_data := to_jsonb(OLD);
        v_new_data := to_jsonb(NEW);
        v_entity_id := NEW.id::TEXT;
    ELSIF TG_OP = 'INSERT' THEN
        v_new_data := to_jsonb(NEW);
        v_entity_id := NEW.id::TEXT;
    ELSIF TG_OP = 'DELETE' THEN
        v_old_data := to_jsonb(OLD);
        v_entity_id := OLD.id::TEXT;
    END IF;

    -- Log the activity
    INSERT INTO activity_logs (
        user_id,
        user_email,
        entity_type,
        entity_id,
        action,
        old_data,
        new_data
    ) VALUES (
        v_user_id,
        v_user_email,
        v_entity_type,
        v_entity_id,
        v_action,
        v_old_data,
        v_new_data
    );

    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."trigger_log_changes"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_from_to"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
    BEGIN
        NEW."from" := NEW.datetime;
        NEW."to" := NEW.datetime + INTERVAL '1 minute' * NEW.duration;
        RETURN NEW;
    END;
    $$;


ALTER FUNCTION "public"."update_from_to"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_full_name"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
  -- Check if first_name or last_name changed
  IF NEW.first_name IS DISTINCT FROM OLD.first_name
     OR NEW.last_name IS DISTINCT FROM OLD.last_name THEN
    NEW.full_name := CONCAT(NEW.first_name, ' ', NEW.last_name);
  END IF;
  RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."update_full_name"() OWNER TO "supabase_admin";


CREATE OR REPLACE FUNCTION "public"."update_visit_request_status"("request_id" "uuid", "status_name" "text") RETURNS "void"
    LANGUAGE "plpgsql"
    AS $$
begin
  update visit_requests
  set status_id = s.id,
      started_at = now()
  from visit_requests_status s
  where visit_requests.id = request_id
    and s.name = name;
end;
$$;


ALTER FUNCTION "public"."update_visit_request_status"("request_id" "uuid", "status_name" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_visit_request_status_vtext"("visit_request_id" "text", "status_name" "text") RETURNS "void"
    LANGUAGE "plpgsql"
    AS $$
begin
    -- Update the visit request status by finding the correct status ID
    update visit_requests
    set status_id = (
            select id
            from visit_requests_status
            where name = status_name
            limit 1
    ),
    -- Set 'started_at' when the status is 'running'
    started_at = case
        when status_name = 'running' then current_timestamp
        else started_at
    end,
    -- Set 'ended_at' when the status is 'completed'
    ended_at = case
        when status_name = 'completed' then current_timestamp
        else ended_at
    end
    where id = visit_request_id;
end;
$$;


ALTER FUNCTION "public"."update_visit_request_status_vtext"("visit_request_id" "text", "status_name" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_visit_title_and_status"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$BEGIN
  -- Check if the status_id is being changed to 2
  IF NEW.status_id = 2 THEN
    -- Construct the title using a prompt and visit_number
    NEW.title := 'Meeting for visit request number' || NEW.visit_number;

END IF;

RETURN NEW;
END;$$;


ALTER FUNCTION "public"."update_visit_title_and_status"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."user_trigger_function"() RETURNS "trigger"
    LANGUAGE "plpgsql" SECURITY DEFINER
    SET "search_path" TO 'public'
    AS $$DECLARE
    default_role_id UUID;
    user_name TEXT;
    full_name TEXT;
BEGIN
    -- Log the start of the function
    RAISE NOTICE 'Trigger started for user ID: %, email: %', NEW.id, NEW.email;

    -- Fetch the default role ID
    SELECT id INTO default_role_id
    FROM public.roles
    WHERE name = 'default_role'
    LIMIT 1;

    IF default_role_id IS NULL THEN
        RAISE NOTICE 'No default role found. Exiting trigger.';
        RETURN NEW;
    END IF;

    -- Extract username from email
    user_name := split_part(NEW.email, '@', 1);

    -- Concatenate first and last names with a space
    full_name := (NEW.raw_user_meta_data->>'first_name') || ' ' || (NEW.raw_user_meta_data->>'last_name');

    -- Insert or update the user in public.users table
    INSERT INTO public.users (user_auth_id, email, role_id, first_name, last_name, full_name)
    VALUES (
        NEW.id,
        NEW.email,
        default_role_id,
        NEW.raw_user_meta_data->>'first_name',
        NEW.raw_user_meta_data->>'last_name',
        full_name
    )
    ON CONFLICT (email)
    DO UPDATE SET
        user_auth_id = NEW.id,
        role_id = default_role_id,
        first_name = NEW.raw_user_meta_data->>'first_name',
        last_name = NEW.raw_user_meta_data->>'last_name',
        full_name = (NEW.raw_user_meta_data->>'first_name') || ' ' || (NEW.raw_user_meta_data->>'last_name');

    -- Log successful insertion
    RAISE NOTICE 'User successfully inserted/updated in public.users';

    RETURN NEW;

EXCEPTION WHEN OTHERS THEN
    RAISE NOTICE 'Error in trigger: %', SQLERRM;
    RETURN NEW;
END;$$;


ALTER FUNCTION "public"."user_trigger_function"() OWNER TO "postgres";

SET default_tablespace = '';

SET default_table_access_method = "heap";


CREATE TABLE IF NOT EXISTS "auth"."audit_log_entries" (
    "instance_id" "uuid",
    "id" "uuid" NOT NULL,
    "payload" "json",
    "created_at" timestamp with time zone,
    "ip_address" character varying(64) DEFAULT ''::character varying NOT NULL
);


ALTER TABLE "auth"."audit_log_entries" OWNER TO "supabase_auth_admin";


COMMENT ON TABLE "auth"."audit_log_entries" IS 'Auth: Audit trail for user actions.';



CREATE TABLE IF NOT EXISTS "auth"."flow_state" (
    "id" "uuid" NOT NULL,
    "user_id" "uuid",
    "auth_code" "text" NOT NULL,
    "code_challenge_method" "auth"."code_challenge_method" NOT NULL,
    "code_challenge" "text" NOT NULL,
    "provider_type" "text" NOT NULL,
    "provider_access_token" "text",
    "provider_refresh_token" "text",
    "created_at" timestamp with time zone,
    "updated_at" timestamp with time zone,
    "authentication_method" "text" NOT NULL,
    "auth_code_issued_at" timestamp with time zone
);


ALTER TABLE "auth"."flow_state" OWNER TO "supabase_auth_admin";


COMMENT ON TABLE "auth"."flow_state" IS 'stores metadata for pkce logins';



CREATE TABLE IF NOT EXISTS "auth"."identities" (
    "provider_id" "text" NOT NULL,
    "user_id" "uuid" NOT NULL,
    "identity_data" "jsonb" NOT NULL,
    "provider" "text" NOT NULL,
    "last_sign_in_at" timestamp with time zone,
    "created_at" timestamp with time zone,
    "updated_at" timestamp with time zone,
    "email" "text" GENERATED ALWAYS AS ("lower"(("identity_data" ->> 'email'::"text"))) STORED,
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL
);


ALTER TABLE "auth"."identities" OWNER TO "supabase_auth_admin";


COMMENT ON TABLE "auth"."identities" IS 'Auth: Stores identities associated to a user.';



COMMENT ON COLUMN "auth"."identities"."email" IS 'Auth: Email is a generated column that references the optional email property in the identity_data';



CREATE TABLE IF NOT EXISTS "auth"."instances" (
    "id" "uuid" NOT NULL,
    "uuid" "uuid",
    "raw_base_config" "text",
    "created_at" timestamp with time zone,
    "updated_at" timestamp with time zone
);


ALTER TABLE "auth"."instances" OWNER TO "supabase_auth_admin";


COMMENT ON TABLE "auth"."instances" IS 'Auth: Manages users across multiple sites.';



CREATE TABLE IF NOT EXISTS "auth"."mfa_amr_claims" (
    "session_id" "uuid" NOT NULL,
    "created_at" timestamp with time zone NOT NULL,
    "updated_at" timestamp with time zone NOT NULL,
    "authentication_method" "text" NOT NULL,
    "id" "uuid" NOT NULL
);


ALTER TABLE "auth"."mfa_amr_claims" OWNER TO "supabase_auth_admin";


COMMENT ON TABLE "auth"."mfa_amr_claims" IS 'auth: stores authenticator method reference claims for multi factor authentication';



CREATE TABLE IF NOT EXISTS "auth"."mfa_challenges" (
    "id" "uuid" NOT NULL,
    "factor_id" "uuid" NOT NULL,
    "created_at" timestamp with time zone NOT NULL,
    "verified_at" timestamp with time zone,
    "ip_address" "inet" NOT NULL,
    "otp_code" "text",
    "web_authn_session_data" "jsonb"
);


ALTER TABLE "auth"."mfa_challenges" OWNER TO "supabase_auth_admin";


COMMENT ON TABLE "auth"."mfa_challenges" IS 'auth: stores metadata about challenge requests made';



CREATE TABLE IF NOT EXISTS "auth"."mfa_factors" (
    "id" "uuid" NOT NULL,
    "user_id" "uuid" NOT NULL,
    "friendly_name" "text",
    "factor_type" "auth"."factor_type" NOT NULL,
    "status" "auth"."factor_status" NOT NULL,
    "created_at" timestamp with time zone NOT NULL,
    "updated_at" timestamp with time zone NOT NULL,
    "secret" "text",
    "phone" "text",
    "last_challenged_at" timestamp with time zone,
    "web_authn_credential" "jsonb",
    "web_authn_aaguid" "uuid"
);


ALTER TABLE "auth"."mfa_factors" OWNER TO "supabase_auth_admin";


COMMENT ON TABLE "auth"."mfa_factors" IS 'auth: stores metadata about factors';



CREATE TABLE IF NOT EXISTS "auth"."one_time_tokens" (
    "id" "uuid" NOT NULL,
    "user_id" "uuid" NOT NULL,
    "token_type" "auth"."one_time_token_type" NOT NULL,
    "token_hash" "text" NOT NULL,
    "relates_to" "text" NOT NULL,
    "created_at" timestamp without time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp without time zone DEFAULT "now"() NOT NULL,
    CONSTRAINT "one_time_tokens_token_hash_check" CHECK (("char_length"("token_hash") > 0))
);


ALTER TABLE "auth"."one_time_tokens" OWNER TO "supabase_auth_admin";


CREATE TABLE IF NOT EXISTS "auth"."refresh_tokens" (
    "instance_id" "uuid",
    "id" bigint NOT NULL,
    "token" character varying(255),
    "user_id" character varying(255),
    "revoked" boolean,
    "created_at" timestamp with time zone,
    "updated_at" timestamp with time zone,
    "parent" character varying(255),
    "session_id" "uuid"
);


ALTER TABLE "auth"."refresh_tokens" OWNER TO "supabase_auth_admin";


COMMENT ON TABLE "auth"."refresh_tokens" IS 'Auth: Store of tokens used to refresh JWT tokens once they expire.';



CREATE SEQUENCE IF NOT EXISTS "auth"."refresh_tokens_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "auth"."refresh_tokens_id_seq" OWNER TO "supabase_auth_admin";


ALTER SEQUENCE "auth"."refresh_tokens_id_seq" OWNED BY "auth"."refresh_tokens"."id";



CREATE TABLE IF NOT EXISTS "auth"."saml_providers" (
    "id" "uuid" NOT NULL,
    "sso_provider_id" "uuid" NOT NULL,
    "entity_id" "text" NOT NULL,
    "metadata_xml" "text" NOT NULL,
    "metadata_url" "text",
    "attribute_mapping" "jsonb",
    "created_at" timestamp with time zone,
    "updated_at" timestamp with time zone,
    "name_id_format" "text",
    CONSTRAINT "entity_id not empty" CHECK (("char_length"("entity_id") > 0)),
    CONSTRAINT "metadata_url not empty" CHECK ((("metadata_url" = NULL::"text") OR ("char_length"("metadata_url") > 0))),
    CONSTRAINT "metadata_xml not empty" CHECK (("char_length"("metadata_xml") > 0))
);


ALTER TABLE "auth"."saml_providers" OWNER TO "supabase_auth_admin";


COMMENT ON TABLE "auth"."saml_providers" IS 'Auth: Manages SAML Identity Provider connections.';



CREATE TABLE IF NOT EXISTS "auth"."saml_relay_states" (
    "id" "uuid" NOT NULL,
    "sso_provider_id" "uuid" NOT NULL,
    "request_id" "text" NOT NULL,
    "for_email" "text",
    "redirect_to" "text",
    "created_at" timestamp with time zone,
    "updated_at" timestamp with time zone,
    "flow_state_id" "uuid",
    CONSTRAINT "request_id not empty" CHECK (("char_length"("request_id") > 0))
);


ALTER TABLE "auth"."saml_relay_states" OWNER TO "supabase_auth_admin";


COMMENT ON TABLE "auth"."saml_relay_states" IS 'Auth: Contains SAML Relay State information for each Service Provider initiated login.';



CREATE TABLE IF NOT EXISTS "auth"."schema_migrations" (
    "version" character varying(255) NOT NULL
);


ALTER TABLE "auth"."schema_migrations" OWNER TO "supabase_auth_admin";


COMMENT ON TABLE "auth"."schema_migrations" IS 'Auth: Manages updates to the auth system.';



CREATE TABLE IF NOT EXISTS "auth"."sessions" (
    "id" "uuid" NOT NULL,
    "user_id" "uuid" NOT NULL,
    "created_at" timestamp with time zone,
    "updated_at" timestamp with time zone,
    "factor_id" "uuid",
    "aal" "auth"."aal_level",
    "not_after" timestamp with time zone,
    "refreshed_at" timestamp without time zone,
    "user_agent" "text",
    "ip" "inet",
    "tag" "text"
);


ALTER TABLE "auth"."sessions" OWNER TO "supabase_auth_admin";


COMMENT ON TABLE "auth"."sessions" IS 'Auth: Stores session data associated to a user.';



COMMENT ON COLUMN "auth"."sessions"."not_after" IS 'Auth: Not after is a nullable column that contains a timestamp after which the session should be regarded as expired.';



CREATE TABLE IF NOT EXISTS "auth"."sso_domains" (
    "id" "uuid" NOT NULL,
    "sso_provider_id" "uuid" NOT NULL,
    "domain" "text" NOT NULL,
    "created_at" timestamp with time zone,
    "updated_at" timestamp with time zone,
    CONSTRAINT "domain not empty" CHECK (("char_length"("domain") > 0))
);


ALTER TABLE "auth"."sso_domains" OWNER TO "supabase_auth_admin";


COMMENT ON TABLE "auth"."sso_domains" IS 'Auth: Manages SSO email address domain mapping to an SSO Identity Provider.';



CREATE TABLE IF NOT EXISTS "auth"."sso_providers" (
    "id" "uuid" NOT NULL,
    "resource_id" "text",
    "created_at" timestamp with time zone,
    "updated_at" timestamp with time zone,
    CONSTRAINT "resource_id not empty" CHECK ((("resource_id" = NULL::"text") OR ("char_length"("resource_id") > 0)))
);


ALTER TABLE "auth"."sso_providers" OWNER TO "supabase_auth_admin";


COMMENT ON TABLE "auth"."sso_providers" IS 'Auth: Manages SSO identity provider information; see saml_providers for SAML.';



COMMENT ON COLUMN "auth"."sso_providers"."resource_id" IS 'Auth: Uniquely identifies a SSO provider according to a user-chosen resource ID (case insensitive), useful in infrastructure as code.';



CREATE TABLE IF NOT EXISTS "auth"."users" (
    "instance_id" "uuid",
    "id" "uuid" NOT NULL,
    "aud" character varying(255),
    "role" character varying(255),
    "email" character varying(255),
    "encrypted_password" character varying(255),
    "email_confirmed_at" timestamp with time zone,
    "invited_at" timestamp with time zone,
    "confirmation_token" character varying(255),
    "confirmation_sent_at" timestamp with time zone,
    "recovery_token" character varying(255),
    "recovery_sent_at" timestamp with time zone,
    "email_change_token_new" character varying(255),
    "email_change" character varying(255),
    "email_change_sent_at" timestamp with time zone,
    "last_sign_in_at" timestamp with time zone,
    "raw_app_meta_data" "jsonb",
    "raw_user_meta_data" "jsonb",
    "is_super_admin" boolean,
    "created_at" timestamp with time zone,
    "updated_at" timestamp with time zone,
    "phone" "text" DEFAULT NULL::character varying,
    "phone_confirmed_at" timestamp with time zone,
    "phone_change" "text" DEFAULT ''::character varying,
    "phone_change_token" character varying(255) DEFAULT ''::character varying,
    "phone_change_sent_at" timestamp with time zone,
    "confirmed_at" timestamp with time zone GENERATED ALWAYS AS (LEAST("email_confirmed_at", "phone_confirmed_at")) STORED,
    "email_change_token_current" character varying(255) DEFAULT ''::character varying,
    "email_change_confirm_status" smallint DEFAULT 0,
    "banned_until" timestamp without time zone,
    "reauthentication_token" character varying(255) DEFAULT ''::character varying,
    "reauthentication_sent_at" timestamp with time zone,
    "is_sso_user" boolean DEFAULT false NOT NULL,
    "deleted_at" timestamp with time zone,
    "is_anonymous" boolean DEFAULT false NOT NULL,
    CONSTRAINT "users_email_change_confirm_status_check" CHECK ((("email_change_confirm_status" >= 0) AND ("email_change_confirm_status" <= 2)))
);


ALTER TABLE "auth"."users" OWNER TO "supabase_auth_admin";


COMMENT ON TABLE "auth"."users" IS 'Auth: Stores user login data within a secure schema.';



COMMENT ON COLUMN "auth"."users"."is_sso_user" IS 'Auth: Set this column to true when the account comes from SSO. These accounts can have duplicate emails.';



CREATE TABLE IF NOT EXISTS "public"."_prisma_migrations" (
    "id" character varying(36) NOT NULL,
    "checksum" character varying(64) NOT NULL,
    "finished_at" timestamp with time zone,
    "migration_name" character varying(255) NOT NULL,
    "logs" "text",
    "rolled_back_at" timestamp with time zone,
    "started_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "applied_steps_count" integer DEFAULT 0 NOT NULL
);


ALTER TABLE "public"."_prisma_migrations" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."activity_logs" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "created_at" timestamp(6) with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "user_id" "uuid",
    "user_email" "text",
    "entity_type" "text" NOT NULL,
    "entity_id" "text" NOT NULL,
    "action" "text" NOT NULL,
    "action_group" "text",
    "old_data" "jsonb",
    "new_data" "jsonb",
    "metadata" "jsonb",
    "ip_address" "text",
    "user_agent" "text"
);


ALTER TABLE "public"."activity_logs" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."availability_times" (
    "id" "text" DEFAULT "gen_random_uuid"() NOT NULL,
    "setting_id" "text" NOT NULL,
    "type" "public"."availability_type" NOT NULL,
    "available_from" time(6) without time zone NOT NULL,
    "available_to" time(6) without time zone NOT NULL
);


ALTER TABLE "public"."availability_times" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."function_logs" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "function_name" "text",
    "message" "text",
    "timestamp" timestamp without time zone DEFAULT "now"()
);


ALTER TABLE "public"."function_logs" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."inmate_availability_settings" (
    "id" "text" DEFAULT "gen_random_uuid"() NOT NULL,
    "day" "public"."days" NOT NULL,
    "inmate_id" "text" NOT NULL
);


ALTER TABLE "public"."inmate_availability_settings" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."inmate_settings" (
    "id" "text" DEFAULT "gen_random_uuid"() NOT NULL,
    "inmate_id" "text" NOT NULL,
    "key" "text" NOT NULL,
    "value" "text" NOT NULL
);


ALTER TABLE "public"."inmate_settings" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."inmate_visitor_documents" (
    "id" "text" DEFAULT "gen_random_uuid"() NOT NULL,
    "inmate_visitor_id" "text" NOT NULL,
    "document_url" "text" NOT NULL,
    "document_name" "text" NOT NULL,
    "document_size" "text" NOT NULL
);


ALTER TABLE "public"."inmate_visitor_documents" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."inmate_visitors" (
    "id" "text" DEFAULT "gen_random_uuid"() NOT NULL,
    "inmate_id" "text" NOT NULL,
    "visitor_id" "text" NOT NULL,
    "is_white_list" boolean,
    "other" "text",
    "relation" "public"."inmate_visitor_relation",
    "deleted_at" timestamp(3) without time zone,
    "requested_inmate_name" "text"
);


ALTER TABLE "public"."inmate_visitors" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."inmates" (
    "id" "text" DEFAULT "gen_random_uuid"() NOT NULL,
    "full_name" "text" NOT NULL,
    "national_id" "text",
    "number_of_visits" integer NOT NULL,
    "section_id" "text" NOT NULL,
    "room_id" "text",
    "deleted_at" timestamp(3) without time zone,
    "is_building_default" boolean,
    "is_room_default" boolean,
    "inmate_code" "text",
    "nationality" "text",
    "status_id" integer DEFAULT 1 NOT NULL
);


ALTER TABLE "public"."inmates" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."inmates_status" (
    "id" integer NOT NULL,
    "name" "text" NOT NULL
);


ALTER TABLE "public"."inmates_status" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."inmates_status_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."inmates_status_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."inmates_status_id_seq" OWNED BY "public"."inmates_status"."id";



CREATE TABLE IF NOT EXISTS "public"."meeting_feedbacks" (
    "id" "text" DEFAULT "gen_random_uuid"() NOT NULL,
    "visitor_id" "text" NOT NULL,
    "comment" "text",
    "rate" integer NOT NULL,
    "visit_request_id" "text" NOT NULL
);


ALTER TABLE "public"."meeting_feedbacks" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."meeting_records" (
    "id" "text" DEFAULT "gen_random_uuid"() NOT NULL,
    "session_id" "text" DEFAULT "gen_random_uuid"(),
    "recording_url" "text",
    "record_size" integer,
    "duration" integer,
    "created_at" timestamp(3) without time zone,
    "deleted_at" timestamp(3) without time zone,
    "archived" boolean DEFAULT false,
    "visit_request_id" "text" NOT NULL
);


ALTER TABLE "public"."meeting_records" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."notification_titles" (
    "id" integer NOT NULL,
    "name" "text" NOT NULL
);


ALTER TABLE "public"."notification_titles" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."notifications" (
    "id" "text" DEFAULT "gen_random_uuid"() NOT NULL,
    "datetime" timestamp without time zone,
    "visitor_id" "text" NOT NULL,
    "visit_id" "text" NOT NULL,
    "message" "text" NOT NULL,
    "seen" boolean DEFAULT false
);


ALTER TABLE "public"."notifications" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."overrides" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "room_id" "text" NOT NULL,
    "date" "date" NOT NULL
);


ALTER TABLE "public"."overrides" OWNER TO "supabase_admin";


CREATE TABLE IF NOT EXISTS "public"."overrides_timeslots" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "override_id" "uuid" NOT NULL,
    "starttime" time(6) without time zone NOT NULL,
    "endtime" time(6) without time zone NOT NULL
);


ALTER TABLE "public"."overrides_timeslots" OWNER TO "supabase_admin";


CREATE TABLE IF NOT EXISTS "public"."permissions" (
    "id" "text" DEFAULT "gen_random_uuid"() NOT NULL,
    "name" "text" NOT NULL,
    "category_id" integer NOT NULL
);


ALTER TABLE "public"."permissions" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."permissions_categories" (
    "id" integer NOT NULL,
    "name" "text" NOT NULL
);


ALTER TABLE "public"."permissions_categories" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."permissions_categories_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."permissions_categories_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."permissions_categories_id_seq" OWNED BY "public"."permissions_categories"."id";



CREATE TABLE IF NOT EXISTS "public"."role_permissions" (
    "id" "text" DEFAULT "gen_random_uuid"() NOT NULL,
    "role_id" "text" NOT NULL,
    "permission_id" "text" NOT NULL
);


ALTER TABLE "public"."role_permissions" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."roles" (
    "id" "text" DEFAULT "gen_random_uuid"() NOT NULL,
    "name" "text" NOT NULL,
    "deleted_at" timestamp(3) without time zone
);


ALTER TABLE "public"."roles" OWNER TO "postgres";


CREATE OR REPLACE VIEW "public"."role_permissions_view" AS
 SELECT "rp"."id" AS "role_permission_id",
    "r"."id" AS "role_id",
    "r"."name" AS "role_name",
    "p"."id" AS "permission_id",
    "p"."name" AS "permission_name"
   FROM (("public"."role_permissions" "rp"
     JOIN "public"."roles" "r" ON (("rp"."role_id" = "r"."id")))
     JOIN "public"."permissions" "p" ON (("rp"."permission_id" = "p"."id")));


ALTER TABLE "public"."role_permissions_view" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."room_availability_settings" (
    "id" "text" DEFAULT "gen_random_uuid"() NOT NULL,
    "day" "public"."days" NOT NULL,
    "room_id" "text" NOT NULL,
    "is_available" boolean DEFAULT true NOT NULL
);


ALTER TABLE "public"."room_availability_settings" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."room_slots" (
    "id" "text" DEFAULT "gen_random_uuid"() NOT NULL,
    "room_id" "text" NOT NULL,
    "slot_start" timestamp(3) without time zone NOT NULL,
    "slot_end" timestamp(3) without time zone NOT NULL,
    "created_at" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updated_at" timestamp(3) without time zone NOT NULL,
    "visit_request_id" "text" NOT NULL
);


ALTER TABLE "public"."room_slots" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."room_status" (
    "id" integer NOT NULL,
    "name" "text" NOT NULL
);


ALTER TABLE "public"."room_status" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."room_status_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."room_status_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."room_status_id_seq" OWNED BY "public"."room_status"."id";



CREATE TABLE IF NOT EXISTS "public"."rooms" (
    "id" "text" DEFAULT "gen_random_uuid"() NOT NULL,
    "name" "text" NOT NULL,
    "active" boolean NOT NULL,
    "section_id" "text" NOT NULL,
    "is_building_default" boolean,
    "deleted_at" timestamp(3) without time zone,
    "status_id" integer DEFAULT 1 NOT NULL
);


ALTER TABLE "public"."rooms" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."schema_migrations" (
    "version" bigint NOT NULL,
    "inserted_at" timestamp(0) without time zone
);


ALTER TABLE "public"."schema_migrations" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."section_availability_settings" (
    "id" "text" DEFAULT "gen_random_uuid"() NOT NULL,
    "section_id" "text" NOT NULL,
    "day" "public"."days" NOT NULL
);


ALTER TABLE "public"."section_availability_settings" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."section_overrides" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "section_id" "text" NOT NULL,
    "date" "date" NOT NULL
);


ALTER TABLE "public"."section_overrides" OWNER TO "supabase_admin";


CREATE TABLE IF NOT EXISTS "public"."section_overrides_timeslots" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "override_id" "uuid" NOT NULL,
    "starttime" time(6) without time zone NOT NULL,
    "endtime" time(6) without time zone NOT NULL
);


ALTER TABLE "public"."section_overrides_timeslots" OWNER TO "supabase_admin";


CREATE TABLE IF NOT EXISTS "public"."sections" (
    "id" "text" DEFAULT "gen_random_uuid"() NOT NULL,
    "name" "text" NOT NULL,
    "is_available" boolean DEFAULT true NOT NULL,
    "deleted_at" timestamp(3) without time zone
);


ALTER TABLE "public"."sections" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."servers" (
    "id" "uuid" NOT NULL,
    "code" integer NOT NULL,
    "link" "text" NOT NULL,
    "isactive" boolean DEFAULT true NOT NULL
);


ALTER TABLE "public"."servers" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."settings" (
    "id" "text" DEFAULT "gen_random_uuid"() NOT NULL,
    "key" "text" NOT NULL,
    "value" "text" NOT NULL,
    "module" "text" NOT NULL
);


ALTER TABLE "public"."settings" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."tenants" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "external_id" "text" NOT NULL,
    "name" "text" NOT NULL,
    "jwt_secret" "text" NOT NULL,
    "postgres_cdc_default" boolean DEFAULT true,
    "inserted_at" timestamp without time zone DEFAULT "now"(),
    "updated_at" timestamp without time zone DEFAULT "now"(),
    "max_concurrent_users" integer DEFAULT 200,
    "max_events_per_second" integer DEFAULT 100,
    "max_bytes_per_second" integer DEFAULT 100000,
    "max_channels_per_client" integer DEFAULT 100,
    "max_joins_per_second" integer DEFAULT 100,
    "suspend" boolean DEFAULT false
);


ALTER TABLE "public"."tenants" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."user_permissions" (
    "id" "text" DEFAULT "gen_random_uuid"() NOT NULL,
    "user_id" "text" NOT NULL,
    "permission_id" "text" NOT NULL
);


ALTER TABLE "public"."user_permissions" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."users" (
    "id" "text" DEFAULT "gen_random_uuid"() NOT NULL,
    "role_id" "text" NOT NULL,
    "full_name" "text",
    "email" "text",
    "password" "text",
    "first_name" "text",
    "last_name" "text",
    "user_auth_id" "uuid",
    "deleted_at" timestamp(3) without time zone
);


ALTER TABLE "public"."users" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."users_notifications" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "visitor_id" "text" NOT NULL,
    "visit_id" "text" NOT NULL,
    "title_id" integer NOT NULL,
    "created_at" timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "seen" timestamp(3) without time zone,
    "user_id" "uuid"
);


ALTER TABLE "public"."users_notifications" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."visit_requests" (
    "id" "text" DEFAULT "gen_random_uuid"() NOT NULL,
    "visitor_id" "text",
    "inmate_id" "text" NOT NULL,
    "datetime" timestamp without time zone NOT NULL,
    "duration" integer NOT NULL,
    "user_id" "uuid",
    "visit_number" integer NOT NULL,
    "archived" boolean DEFAULT false NOT NULL,
    "requested_inmate_name" "text",
    "from" time without time zone,
    "status_id" integer NOT NULL,
    "to" time without time zone,
    "actual_duration" integer,
    "ended_at" timestamp(3) without time zone,
    "reason" "text",
    "started_at" timestamp(3) without time zone,
    "title" "text",
    "deleted_at" timestamp(3) without time zone,
    "created_at" timestamp without time zone DEFAULT "now"() NOT NULL,
    "first_response_time" timestamp(3) without time zone,
    "updated_at" timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "meeting_url" "text",
    "room_id" "text"
);


ALTER TABLE "public"."visit_requests" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."visit_requests_status" (
    "id" integer NOT NULL,
    "name" "text" NOT NULL
);


ALTER TABLE "public"."visit_requests_status" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."visit_requests_status_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."visit_requests_status_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."visit_requests_status_id_seq" OWNED BY "public"."visit_requests_status"."id";



CREATE TABLE IF NOT EXISTS "public"."visitors" (
    "id" "text" DEFAULT "gen_random_uuid"() NOT NULL,
    "full_name" "text" NOT NULL,
    "national_id" "text" NOT NULL,
    "blocked" boolean DEFAULT false NOT NULL,
    "phone_number" "text",
    "block_reason" "text",
    "deleted_at" timestamp(3) without time zone,
    "nationality" "text",
    "first_name" "text",
    "last_name" "text"
);


ALTER TABLE "public"."visitors" OWNER TO "postgres";


CREATE OR REPLACE VIEW "public"."visit_requests_view" AS
 SELECT "vr"."id",
    "vr"."visit_number",
    "vr"."datetime",
    "vr"."duration",
    "vr"."actual_duration",
    "vr"."started_at",
    "vr"."ended_at",
    "vr"."from",
    "vr"."to",
    "vr"."title",
    "vr"."reason",
    "vr"."status_id",
    "s"."name" AS "status_name",
    "vr"."visitor_id",
    "v"."full_name" AS "visitor_name",
    "vr"."inmate_id",
    "i"."full_name" AS "inmate_name",
    "vr"."requested_inmate_name",
    "vr"."user_id",
    "u"."full_name" AS "user_name",
    "vr"."room_id",
    "r"."name" AS "room_name",
    "vr"."meeting_url",
    "vr"."archived",
    "vr"."deleted_at",
    "vr"."created_at",
    "vr"."updated_at",
    "vr"."first_response_time"
   FROM ((((("public"."visit_requests" "vr"
     LEFT JOIN "public"."visitors" "v" ON (("v"."id" = "vr"."visitor_id")))
     LEFT JOIN "public"."inmates" "i" ON (("i"."id" = "vr"."inmate_id")))
     LEFT JOIN "public"."visit_requests_status" "s" ON (("s"."id" = "vr"."status_id")))
     LEFT JOIN "public"."users" "u" ON (("u"."user_auth_id" = "vr"."user_id")))
     LEFT JOIN "public"."rooms" "r" ON (("r"."id" = "vr"."room_id")));


ALTER TABLE "public"."visit_requests_view" OWNER TO "supabase_admin";


CREATE SEQUENCE IF NOT EXISTS "public"."visit_requests_visit_number_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."visit_requests_visit_number_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."visit_requests_visit_number_seq" OWNED BY "public"."visit_requests"."visit_number";



ALTER TABLE ONLY "auth"."refresh_tokens" ALTER COLUMN "id" SET DEFAULT "nextval"('"auth"."refresh_tokens_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."inmates_status" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."inmates_status_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."permissions_categories" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."permissions_categories_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."room_status" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."room_status_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."visit_requests" ALTER COLUMN "visit_number" SET DEFAULT "nextval"('"public"."visit_requests_visit_number_seq"'::"regclass");



ALTER TABLE ONLY "public"."visit_requests_status" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."visit_requests_status_id_seq"'::"regclass");



ALTER TABLE ONLY "auth"."mfa_amr_claims"
    ADD CONSTRAINT "amr_id_pk" PRIMARY KEY ("id");



ALTER TABLE ONLY "auth"."audit_log_entries"
    ADD CONSTRAINT "audit_log_entries_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "auth"."flow_state"
    ADD CONSTRAINT "flow_state_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "auth"."identities"
    ADD CONSTRAINT "identities_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "auth"."identities"
    ADD CONSTRAINT "identities_provider_id_provider_unique" UNIQUE ("provider_id", "provider");



ALTER TABLE ONLY "auth"."instances"
    ADD CONSTRAINT "instances_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "auth"."mfa_amr_claims"
    ADD CONSTRAINT "mfa_amr_claims_session_id_authentication_method_pkey" UNIQUE ("session_id", "authentication_method");



ALTER TABLE ONLY "auth"."mfa_challenges"
    ADD CONSTRAINT "mfa_challenges_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "auth"."mfa_factors"
    ADD CONSTRAINT "mfa_factors_last_challenged_at_key" UNIQUE ("last_challenged_at");



ALTER TABLE ONLY "auth"."mfa_factors"
    ADD CONSTRAINT "mfa_factors_phone_key" UNIQUE ("phone");



ALTER TABLE ONLY "auth"."mfa_factors"
    ADD CONSTRAINT "mfa_factors_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "auth"."one_time_tokens"
    ADD CONSTRAINT "one_time_tokens_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "auth"."refresh_tokens"
    ADD CONSTRAINT "refresh_tokens_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "auth"."refresh_tokens"
    ADD CONSTRAINT "refresh_tokens_token_unique" UNIQUE ("token");



ALTER TABLE ONLY "auth"."saml_providers"
    ADD CONSTRAINT "saml_providers_entity_id_key" UNIQUE ("entity_id");



ALTER TABLE ONLY "auth"."saml_providers"
    ADD CONSTRAINT "saml_providers_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "auth"."saml_relay_states"
    ADD CONSTRAINT "saml_relay_states_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "auth"."schema_migrations"
    ADD CONSTRAINT "schema_migrations_pkey" PRIMARY KEY ("version");



ALTER TABLE ONLY "auth"."sessions"
    ADD CONSTRAINT "sessions_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "auth"."sso_domains"
    ADD CONSTRAINT "sso_domains_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "auth"."sso_providers"
    ADD CONSTRAINT "sso_providers_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "auth"."users"
    ADD CONSTRAINT "users_phone_key" UNIQUE ("phone");



ALTER TABLE ONLY "auth"."users"
    ADD CONSTRAINT "users_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."_prisma_migrations"
    ADD CONSTRAINT "_prisma_migrations_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."activity_logs"
    ADD CONSTRAINT "activity_logs_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."availability_times"
    ADD CONSTRAINT "availability_times_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."function_logs"
    ADD CONSTRAINT "function_logs_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."inmate_availability_settings"
    ADD CONSTRAINT "inmate_availability_settings_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."inmate_settings"
    ADD CONSTRAINT "inmate_settings_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."inmate_visitor_documents"
    ADD CONSTRAINT "inmate_visitor_documents_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."inmate_visitors"
    ADD CONSTRAINT "inmate_visitors_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."inmates"
    ADD CONSTRAINT "inmates_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."inmates_status"
    ADD CONSTRAINT "inmates_status_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."meeting_feedbacks"
    ADD CONSTRAINT "meeting_feedbacks_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."meeting_records"
    ADD CONSTRAINT "meeting_records_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."notification_titles"
    ADD CONSTRAINT "notification_titles_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."notifications"
    ADD CONSTRAINT "notifications_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."overrides"
    ADD CONSTRAINT "overrides_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."overrides_timeslots"
    ADD CONSTRAINT "overrides_timeslots_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."permissions_categories"
    ADD CONSTRAINT "permissions_categories_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."permissions"
    ADD CONSTRAINT "permissions_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."role_permissions"
    ADD CONSTRAINT "role_permissions_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."roles"
    ADD CONSTRAINT "roles_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."room_availability_settings"
    ADD CONSTRAINT "room_availability_settings_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."room_slots"
    ADD CONSTRAINT "room_slots_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."room_status"
    ADD CONSTRAINT "room_status_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."rooms"
    ADD CONSTRAINT "rooms_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."schema_migrations"
    ADD CONSTRAINT "schema_migrations_pkey" PRIMARY KEY ("version");



ALTER TABLE ONLY "public"."section_availability_settings"
    ADD CONSTRAINT "section_availability_settings_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."section_overrides"
    ADD CONSTRAINT "section_overrides_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."section_overrides_timeslots"
    ADD CONSTRAINT "section_overrides_timeslots_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."sections"
    ADD CONSTRAINT "sections_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."servers"
    ADD CONSTRAINT "servers_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."settings"
    ADD CONSTRAINT "settings_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."tenants"
    ADD CONSTRAINT "tenants_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."overrides"
    ADD CONSTRAINT "unique_override_room_date" UNIQUE ("room_id", "date");



ALTER TABLE ONLY "public"."section_overrides"
    ADD CONSTRAINT "unique_section_override" UNIQUE ("section_id", "date");



ALTER TABLE ONLY "public"."user_permissions"
    ADD CONSTRAINT "user_permissions_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."users_notifications"
    ADD CONSTRAINT "users_notifications_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."users"
    ADD CONSTRAINT "users_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."users"
    ADD CONSTRAINT "users_user_auth_id_unique" UNIQUE ("user_auth_id");



ALTER TABLE ONLY "public"."visit_requests"
    ADD CONSTRAINT "visit_requests_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."visit_requests_status"
    ADD CONSTRAINT "visit_requests_status_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."visitors"
    ADD CONSTRAINT "visitors_pkey" PRIMARY KEY ("id");



CREATE INDEX "audit_logs_instance_id_idx" ON "auth"."audit_log_entries" USING "btree" ("instance_id");



CREATE UNIQUE INDEX "confirmation_token_idx" ON "auth"."users" USING "btree" ("confirmation_token") WHERE (("confirmation_token")::"text" !~ '^[0-9 ]*$'::"text");



CREATE UNIQUE INDEX "email_change_token_current_idx" ON "auth"."users" USING "btree" ("email_change_token_current") WHERE (("email_change_token_current")::"text" !~ '^[0-9 ]*$'::"text");



CREATE UNIQUE INDEX "email_change_token_new_idx" ON "auth"."users" USING "btree" ("email_change_token_new") WHERE (("email_change_token_new")::"text" !~ '^[0-9 ]*$'::"text");



CREATE INDEX "factor_id_created_at_idx" ON "auth"."mfa_factors" USING "btree" ("user_id", "created_at");



CREATE INDEX "flow_state_created_at_idx" ON "auth"."flow_state" USING "btree" ("created_at" DESC);



CREATE INDEX "identities_email_idx" ON "auth"."identities" USING "btree" ("email" "text_pattern_ops");



COMMENT ON INDEX "auth"."identities_email_idx" IS 'Auth: Ensures indexed queries on the email column';



CREATE INDEX "identities_user_id_idx" ON "auth"."identities" USING "btree" ("user_id");



CREATE INDEX "idx_auth_code" ON "auth"."flow_state" USING "btree" ("auth_code");



CREATE INDEX "idx_user_id_auth_method" ON "auth"."flow_state" USING "btree" ("user_id", "authentication_method");



CREATE INDEX "mfa_challenge_created_at_idx" ON "auth"."mfa_challenges" USING "btree" ("created_at" DESC);



CREATE UNIQUE INDEX "mfa_factors_user_friendly_name_unique" ON "auth"."mfa_factors" USING "btree" ("friendly_name", "user_id") WHERE (TRIM(BOTH FROM "friendly_name") <> ''::"text");



CREATE INDEX "mfa_factors_user_id_idx" ON "auth"."mfa_factors" USING "btree" ("user_id");



CREATE INDEX "one_time_tokens_relates_to_hash_idx" ON "auth"."one_time_tokens" USING "hash" ("relates_to");



CREATE INDEX "one_time_tokens_token_hash_hash_idx" ON "auth"."one_time_tokens" USING "hash" ("token_hash");



CREATE UNIQUE INDEX "one_time_tokens_user_id_token_type_key" ON "auth"."one_time_tokens" USING "btree" ("user_id", "token_type");



CREATE UNIQUE INDEX "reauthentication_token_idx" ON "auth"."users" USING "btree" ("reauthentication_token") WHERE (("reauthentication_token")::"text" !~ '^[0-9 ]*$'::"text");



CREATE UNIQUE INDEX "recovery_token_idx" ON "auth"."users" USING "btree" ("recovery_token") WHERE (("recovery_token")::"text" !~ '^[0-9 ]*$'::"text");



CREATE INDEX "refresh_tokens_instance_id_idx" ON "auth"."refresh_tokens" USING "btree" ("instance_id");



CREATE INDEX "refresh_tokens_instance_id_user_id_idx" ON "auth"."refresh_tokens" USING "btree" ("instance_id", "user_id");



CREATE INDEX "refresh_tokens_parent_idx" ON "auth"."refresh_tokens" USING "btree" ("parent");



CREATE INDEX "refresh_tokens_session_id_revoked_idx" ON "auth"."refresh_tokens" USING "btree" ("session_id", "revoked");



CREATE INDEX "refresh_tokens_updated_at_idx" ON "auth"."refresh_tokens" USING "btree" ("updated_at" DESC);



CREATE INDEX "saml_providers_sso_provider_id_idx" ON "auth"."saml_providers" USING "btree" ("sso_provider_id");



CREATE INDEX "saml_relay_states_created_at_idx" ON "auth"."saml_relay_states" USING "btree" ("created_at" DESC);



CREATE INDEX "saml_relay_states_for_email_idx" ON "auth"."saml_relay_states" USING "btree" ("for_email");



CREATE INDEX "saml_relay_states_sso_provider_id_idx" ON "auth"."saml_relay_states" USING "btree" ("sso_provider_id");



CREATE UNIQUE INDEX "schema_migrations_version_idx" ON "auth"."schema_migrations" USING "btree" ("version");



CREATE INDEX "sessions_not_after_idx" ON "auth"."sessions" USING "btree" ("not_after" DESC);



CREATE INDEX "sessions_user_id_idx" ON "auth"."sessions" USING "btree" ("user_id");



CREATE UNIQUE INDEX "sso_domains_domain_idx" ON "auth"."sso_domains" USING "btree" ("lower"("domain"));



CREATE INDEX "sso_domains_sso_provider_id_idx" ON "auth"."sso_domains" USING "btree" ("sso_provider_id");



CREATE UNIQUE INDEX "sso_providers_resource_id_idx" ON "auth"."sso_providers" USING "btree" ("lower"("resource_id"));



CREATE UNIQUE INDEX "unique_phone_factor_per_user" ON "auth"."mfa_factors" USING "btree" ("user_id", "phone");



CREATE UNIQUE INDEX "unique_verified_phone_factor" ON "auth"."mfa_factors" USING "btree" ("user_id", "phone");



CREATE INDEX "user_id_created_at_idx" ON "auth"."sessions" USING "btree" ("user_id", "created_at");



CREATE UNIQUE INDEX "users_email_partial_key" ON "auth"."users" USING "btree" ("email") WHERE ("is_sso_user" = false);



COMMENT ON INDEX "auth"."users_email_partial_key" IS 'Auth: A partial unique index that applies only when is_sso_user is false';



CREATE INDEX "users_instance_id_email_idx" ON "auth"."users" USING "btree" ("instance_id", "lower"(("email")::"text"));



CREATE INDEX "users_instance_id_idx" ON "auth"."users" USING "btree" ("instance_id");



CREATE INDEX "users_is_anonymous_idx" ON "auth"."users" USING "btree" ("is_anonymous");



CREATE INDEX "activity_logs_action_idx" ON "public"."activity_logs" USING "btree" ("action");



CREATE INDEX "activity_logs_created_at_idx" ON "public"."activity_logs" USING "btree" ("created_at" DESC);



CREATE INDEX "activity_logs_entity_type_entity_id_idx" ON "public"."activity_logs" USING "btree" ("entity_type", "entity_id");



CREATE INDEX "activity_logs_entity_type_idx" ON "public"."activity_logs" USING "btree" ("entity_type");



CREATE INDEX "activity_logs_user_id_idx" ON "public"."activity_logs" USING "btree" ("user_id");



CREATE INDEX "idx_inmates_national_id" ON "public"."inmates" USING "hash" ("national_id");



CREATE INDEX "idx_override_room_date" ON "public"."overrides" USING "btree" ("room_id", "date");



CREATE INDEX "idx_section_override_section_date" ON "public"."section_overrides" USING "btree" ("section_id", "date");



CREATE UNIQUE INDEX "inmate_settings_inmate_id_key_key" ON "public"."inmate_settings" USING "btree" ("inmate_id", "key");



CREATE UNIQUE INDEX "inmates_inmate_code_key" ON "public"."inmates" USING "btree" ("inmate_code");



CREATE UNIQUE INDEX "inmates_status_name_key" ON "public"."inmates_status" USING "btree" ("name");



CREATE UNIQUE INDEX "room_status_name_key" ON "public"."room_status" USING "btree" ("name");



CREATE UNIQUE INDEX "servers_code_key" ON "public"."servers" USING "btree" ("code");



CREATE UNIQUE INDEX "servers_link_key" ON "public"."servers" USING "btree" ("link");



CREATE UNIQUE INDEX "unique_permission_name" ON "public"."permissions" USING "btree" ("name");



CREATE UNIQUE INDEX "unique_role_permission" ON "public"."role_permissions" USING "btree" ("role_id", "permission_id");



CREATE UNIQUE INDEX "users_email_key" ON "public"."users" USING "btree" ("email");



CREATE INDEX "users_notifications_seen_idx" ON "public"."users_notifications" USING "btree" ("seen");



CREATE INDEX "users_notifications_title_id_idx" ON "public"."users_notifications" USING "btree" ("title_id");



CREATE INDEX "users_notifications_visit_id_idx" ON "public"."users_notifications" USING "btree" ("visit_id");



CREATE INDEX "users_notifications_visitor_id_idx" ON "public"."users_notifications" USING "btree" ("visitor_id");



CREATE INDEX "users_notifications_visitor_id_visit_id_idx" ON "public"."users_notifications" USING "btree" ("visitor_id", "visit_id");



CREATE UNIQUE INDEX "users_user_auth_id_key" ON "public"."users" USING "btree" ("user_auth_id");



CREATE INDEX "visit_requests_created_at_idx" ON "public"."visit_requests" USING "btree" ("created_at");



CREATE INDEX "visit_requests_inmate_id_idx" ON "public"."visit_requests" USING "btree" ("inmate_id");



CREATE INDEX "visit_requests_status_id_idx" ON "public"."visit_requests" USING "btree" ("status_id");



CREATE UNIQUE INDEX "visit_requests_status_name_key" ON "public"."visit_requests_status" USING "btree" ("name");



CREATE INDEX "visit_requests_visitor_id_idx" ON "public"."visit_requests" USING "btree" ("visitor_id");



CREATE UNIQUE INDEX "visitors_national_id_key" ON "public"."visitors" USING "btree" ("national_id");



CREATE OR REPLACE TRIGGER "on_auth_user_created" AFTER INSERT ON "auth"."users" FOR EACH ROW EXECUTE FUNCTION "public"."user_trigger_function"();



CREATE OR REPLACE TRIGGER "user_delete_trigger" AFTER DELETE ON "auth"."users" FOR EACH ROW EXECUTE FUNCTION "public"."delete_user_from_public"();



CREATE OR REPLACE TRIGGER "log_availability_times_changes" AFTER INSERT OR DELETE OR UPDATE ON "public"."availability_times" FOR EACH ROW EXECUTE FUNCTION "public"."trigger_log_changes"();



CREATE OR REPLACE TRIGGER "log_inmate_availability_settings_changes" AFTER INSERT OR DELETE OR UPDATE ON "public"."inmate_availability_settings" FOR EACH ROW EXECUTE FUNCTION "public"."trigger_log_changes"();



CREATE OR REPLACE TRIGGER "log_inmate_settings_changes" AFTER INSERT OR DELETE OR UPDATE ON "public"."inmate_settings" FOR EACH ROW EXECUTE FUNCTION "public"."trigger_log_changes"();



CREATE OR REPLACE TRIGGER "log_inmate_visitor_documents_changes" AFTER INSERT OR DELETE OR UPDATE ON "public"."inmate_visitor_documents" FOR EACH ROW EXECUTE FUNCTION "public"."trigger_log_changes"();



CREATE OR REPLACE TRIGGER "log_inmate_visitors_changes" AFTER INSERT OR DELETE OR UPDATE ON "public"."inmate_visitors" FOR EACH ROW EXECUTE FUNCTION "public"."trigger_log_changes"();



CREATE OR REPLACE TRIGGER "log_inmates_changes" AFTER INSERT OR DELETE OR UPDATE ON "public"."inmates" FOR EACH ROW EXECUTE FUNCTION "public"."trigger_log_changes"();



CREATE OR REPLACE TRIGGER "log_inmates_status_changes" AFTER INSERT OR DELETE OR UPDATE ON "public"."inmates_status" FOR EACH ROW EXECUTE FUNCTION "public"."trigger_log_changes"();



CREATE OR REPLACE TRIGGER "log_meeting_feedbacks_changes" AFTER INSERT OR DELETE OR UPDATE ON "public"."meeting_feedbacks" FOR EACH ROW EXECUTE FUNCTION "public"."trigger_log_changes"();



CREATE OR REPLACE TRIGGER "log_meeting_records_changes" AFTER INSERT OR DELETE OR UPDATE ON "public"."meeting_records" FOR EACH ROW EXECUTE FUNCTION "public"."trigger_log_changes"();



CREATE OR REPLACE TRIGGER "log_notifications_changes" AFTER INSERT OR DELETE OR UPDATE ON "public"."notifications" FOR EACH ROW EXECUTE FUNCTION "public"."trigger_log_changes"();



CREATE OR REPLACE TRIGGER "log_permissions_changes" AFTER INSERT OR DELETE OR UPDATE ON "public"."permissions" FOR EACH ROW EXECUTE FUNCTION "public"."trigger_log_changes"();



CREATE OR REPLACE TRIGGER "log_role_permissions_changes" AFTER INSERT OR DELETE OR UPDATE ON "public"."role_permissions" FOR EACH ROW EXECUTE FUNCTION "public"."trigger_log_changes"();



CREATE OR REPLACE TRIGGER "log_roles_changes" AFTER INSERT OR DELETE OR UPDATE ON "public"."roles" FOR EACH ROW EXECUTE FUNCTION "public"."trigger_log_changes"();



CREATE OR REPLACE TRIGGER "log_room_availability_settings_changes" AFTER INSERT OR DELETE OR UPDATE ON "public"."room_availability_settings" FOR EACH ROW EXECUTE FUNCTION "public"."trigger_log_changes"();



CREATE OR REPLACE TRIGGER "log_room_slots_changes" AFTER INSERT OR DELETE OR UPDATE ON "public"."room_slots" FOR EACH ROW EXECUTE FUNCTION "public"."trigger_log_changes"();



CREATE OR REPLACE TRIGGER "log_room_status_changes" AFTER INSERT OR DELETE OR UPDATE ON "public"."room_status" FOR EACH ROW EXECUTE FUNCTION "public"."trigger_log_changes"();



CREATE OR REPLACE TRIGGER "log_rooms_changes" AFTER INSERT OR DELETE OR UPDATE ON "public"."rooms" FOR EACH ROW EXECUTE FUNCTION "public"."trigger_log_changes"();



CREATE OR REPLACE TRIGGER "log_section_availability_settings_changes" AFTER INSERT OR DELETE OR UPDATE ON "public"."section_availability_settings" FOR EACH ROW EXECUTE FUNCTION "public"."trigger_log_changes"();



CREATE OR REPLACE TRIGGER "log_sections_changes" AFTER INSERT OR DELETE OR UPDATE ON "public"."sections" FOR EACH ROW EXECUTE FUNCTION "public"."trigger_log_changes"();



CREATE OR REPLACE TRIGGER "log_settings_changes" AFTER INSERT OR DELETE OR UPDATE ON "public"."settings" FOR EACH ROW EXECUTE FUNCTION "public"."trigger_log_changes"();



CREATE OR REPLACE TRIGGER "log_user_permissions_changes" AFTER INSERT OR DELETE OR UPDATE ON "public"."user_permissions" FOR EACH ROW EXECUTE FUNCTION "public"."trigger_log_changes"();



CREATE OR REPLACE TRIGGER "log_users_changes" AFTER INSERT OR DELETE OR UPDATE ON "public"."users" FOR EACH ROW EXECUTE FUNCTION "public"."trigger_log_changes"();



CREATE OR REPLACE TRIGGER "log_visit_requests_changes" AFTER INSERT OR DELETE OR UPDATE ON "public"."visit_requests" FOR EACH ROW EXECUTE FUNCTION "public"."trigger_log_changes"();



CREATE OR REPLACE TRIGGER "log_visit_requests_status_changes" AFTER INSERT OR DELETE OR UPDATE ON "public"."visit_requests_status" FOR EACH ROW EXECUTE FUNCTION "public"."trigger_log_changes"();



CREATE OR REPLACE TRIGGER "log_visitors_changes" AFTER INSERT OR DELETE OR UPDATE ON "public"."visitors" FOR EACH ROW EXECUTE FUNCTION "public"."trigger_log_changes"();



CREATE OR REPLACE TRIGGER "quotes" AFTER INSERT OR UPDATE ON "public"."users" FOR EACH ROW EXECUTE FUNCTION "public"."quotes"();



CREATE OR REPLACE TRIGGER "request_status_trigger" AFTER INSERT OR UPDATE OF "status_id" ON "public"."visit_requests" FOR EACH ROW EXECUTE FUNCTION "public"."generate_user_notification"();



CREATE OR REPLACE TRIGGER "trg_update_full_name" BEFORE UPDATE ON "public"."users" FOR EACH ROW EXECUTE FUNCTION "public"."update_full_name"();



CREATE OR REPLACE TRIGGER "trigger_notify_on_visit_request_insert" AFTER INSERT ON "public"."visit_requests" FOR EACH ROW EXECUTE FUNCTION "public"."notify_on_visit_request_insert"();



CREATE OR REPLACE TRIGGER "trigger_remove_quotes" BEFORE INSERT OR UPDATE ON "public"."users" FOR EACH ROW EXECUTE FUNCTION "public"."remove_quotes_from_name"();



CREATE OR REPLACE TRIGGER "trigger_set_banned_until" AFTER UPDATE ON "public"."users" FOR EACH ROW WHEN ((("old"."deleted_at" IS NULL) AND ("new"."deleted_at" IS NOT NULL))) EXECUTE FUNCTION "public"."set_banned_until"();



CREATE OR REPLACE TRIGGER "trigger_update_first_response_time" BEFORE UPDATE OF "status_id" ON "public"."visit_requests" FOR EACH ROW WHEN (("old"."status_id" IS DISTINCT FROM "new"."status_id")) EXECUTE FUNCTION "public"."set_first_response_time"();



CREATE OR REPLACE TRIGGER "trigger_update_from_to" BEFORE INSERT OR UPDATE ON "public"."visit_requests" FOR EACH ROW EXECUTE FUNCTION "public"."update_from_to"();



CREATE OR REPLACE TRIGGER "trigger_update_visit" BEFORE UPDATE ON "public"."visit_requests" FOR EACH ROW WHEN (("old"."status_id" IS DISTINCT FROM "new"."status_id")) EXECUTE FUNCTION "public"."update_visit_title_and_status"();



CREATE OR REPLACE TRIGGER "trigger_visit_status_change" AFTER UPDATE ON "public"."visit_requests" FOR EACH ROW WHEN (("old"."status_id" IS DISTINCT FROM "new"."status_id")) EXECUTE FUNCTION "public"."notify_visit_status_change"();



ALTER TABLE ONLY "auth"."identities"
    ADD CONSTRAINT "identities_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "auth"."mfa_amr_claims"
    ADD CONSTRAINT "mfa_amr_claims_session_id_fkey" FOREIGN KEY ("session_id") REFERENCES "auth"."sessions"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "auth"."mfa_challenges"
    ADD CONSTRAINT "mfa_challenges_auth_factor_id_fkey" FOREIGN KEY ("factor_id") REFERENCES "auth"."mfa_factors"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "auth"."mfa_factors"
    ADD CONSTRAINT "mfa_factors_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "auth"."one_time_tokens"
    ADD CONSTRAINT "one_time_tokens_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "auth"."refresh_tokens"
    ADD CONSTRAINT "refresh_tokens_session_id_fkey" FOREIGN KEY ("session_id") REFERENCES "auth"."sessions"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "auth"."saml_providers"
    ADD CONSTRAINT "saml_providers_sso_provider_id_fkey" FOREIGN KEY ("sso_provider_id") REFERENCES "auth"."sso_providers"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "auth"."saml_relay_states"
    ADD CONSTRAINT "saml_relay_states_flow_state_id_fkey" FOREIGN KEY ("flow_state_id") REFERENCES "auth"."flow_state"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "auth"."saml_relay_states"
    ADD CONSTRAINT "saml_relay_states_sso_provider_id_fkey" FOREIGN KEY ("sso_provider_id") REFERENCES "auth"."sso_providers"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "auth"."sessions"
    ADD CONSTRAINT "sessions_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "auth"."sso_domains"
    ADD CONSTRAINT "sso_domains_sso_provider_id_fkey" FOREIGN KEY ("sso_provider_id") REFERENCES "auth"."sso_providers"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."activity_logs"
    ADD CONSTRAINT "activity_logs_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("user_auth_id");



ALTER TABLE ONLY "public"."overrides_timeslots"
    ADD CONSTRAINT "fk_override" FOREIGN KEY ("override_id") REFERENCES "public"."overrides"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."overrides"
    ADD CONSTRAINT "fk_room" FOREIGN KEY ("room_id") REFERENCES "public"."rooms"("id") ON DELETE RESTRICT;



ALTER TABLE ONLY "public"."section_overrides"
    ADD CONSTRAINT "fk_section" FOREIGN KEY ("section_id") REFERENCES "public"."sections"("id") ON DELETE RESTRICT;



ALTER TABLE ONLY "public"."section_overrides_timeslots"
    ADD CONSTRAINT "fk_section_override" FOREIGN KEY ("override_id") REFERENCES "public"."section_overrides"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."visit_requests"
    ADD CONSTRAINT "fk_visit_requests_rooms" FOREIGN KEY ("room_id") REFERENCES "public"."rooms"("id");



ALTER TABLE ONLY "public"."inmate_availability_settings"
    ADD CONSTRAINT "inmate_availability_settings_inmate_id_fkey" FOREIGN KEY ("inmate_id") REFERENCES "public"."inmates"("id") ON UPDATE CASCADE ON DELETE RESTRICT;



ALTER TABLE ONLY "public"."inmate_settings"
    ADD CONSTRAINT "inmate_settings_inmate_id_fkey" FOREIGN KEY ("inmate_id") REFERENCES "public"."inmates"("id") ON UPDATE CASCADE ON DELETE RESTRICT;



ALTER TABLE ONLY "public"."inmate_visitor_documents"
    ADD CONSTRAINT "inmate_visitor_documents_inmate_visitor_id_fkey" FOREIGN KEY ("inmate_visitor_id") REFERENCES "public"."inmate_visitors"("id") ON UPDATE CASCADE ON DELETE RESTRICT;



ALTER TABLE ONLY "public"."inmate_visitors"
    ADD CONSTRAINT "inmate_visitors_inmate_id_fkey" FOREIGN KEY ("inmate_id") REFERENCES "public"."inmates"("id") ON UPDATE CASCADE ON DELETE RESTRICT;



ALTER TABLE ONLY "public"."inmate_visitors"
    ADD CONSTRAINT "inmate_visitors_visitor_id_fkey" FOREIGN KEY ("visitor_id") REFERENCES "public"."visitors"("id") ON UPDATE CASCADE ON DELETE RESTRICT;



ALTER TABLE ONLY "public"."inmates"
    ADD CONSTRAINT "inmates_room_id_fkey" FOREIGN KEY ("room_id") REFERENCES "public"."rooms"("id") ON UPDATE CASCADE ON DELETE SET NULL;



ALTER TABLE ONLY "public"."inmates"
    ADD CONSTRAINT "inmates_section_id_fkey" FOREIGN KEY ("section_id") REFERENCES "public"."sections"("id") ON UPDATE CASCADE ON DELETE RESTRICT;



ALTER TABLE ONLY "public"."inmates"
    ADD CONSTRAINT "inmates_status_id_fkey" FOREIGN KEY ("status_id") REFERENCES "public"."inmates_status"("id") ON UPDATE CASCADE ON DELETE RESTRICT;



ALTER TABLE ONLY "public"."meeting_feedbacks"
    ADD CONSTRAINT "meeting_feedbacks_visit_request_id_fkey" FOREIGN KEY ("visit_request_id") REFERENCES "public"."visit_requests"("id") ON UPDATE CASCADE ON DELETE RESTRICT;



ALTER TABLE ONLY "public"."meeting_feedbacks"
    ADD CONSTRAINT "meeting_feedbacks_visitor_id_fkey" FOREIGN KEY ("visitor_id") REFERENCES "public"."visitors"("id") ON UPDATE CASCADE ON DELETE RESTRICT;



ALTER TABLE ONLY "public"."meeting_records"
    ADD CONSTRAINT "meeting_records_visit_request_id_fkey" FOREIGN KEY ("visit_request_id") REFERENCES "public"."visit_requests"("id") ON UPDATE CASCADE ON DELETE RESTRICT;



ALTER TABLE ONLY "public"."notifications"
    ADD CONSTRAINT "notifications_visit_id_fkey" FOREIGN KEY ("visit_id") REFERENCES "public"."visit_requests"("id") ON UPDATE CASCADE ON DELETE RESTRICT;



ALTER TABLE ONLY "public"."notifications"
    ADD CONSTRAINT "notifications_visitor_id_fkey" FOREIGN KEY ("visitor_id") REFERENCES "public"."visitors"("id") ON UPDATE CASCADE ON DELETE RESTRICT;



ALTER TABLE ONLY "public"."permissions"
    ADD CONSTRAINT "permissions_category_id_fkey" FOREIGN KEY ("category_id") REFERENCES "public"."permissions_categories"("id") ON UPDATE CASCADE ON DELETE RESTRICT;



ALTER TABLE ONLY "public"."users_notifications"
    ADD CONSTRAINT "public_users_notifications_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("user_auth_id") ON UPDATE CASCADE;



ALTER TABLE ONLY "public"."role_permissions"
    ADD CONSTRAINT "role_permissions_permission_id_fkey" FOREIGN KEY ("permission_id") REFERENCES "public"."permissions"("id") ON UPDATE CASCADE ON DELETE RESTRICT;



ALTER TABLE ONLY "public"."role_permissions"
    ADD CONSTRAINT "role_permissions_role_id_fkey" FOREIGN KEY ("role_id") REFERENCES "public"."roles"("id") ON UPDATE CASCADE ON DELETE RESTRICT;



ALTER TABLE ONLY "public"."room_availability_settings"
    ADD CONSTRAINT "room_availability_settings_room_id_fkey" FOREIGN KEY ("room_id") REFERENCES "public"."rooms"("id") ON UPDATE CASCADE ON DELETE RESTRICT;



ALTER TABLE ONLY "public"."room_slots"
    ADD CONSTRAINT "room_slots_room_id_fkey" FOREIGN KEY ("room_id") REFERENCES "public"."rooms"("id") ON UPDATE CASCADE ON DELETE RESTRICT;



ALTER TABLE ONLY "public"."room_slots"
    ADD CONSTRAINT "room_slots_visit_request_id_fkey" FOREIGN KEY ("visit_request_id") REFERENCES "public"."visit_requests"("id") ON UPDATE CASCADE ON DELETE RESTRICT;



ALTER TABLE ONLY "public"."rooms"
    ADD CONSTRAINT "rooms_section_id_fkey" FOREIGN KEY ("section_id") REFERENCES "public"."sections"("id") ON UPDATE CASCADE ON DELETE RESTRICT;



ALTER TABLE ONLY "public"."rooms"
    ADD CONSTRAINT "rooms_status_id_fkey" FOREIGN KEY ("status_id") REFERENCES "public"."room_status"("id") ON UPDATE CASCADE ON DELETE RESTRICT;



ALTER TABLE ONLY "public"."section_availability_settings"
    ADD CONSTRAINT "section_availability_settings_section_id_fkey" FOREIGN KEY ("section_id") REFERENCES "public"."sections"("id") ON UPDATE CASCADE ON DELETE RESTRICT;



ALTER TABLE ONLY "public"."user_permissions"
    ADD CONSTRAINT "user_permissions_permission_id_fkey" FOREIGN KEY ("permission_id") REFERENCES "public"."permissions"("id") ON UPDATE CASCADE ON DELETE RESTRICT;



ALTER TABLE ONLY "public"."user_permissions"
    ADD CONSTRAINT "user_permissions_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON UPDATE CASCADE ON DELETE RESTRICT;



ALTER TABLE ONLY "public"."users_notifications"
    ADD CONSTRAINT "users_notifications_title_id_fkey" FOREIGN KEY ("title_id") REFERENCES "public"."notification_titles"("id") ON UPDATE CASCADE ON DELETE RESTRICT;



ALTER TABLE ONLY "public"."users_notifications"
    ADD CONSTRAINT "users_notifications_visit_id_fkey" FOREIGN KEY ("visit_id") REFERENCES "public"."visit_requests"("id") ON UPDATE CASCADE ON DELETE RESTRICT;



ALTER TABLE ONLY "public"."users_notifications"
    ADD CONSTRAINT "users_notifications_visitor_id_fkey" FOREIGN KEY ("visitor_id") REFERENCES "public"."visitors"("id") ON UPDATE CASCADE ON DELETE RESTRICT;



ALTER TABLE ONLY "public"."users"
    ADD CONSTRAINT "users_role_id_fkey" FOREIGN KEY ("role_id") REFERENCES "public"."roles"("id") ON UPDATE CASCADE ON DELETE RESTRICT;



ALTER TABLE ONLY "public"."visit_requests"
    ADD CONSTRAINT "visit_requests_inmate_id_fkey" FOREIGN KEY ("inmate_id") REFERENCES "public"."inmates"("id") ON UPDATE CASCADE ON DELETE RESTRICT;



ALTER TABLE ONLY "public"."visit_requests"
    ADD CONSTRAINT "visit_requests_status_id_fkey" FOREIGN KEY ("status_id") REFERENCES "public"."visit_requests_status"("id") ON UPDATE CASCADE ON DELETE RESTRICT;



ALTER TABLE ONLY "public"."visit_requests"
    ADD CONSTRAINT "visit_requests_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("user_auth_id");



ALTER TABLE ONLY "public"."visit_requests"
    ADD CONSTRAINT "visit_requests_visitor_id_fkey" FOREIGN KEY ("visitor_id") REFERENCES "public"."visitors"("id") ON UPDATE CASCADE ON DELETE SET NULL;



ALTER TABLE "auth"."audit_log_entries" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "auth"."flow_state" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "auth"."identities" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "auth"."instances" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "auth"."mfa_amr_claims" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "auth"."mfa_challenges" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "auth"."mfa_factors" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "auth"."one_time_tokens" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "auth"."refresh_tokens" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "auth"."saml_providers" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "auth"."saml_relay_states" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "auth"."schema_migrations" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "auth"."sessions" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "auth"."sso_domains" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "auth"."sso_providers" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "auth"."users" ENABLE ROW LEVEL SECURITY;


GRANT USAGE ON SCHEMA "auth" TO "anon";
GRANT USAGE ON SCHEMA "auth" TO "authenticated";
GRANT USAGE ON SCHEMA "auth" TO "service_role";
GRANT ALL ON SCHEMA "auth" TO "supabase_auth_admin";
GRANT ALL ON SCHEMA "auth" TO "dashboard_user";
GRANT ALL ON SCHEMA "auth" TO "postgres";



REVOKE USAGE ON SCHEMA "public" FROM PUBLIC;
GRANT ALL ON SCHEMA "public" TO "anon";
GRANT USAGE ON SCHEMA "public" TO "authenticated";
GRANT USAGE ON SCHEMA "public" TO "service_role";
GRANT USAGE ON SCHEMA "public" TO "supabase_admin";
GRANT USAGE ON SCHEMA "public" TO "supabase_replication_admin";
GRANT USAGE ON SCHEMA "public" TO "supabase_read_only_user";
GRANT USAGE ON SCHEMA "public" TO "dashboard_user";
GRANT USAGE ON SCHEMA "public" TO "authenticator";
GRANT USAGE ON SCHEMA "public" TO "supabase_auth_admin";
GRANT USAGE ON SCHEMA "public" TO "supabase_functions_admin";
GRANT USAGE ON SCHEMA "public" TO "supabase_storage_admin";



GRANT ALL ON FUNCTION "auth"."email"() TO "dashboard_user";
GRANT ALL ON FUNCTION "auth"."email"() TO "anon";
GRANT ALL ON FUNCTION "auth"."email"() TO "authenticated";
GRANT ALL ON FUNCTION "auth"."email"() TO "service_role";



GRANT ALL ON FUNCTION "auth"."jwt"() TO "postgres";
GRANT ALL ON FUNCTION "auth"."jwt"() TO "dashboard_user";
GRANT ALL ON FUNCTION "auth"."jwt"() TO "anon";
GRANT ALL ON FUNCTION "auth"."jwt"() TO "authenticated";
GRANT ALL ON FUNCTION "auth"."jwt"() TO "service_role";



GRANT ALL ON FUNCTION "auth"."role"() TO "dashboard_user";
GRANT ALL ON FUNCTION "auth"."role"() TO "anon";
GRANT ALL ON FUNCTION "auth"."role"() TO "authenticated";
GRANT ALL ON FUNCTION "auth"."role"() TO "service_role";



GRANT ALL ON FUNCTION "auth"."uid"() TO "dashboard_user";
GRANT ALL ON FUNCTION "auth"."uid"() TO "anon";
GRANT ALL ON FUNCTION "auth"."uid"() TO "authenticated";
GRANT ALL ON FUNCTION "auth"."uid"() TO "service_role";



GRANT ALL ON FUNCTION "public"."approve_visit_if_whitelisted"() TO "anon";
GRANT ALL ON FUNCTION "public"."approve_visit_if_whitelisted"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."approve_visit_if_whitelisted"() TO "service_role";
GRANT ALL ON FUNCTION "public"."approve_visit_if_whitelisted"() TO "supabase_replication_admin";
GRANT ALL ON FUNCTION "public"."approve_visit_if_whitelisted"() TO "supabase_read_only_user";
GRANT ALL ON FUNCTION "public"."approve_visit_if_whitelisted"() TO "dashboard_user";
GRANT ALL ON FUNCTION "public"."approve_visit_if_whitelisted"() TO "authenticator";
GRANT ALL ON FUNCTION "public"."approve_visit_if_whitelisted"() TO "supabase_auth_admin";
GRANT ALL ON FUNCTION "public"."approve_visit_if_whitelisted"() TO "supabase_functions_admin";
GRANT ALL ON FUNCTION "public"."approve_visit_if_whitelisted"() TO "supabase_storage_admin";
GRANT ALL ON FUNCTION "public"."approve_visit_if_whitelisted"() TO "supabase_admin";



GRANT ALL ON FUNCTION "public"."assign_room_to_visit"() TO "anon";
GRANT ALL ON FUNCTION "public"."assign_room_to_visit"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."assign_room_to_visit"() TO "service_role";
GRANT ALL ON FUNCTION "public"."assign_room_to_visit"() TO "supabase_replication_admin";
GRANT ALL ON FUNCTION "public"."assign_room_to_visit"() TO "supabase_read_only_user";
GRANT ALL ON FUNCTION "public"."assign_room_to_visit"() TO "dashboard_user";
GRANT ALL ON FUNCTION "public"."assign_room_to_visit"() TO "authenticator";
GRANT ALL ON FUNCTION "public"."assign_room_to_visit"() TO "supabase_auth_admin";
GRANT ALL ON FUNCTION "public"."assign_room_to_visit"() TO "supabase_functions_admin";
GRANT ALL ON FUNCTION "public"."assign_room_to_visit"() TO "supabase_storage_admin";
GRANT ALL ON FUNCTION "public"."assign_room_to_visit"() TO "supabase_admin";



GRANT ALL ON FUNCTION "public"."delete_user_from_public"() TO "anon";
GRANT ALL ON FUNCTION "public"."delete_user_from_public"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."delete_user_from_public"() TO "service_role";
GRANT ALL ON FUNCTION "public"."delete_user_from_public"() TO "authenticator";
GRANT ALL ON FUNCTION "public"."delete_user_from_public"() TO "supabase_replication_admin";
GRANT ALL ON FUNCTION "public"."delete_user_from_public"() TO "supabase_read_only_user";
GRANT ALL ON FUNCTION "public"."delete_user_from_public"() TO "dashboard_user";
GRANT ALL ON FUNCTION "public"."delete_user_from_public"() TO "supabase_auth_admin";
GRANT ALL ON FUNCTION "public"."delete_user_from_public"() TO "supabase_functions_admin";
GRANT ALL ON FUNCTION "public"."delete_user_from_public"() TO "supabase_storage_admin";
GRANT ALL ON FUNCTION "public"."delete_user_from_public"() TO "supabase_admin";



GRANT ALL ON FUNCTION "public"."generate_user_notification"() TO "anon";
GRANT ALL ON FUNCTION "public"."generate_user_notification"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."generate_user_notification"() TO "service_role";
GRANT ALL ON FUNCTION "public"."generate_user_notification"() TO "supabase_replication_admin";
GRANT ALL ON FUNCTION "public"."generate_user_notification"() TO "supabase_read_only_user";
GRANT ALL ON FUNCTION "public"."generate_user_notification"() TO "dashboard_user";
GRANT ALL ON FUNCTION "public"."generate_user_notification"() TO "authenticator";
GRANT ALL ON FUNCTION "public"."generate_user_notification"() TO "supabase_auth_admin";
GRANT ALL ON FUNCTION "public"."generate_user_notification"() TO "supabase_functions_admin";
GRANT ALL ON FUNCTION "public"."generate_user_notification"() TO "supabase_storage_admin";
GRANT ALL ON FUNCTION "public"."generate_user_notification"() TO "supabase_admin";



GRANT ALL ON FUNCTION "public"."get_admin_requests_with_meeting_records"() TO "anon";
GRANT ALL ON FUNCTION "public"."get_admin_requests_with_meeting_records"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_admin_requests_with_meeting_records"() TO "service_role";
GRANT ALL ON FUNCTION "public"."get_admin_requests_with_meeting_records"() TO "supabase_replication_admin";
GRANT ALL ON FUNCTION "public"."get_admin_requests_with_meeting_records"() TO "supabase_read_only_user";
GRANT ALL ON FUNCTION "public"."get_admin_requests_with_meeting_records"() TO "dashboard_user";
GRANT ALL ON FUNCTION "public"."get_admin_requests_with_meeting_records"() TO "authenticator";
GRANT ALL ON FUNCTION "public"."get_admin_requests_with_meeting_records"() TO "supabase_auth_admin";
GRANT ALL ON FUNCTION "public"."get_admin_requests_with_meeting_records"() TO "supabase_functions_admin";
GRANT ALL ON FUNCTION "public"."get_admin_requests_with_meeting_records"() TO "supabase_storage_admin";
GRANT ALL ON FUNCTION "public"."get_admin_requests_with_meeting_records"() TO "supabase_admin";



GRANT ALL ON FUNCTION "public"."get_admin_requests_with_meeting_records_not_archived"() TO "anon";
GRANT ALL ON FUNCTION "public"."get_admin_requests_with_meeting_records_not_archived"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_admin_requests_with_meeting_records_not_archived"() TO "service_role";
GRANT ALL ON FUNCTION "public"."get_admin_requests_with_meeting_records_not_archived"() TO "supabase_replication_admin";
GRANT ALL ON FUNCTION "public"."get_admin_requests_with_meeting_records_not_archived"() TO "supabase_read_only_user";
GRANT ALL ON FUNCTION "public"."get_admin_requests_with_meeting_records_not_archived"() TO "dashboard_user";
GRANT ALL ON FUNCTION "public"."get_admin_requests_with_meeting_records_not_archived"() TO "authenticator";
GRANT ALL ON FUNCTION "public"."get_admin_requests_with_meeting_records_not_archived"() TO "supabase_auth_admin";
GRANT ALL ON FUNCTION "public"."get_admin_requests_with_meeting_records_not_archived"() TO "supabase_functions_admin";
GRANT ALL ON FUNCTION "public"."get_admin_requests_with_meeting_records_not_archived"() TO "supabase_storage_admin";
GRANT ALL ON FUNCTION "public"."get_admin_requests_with_meeting_records_not_archived"() TO "supabase_admin";



GRANT ALL ON FUNCTION "public"."get_visitor_requests_with_meeting_records"() TO "anon";
GRANT ALL ON FUNCTION "public"."get_visitor_requests_with_meeting_records"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_visitor_requests_with_meeting_records"() TO "service_role";
GRANT ALL ON FUNCTION "public"."get_visitor_requests_with_meeting_records"() TO "supabase_replication_admin";
GRANT ALL ON FUNCTION "public"."get_visitor_requests_with_meeting_records"() TO "supabase_read_only_user";
GRANT ALL ON FUNCTION "public"."get_visitor_requests_with_meeting_records"() TO "dashboard_user";
GRANT ALL ON FUNCTION "public"."get_visitor_requests_with_meeting_records"() TO "authenticator";
GRANT ALL ON FUNCTION "public"."get_visitor_requests_with_meeting_records"() TO "supabase_auth_admin";
GRANT ALL ON FUNCTION "public"."get_visitor_requests_with_meeting_records"() TO "supabase_functions_admin";
GRANT ALL ON FUNCTION "public"."get_visitor_requests_with_meeting_records"() TO "supabase_storage_admin";
GRANT ALL ON FUNCTION "public"."get_visitor_requests_with_meeting_records"() TO "supabase_admin";



GRANT ALL ON FUNCTION "public"."get_visitor_requests_with_meeting_records_not_archived"() TO "postgres";
GRANT ALL ON FUNCTION "public"."get_visitor_requests_with_meeting_records_not_archived"() TO "anon";
GRANT ALL ON FUNCTION "public"."get_visitor_requests_with_meeting_records_not_archived"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_visitor_requests_with_meeting_records_not_archived"() TO "service_role";



GRANT ALL ON FUNCTION "public"."get_visitor_requests_with_relation"("visitor_id_input" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."get_visitor_requests_with_relation"("visitor_id_input" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_visitor_requests_with_relation"("visitor_id_input" "uuid") TO "service_role";
GRANT ALL ON FUNCTION "public"."get_visitor_requests_with_relation"("visitor_id_input" "uuid") TO "supabase_replication_admin";
GRANT ALL ON FUNCTION "public"."get_visitor_requests_with_relation"("visitor_id_input" "uuid") TO "supabase_read_only_user";
GRANT ALL ON FUNCTION "public"."get_visitor_requests_with_relation"("visitor_id_input" "uuid") TO "dashboard_user";
GRANT ALL ON FUNCTION "public"."get_visitor_requests_with_relation"("visitor_id_input" "uuid") TO "authenticator";
GRANT ALL ON FUNCTION "public"."get_visitor_requests_with_relation"("visitor_id_input" "uuid") TO "supabase_auth_admin";
GRANT ALL ON FUNCTION "public"."get_visitor_requests_with_relation"("visitor_id_input" "uuid") TO "supabase_functions_admin";
GRANT ALL ON FUNCTION "public"."get_visitor_requests_with_relation"("visitor_id_input" "uuid") TO "supabase_storage_admin";
GRANT ALL ON FUNCTION "public"."get_visitor_requests_with_relation"("visitor_id_input" "uuid") TO "supabase_admin";



GRANT ALL ON FUNCTION "public"."get_visitor_visit_requests_with_meeting_records_archived"() TO "anon";
GRANT ALL ON FUNCTION "public"."get_visitor_visit_requests_with_meeting_records_archived"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_visitor_visit_requests_with_meeting_records_archived"() TO "service_role";
GRANT ALL ON FUNCTION "public"."get_visitor_visit_requests_with_meeting_records_archived"() TO "supabase_replication_admin";
GRANT ALL ON FUNCTION "public"."get_visitor_visit_requests_with_meeting_records_archived"() TO "supabase_read_only_user";
GRANT ALL ON FUNCTION "public"."get_visitor_visit_requests_with_meeting_records_archived"() TO "dashboard_user";
GRANT ALL ON FUNCTION "public"."get_visitor_visit_requests_with_meeting_records_archived"() TO "authenticator";
GRANT ALL ON FUNCTION "public"."get_visitor_visit_requests_with_meeting_records_archived"() TO "supabase_auth_admin";
GRANT ALL ON FUNCTION "public"."get_visitor_visit_requests_with_meeting_records_archived"() TO "supabase_functions_admin";
GRANT ALL ON FUNCTION "public"."get_visitor_visit_requests_with_meeting_records_archived"() TO "supabase_storage_admin";
GRANT ALL ON FUNCTION "public"."get_visitor_visit_requests_with_meeting_records_archived"() TO "supabase_admin";



GRANT ALL ON FUNCTION "public"."get_visitor_visit_requests_with_meeting_records_not_archived"() TO "postgres";
GRANT ALL ON FUNCTION "public"."get_visitor_visit_requests_with_meeting_records_not_archived"() TO "anon";
GRANT ALL ON FUNCTION "public"."get_visitor_visit_requests_with_meeting_records_not_archived"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_visitor_visit_requests_with_meeting_records_not_archived"() TO "service_role";



GRANT ALL ON FUNCTION "public"."handle_new_user"() TO "anon";
GRANT ALL ON FUNCTION "public"."handle_new_user"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."handle_new_user"() TO "service_role";
GRANT ALL ON FUNCTION "public"."handle_new_user"() TO "supabase_replication_admin";
GRANT ALL ON FUNCTION "public"."handle_new_user"() TO "supabase_read_only_user";
GRANT ALL ON FUNCTION "public"."handle_new_user"() TO "dashboard_user";
GRANT ALL ON FUNCTION "public"."handle_new_user"() TO "authenticator";
GRANT ALL ON FUNCTION "public"."handle_new_user"() TO "supabase_auth_admin";
GRANT ALL ON FUNCTION "public"."handle_new_user"() TO "supabase_functions_admin";
GRANT ALL ON FUNCTION "public"."handle_new_user"() TO "supabase_storage_admin";
GRANT ALL ON FUNCTION "public"."handle_new_user"() TO "supabase_admin";



GRANT ALL ON FUNCTION "public"."handle_public_user"() TO "anon";
GRANT ALL ON FUNCTION "public"."handle_public_user"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."handle_public_user"() TO "service_role";
GRANT ALL ON FUNCTION "public"."handle_public_user"() TO "supabase_replication_admin";
GRANT ALL ON FUNCTION "public"."handle_public_user"() TO "supabase_read_only_user";
GRANT ALL ON FUNCTION "public"."handle_public_user"() TO "dashboard_user";
GRANT ALL ON FUNCTION "public"."handle_public_user"() TO "authenticator";
GRANT ALL ON FUNCTION "public"."handle_public_user"() TO "supabase_auth_admin";
GRANT ALL ON FUNCTION "public"."handle_public_user"() TO "supabase_functions_admin";
GRANT ALL ON FUNCTION "public"."handle_public_user"() TO "supabase_storage_admin";
GRANT ALL ON FUNCTION "public"."handle_public_user"() TO "supabase_admin";



GRANT ALL ON FUNCTION "public"."log_activity"("p_user_id" "uuid", "p_user_email" "text", "p_entity_type" "text", "p_entity_id" "text", "p_action" "text", "p_action_group" "text", "p_old_data" "jsonb", "p_new_data" "jsonb", "p_metadata" "jsonb", "p_ip_address" "text", "p_user_agent" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."log_activity"("p_user_id" "uuid", "p_user_email" "text", "p_entity_type" "text", "p_entity_id" "text", "p_action" "text", "p_action_group" "text", "p_old_data" "jsonb", "p_new_data" "jsonb", "p_metadata" "jsonb", "p_ip_address" "text", "p_user_agent" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."log_activity"("p_user_id" "uuid", "p_user_email" "text", "p_entity_type" "text", "p_entity_id" "text", "p_action" "text", "p_action_group" "text", "p_old_data" "jsonb", "p_new_data" "jsonb", "p_metadata" "jsonb", "p_ip_address" "text", "p_user_agent" "text") TO "service_role";
GRANT ALL ON FUNCTION "public"."log_activity"("p_user_id" "uuid", "p_user_email" "text", "p_entity_type" "text", "p_entity_id" "text", "p_action" "text", "p_action_group" "text", "p_old_data" "jsonb", "p_new_data" "jsonb", "p_metadata" "jsonb", "p_ip_address" "text", "p_user_agent" "text") TO "supabase_replication_admin";
GRANT ALL ON FUNCTION "public"."log_activity"("p_user_id" "uuid", "p_user_email" "text", "p_entity_type" "text", "p_entity_id" "text", "p_action" "text", "p_action_group" "text", "p_old_data" "jsonb", "p_new_data" "jsonb", "p_metadata" "jsonb", "p_ip_address" "text", "p_user_agent" "text") TO "supabase_read_only_user";
GRANT ALL ON FUNCTION "public"."log_activity"("p_user_id" "uuid", "p_user_email" "text", "p_entity_type" "text", "p_entity_id" "text", "p_action" "text", "p_action_group" "text", "p_old_data" "jsonb", "p_new_data" "jsonb", "p_metadata" "jsonb", "p_ip_address" "text", "p_user_agent" "text") TO "dashboard_user";
GRANT ALL ON FUNCTION "public"."log_activity"("p_user_id" "uuid", "p_user_email" "text", "p_entity_type" "text", "p_entity_id" "text", "p_action" "text", "p_action_group" "text", "p_old_data" "jsonb", "p_new_data" "jsonb", "p_metadata" "jsonb", "p_ip_address" "text", "p_user_agent" "text") TO "authenticator";
GRANT ALL ON FUNCTION "public"."log_activity"("p_user_id" "uuid", "p_user_email" "text", "p_entity_type" "text", "p_entity_id" "text", "p_action" "text", "p_action_group" "text", "p_old_data" "jsonb", "p_new_data" "jsonb", "p_metadata" "jsonb", "p_ip_address" "text", "p_user_agent" "text") TO "supabase_auth_admin";
GRANT ALL ON FUNCTION "public"."log_activity"("p_user_id" "uuid", "p_user_email" "text", "p_entity_type" "text", "p_entity_id" "text", "p_action" "text", "p_action_group" "text", "p_old_data" "jsonb", "p_new_data" "jsonb", "p_metadata" "jsonb", "p_ip_address" "text", "p_user_agent" "text") TO "supabase_functions_admin";
GRANT ALL ON FUNCTION "public"."log_activity"("p_user_id" "uuid", "p_user_email" "text", "p_entity_type" "text", "p_entity_id" "text", "p_action" "text", "p_action_group" "text", "p_old_data" "jsonb", "p_new_data" "jsonb", "p_metadata" "jsonb", "p_ip_address" "text", "p_user_agent" "text") TO "supabase_storage_admin";
GRANT ALL ON FUNCTION "public"."log_activity"("p_user_id" "uuid", "p_user_email" "text", "p_entity_type" "text", "p_entity_id" "text", "p_action" "text", "p_action_group" "text", "p_old_data" "jsonb", "p_new_data" "jsonb", "p_metadata" "jsonb", "p_ip_address" "text", "p_user_agent" "text") TO "supabase_admin";



GRANT ALL ON FUNCTION "public"."log_custom_activity"("p_entity_type" "text", "p_entity_id" "text", "p_action" "text", "p_action_group" "text", "p_metadata" "jsonb") TO "anon";
GRANT ALL ON FUNCTION "public"."log_custom_activity"("p_entity_type" "text", "p_entity_id" "text", "p_action" "text", "p_action_group" "text", "p_metadata" "jsonb") TO "authenticated";
GRANT ALL ON FUNCTION "public"."log_custom_activity"("p_entity_type" "text", "p_entity_id" "text", "p_action" "text", "p_action_group" "text", "p_metadata" "jsonb") TO "service_role";
GRANT ALL ON FUNCTION "public"."log_custom_activity"("p_entity_type" "text", "p_entity_id" "text", "p_action" "text", "p_action_group" "text", "p_metadata" "jsonb") TO "supabase_replication_admin";
GRANT ALL ON FUNCTION "public"."log_custom_activity"("p_entity_type" "text", "p_entity_id" "text", "p_action" "text", "p_action_group" "text", "p_metadata" "jsonb") TO "supabase_read_only_user";
GRANT ALL ON FUNCTION "public"."log_custom_activity"("p_entity_type" "text", "p_entity_id" "text", "p_action" "text", "p_action_group" "text", "p_metadata" "jsonb") TO "dashboard_user";
GRANT ALL ON FUNCTION "public"."log_custom_activity"("p_entity_type" "text", "p_entity_id" "text", "p_action" "text", "p_action_group" "text", "p_metadata" "jsonb") TO "authenticator";
GRANT ALL ON FUNCTION "public"."log_custom_activity"("p_entity_type" "text", "p_entity_id" "text", "p_action" "text", "p_action_group" "text", "p_metadata" "jsonb") TO "supabase_auth_admin";
GRANT ALL ON FUNCTION "public"."log_custom_activity"("p_entity_type" "text", "p_entity_id" "text", "p_action" "text", "p_action_group" "text", "p_metadata" "jsonb") TO "supabase_functions_admin";
GRANT ALL ON FUNCTION "public"."log_custom_activity"("p_entity_type" "text", "p_entity_id" "text", "p_action" "text", "p_action_group" "text", "p_metadata" "jsonb") TO "supabase_storage_admin";
GRANT ALL ON FUNCTION "public"."log_custom_activity"("p_entity_type" "text", "p_entity_id" "text", "p_action" "text", "p_action_group" "text", "p_metadata" "jsonb") TO "supabase_admin";



GRANT ALL ON FUNCTION "public"."notify_on_visit_request_insert"() TO "anon";
GRANT ALL ON FUNCTION "public"."notify_on_visit_request_insert"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."notify_on_visit_request_insert"() TO "service_role";



GRANT ALL ON FUNCTION "public"."notify_visit_status_change"() TO "anon";
GRANT ALL ON FUNCTION "public"."notify_visit_status_change"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."notify_visit_status_change"() TO "service_role";
GRANT ALL ON FUNCTION "public"."notify_visit_status_change"() TO "supabase_replication_admin";
GRANT ALL ON FUNCTION "public"."notify_visit_status_change"() TO "supabase_read_only_user";
GRANT ALL ON FUNCTION "public"."notify_visit_status_change"() TO "dashboard_user";
GRANT ALL ON FUNCTION "public"."notify_visit_status_change"() TO "authenticator";
GRANT ALL ON FUNCTION "public"."notify_visit_status_change"() TO "supabase_auth_admin";
GRANT ALL ON FUNCTION "public"."notify_visit_status_change"() TO "supabase_functions_admin";
GRANT ALL ON FUNCTION "public"."notify_visit_status_change"() TO "supabase_storage_admin";
GRANT ALL ON FUNCTION "public"."notify_visit_status_change"() TO "supabase_admin";



GRANT ALL ON FUNCTION "public"."quotes"() TO "anon";
GRANT ALL ON FUNCTION "public"."quotes"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."quotes"() TO "service_role";
GRANT ALL ON FUNCTION "public"."quotes"() TO "supabase_replication_admin";
GRANT ALL ON FUNCTION "public"."quotes"() TO "supabase_read_only_user";
GRANT ALL ON FUNCTION "public"."quotes"() TO "dashboard_user";
GRANT ALL ON FUNCTION "public"."quotes"() TO "authenticator";
GRANT ALL ON FUNCTION "public"."quotes"() TO "supabase_auth_admin";
GRANT ALL ON FUNCTION "public"."quotes"() TO "supabase_functions_admin";
GRANT ALL ON FUNCTION "public"."quotes"() TO "supabase_storage_admin";
GRANT ALL ON FUNCTION "public"."quotes"() TO "supabase_admin";



GRANT ALL ON FUNCTION "public"."remove_quotes_from_name"() TO "anon";
GRANT ALL ON FUNCTION "public"."remove_quotes_from_name"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."remove_quotes_from_name"() TO "service_role";
GRANT ALL ON FUNCTION "public"."remove_quotes_from_name"() TO "supabase_replication_admin";
GRANT ALL ON FUNCTION "public"."remove_quotes_from_name"() TO "supabase_read_only_user";
GRANT ALL ON FUNCTION "public"."remove_quotes_from_name"() TO "dashboard_user";
GRANT ALL ON FUNCTION "public"."remove_quotes_from_name"() TO "authenticator";
GRANT ALL ON FUNCTION "public"."remove_quotes_from_name"() TO "supabase_auth_admin";
GRANT ALL ON FUNCTION "public"."remove_quotes_from_name"() TO "supabase_functions_admin";
GRANT ALL ON FUNCTION "public"."remove_quotes_from_name"() TO "supabase_storage_admin";
GRANT ALL ON FUNCTION "public"."remove_quotes_from_name"() TO "supabase_admin";



GRANT ALL ON FUNCTION "public"."remove_quotes_from_names"() TO "anon";
GRANT ALL ON FUNCTION "public"."remove_quotes_from_names"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."remove_quotes_from_names"() TO "service_role";
GRANT ALL ON FUNCTION "public"."remove_quotes_from_names"() TO "supabase_replication_admin";
GRANT ALL ON FUNCTION "public"."remove_quotes_from_names"() TO "supabase_read_only_user";
GRANT ALL ON FUNCTION "public"."remove_quotes_from_names"() TO "dashboard_user";
GRANT ALL ON FUNCTION "public"."remove_quotes_from_names"() TO "authenticator";
GRANT ALL ON FUNCTION "public"."remove_quotes_from_names"() TO "supabase_auth_admin";
GRANT ALL ON FUNCTION "public"."remove_quotes_from_names"() TO "supabase_functions_admin";
GRANT ALL ON FUNCTION "public"."remove_quotes_from_names"() TO "supabase_storage_admin";
GRANT ALL ON FUNCTION "public"."remove_quotes_from_names"() TO "supabase_admin";



GRANT ALL ON FUNCTION "public"."set_banned_until"() TO "anon";
GRANT ALL ON FUNCTION "public"."set_banned_until"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."set_banned_until"() TO "service_role";
GRANT ALL ON FUNCTION "public"."set_banned_until"() TO "supabase_replication_admin";
GRANT ALL ON FUNCTION "public"."set_banned_until"() TO "supabase_read_only_user";
GRANT ALL ON FUNCTION "public"."set_banned_until"() TO "dashboard_user";
GRANT ALL ON FUNCTION "public"."set_banned_until"() TO "authenticator";
GRANT ALL ON FUNCTION "public"."set_banned_until"() TO "supabase_auth_admin";
GRANT ALL ON FUNCTION "public"."set_banned_until"() TO "supabase_functions_admin";
GRANT ALL ON FUNCTION "public"."set_banned_until"() TO "supabase_storage_admin";
GRANT ALL ON FUNCTION "public"."set_banned_until"() TO "supabase_admin";



GRANT ALL ON FUNCTION "public"."set_first_response_time"() TO "anon";
GRANT ALL ON FUNCTION "public"."set_first_response_time"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."set_first_response_time"() TO "service_role";
GRANT ALL ON FUNCTION "public"."set_first_response_time"() TO "supabase_replication_admin";
GRANT ALL ON FUNCTION "public"."set_first_response_time"() TO "supabase_read_only_user";
GRANT ALL ON FUNCTION "public"."set_first_response_time"() TO "dashboard_user";
GRANT ALL ON FUNCTION "public"."set_first_response_time"() TO "authenticator";
GRANT ALL ON FUNCTION "public"."set_first_response_time"() TO "supabase_auth_admin";
GRANT ALL ON FUNCTION "public"."set_first_response_time"() TO "supabase_functions_admin";
GRANT ALL ON FUNCTION "public"."set_first_response_time"() TO "supabase_storage_admin";
GRANT ALL ON FUNCTION "public"."set_first_response_time"() TO "supabase_admin";



GRANT ALL ON FUNCTION "public"."sync_all_auth_users"() TO "anon";
GRANT ALL ON FUNCTION "public"."sync_all_auth_users"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."sync_all_auth_users"() TO "service_role";



GRANT ALL ON FUNCTION "public"."trigger_log_changes"() TO "anon";
GRANT ALL ON FUNCTION "public"."trigger_log_changes"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."trigger_log_changes"() TO "service_role";
GRANT ALL ON FUNCTION "public"."trigger_log_changes"() TO "supabase_replication_admin";
GRANT ALL ON FUNCTION "public"."trigger_log_changes"() TO "supabase_read_only_user";
GRANT ALL ON FUNCTION "public"."trigger_log_changes"() TO "dashboard_user";
GRANT ALL ON FUNCTION "public"."trigger_log_changes"() TO "authenticator";
GRANT ALL ON FUNCTION "public"."trigger_log_changes"() TO "supabase_auth_admin";
GRANT ALL ON FUNCTION "public"."trigger_log_changes"() TO "supabase_functions_admin";
GRANT ALL ON FUNCTION "public"."trigger_log_changes"() TO "supabase_storage_admin";
GRANT ALL ON FUNCTION "public"."trigger_log_changes"() TO "supabase_admin";



GRANT ALL ON FUNCTION "public"."update_from_to"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_from_to"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_from_to"() TO "service_role";
GRANT ALL ON FUNCTION "public"."update_from_to"() TO "supabase_replication_admin";
GRANT ALL ON FUNCTION "public"."update_from_to"() TO "supabase_read_only_user";
GRANT ALL ON FUNCTION "public"."update_from_to"() TO "dashboard_user";
GRANT ALL ON FUNCTION "public"."update_from_to"() TO "authenticator";
GRANT ALL ON FUNCTION "public"."update_from_to"() TO "supabase_auth_admin";
GRANT ALL ON FUNCTION "public"."update_from_to"() TO "supabase_functions_admin";
GRANT ALL ON FUNCTION "public"."update_from_to"() TO "supabase_storage_admin";
GRANT ALL ON FUNCTION "public"."update_from_to"() TO "supabase_admin";



GRANT ALL ON FUNCTION "public"."update_full_name"() TO "postgres";
GRANT ALL ON FUNCTION "public"."update_full_name"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_full_name"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_full_name"() TO "service_role";



GRANT ALL ON FUNCTION "public"."update_visit_request_status"("request_id" "uuid", "status_name" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."update_visit_request_status"("request_id" "uuid", "status_name" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_visit_request_status"("request_id" "uuid", "status_name" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."update_visit_request_status_vtext"("visit_request_id" "text", "status_name" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."update_visit_request_status_vtext"("visit_request_id" "text", "status_name" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_visit_request_status_vtext"("visit_request_id" "text", "status_name" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."update_visit_title_and_status"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_visit_title_and_status"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_visit_title_and_status"() TO "service_role";
GRANT ALL ON FUNCTION "public"."update_visit_title_and_status"() TO "supabase_replication_admin";
GRANT ALL ON FUNCTION "public"."update_visit_title_and_status"() TO "supabase_read_only_user";
GRANT ALL ON FUNCTION "public"."update_visit_title_and_status"() TO "dashboard_user";
GRANT ALL ON FUNCTION "public"."update_visit_title_and_status"() TO "authenticator";
GRANT ALL ON FUNCTION "public"."update_visit_title_and_status"() TO "supabase_auth_admin";
GRANT ALL ON FUNCTION "public"."update_visit_title_and_status"() TO "supabase_functions_admin";
GRANT ALL ON FUNCTION "public"."update_visit_title_and_status"() TO "supabase_storage_admin";
GRANT ALL ON FUNCTION "public"."update_visit_title_and_status"() TO "supabase_admin";



GRANT ALL ON FUNCTION "public"."user_trigger_function"() TO "anon";
GRANT ALL ON FUNCTION "public"."user_trigger_function"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."user_trigger_function"() TO "service_role";
GRANT ALL ON FUNCTION "public"."user_trigger_function"() TO "supabase_replication_admin";
GRANT ALL ON FUNCTION "public"."user_trigger_function"() TO "supabase_read_only_user";
GRANT ALL ON FUNCTION "public"."user_trigger_function"() TO "dashboard_user";
GRANT ALL ON FUNCTION "public"."user_trigger_function"() TO "authenticator";
GRANT ALL ON FUNCTION "public"."user_trigger_function"() TO "supabase_auth_admin";
GRANT ALL ON FUNCTION "public"."user_trigger_function"() TO "supabase_functions_admin";
GRANT ALL ON FUNCTION "public"."user_trigger_function"() TO "supabase_storage_admin";
GRANT ALL ON FUNCTION "public"."user_trigger_function"() TO "supabase_admin";



GRANT ALL ON TABLE "auth"."audit_log_entries" TO "dashboard_user";
GRANT INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE "auth"."audit_log_entries" TO "postgres";
GRANT SELECT ON TABLE "auth"."audit_log_entries" TO "postgres" WITH GRANT OPTION;
SET SESSION AUTHORIZATION "postgres";
GRANT SELECT ON TABLE "auth"."audit_log_entries" TO "postgres" WITH GRANT OPTION;
RESET SESSION AUTHORIZATION;
GRANT ALL ON TABLE "auth"."audit_log_entries" TO "anon";
GRANT ALL ON TABLE "auth"."audit_log_entries" TO "authenticated";
GRANT ALL ON TABLE "auth"."audit_log_entries" TO "service_role";
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "auth"."audit_log_entries" TO PUBLIC;



GRANT INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE "auth"."flow_state" TO "postgres";
GRANT SELECT ON TABLE "auth"."flow_state" TO "postgres" WITH GRANT OPTION;
GRANT ALL ON TABLE "auth"."flow_state" TO "dashboard_user";
SET SESSION AUTHORIZATION "postgres";
GRANT SELECT ON TABLE "auth"."flow_state" TO "postgres" WITH GRANT OPTION;
RESET SESSION AUTHORIZATION;
GRANT ALL ON TABLE "auth"."flow_state" TO "anon";
GRANT ALL ON TABLE "auth"."flow_state" TO "authenticated";
GRANT ALL ON TABLE "auth"."flow_state" TO "service_role";
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "auth"."flow_state" TO PUBLIC;



GRANT INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE "auth"."identities" TO "postgres";
GRANT SELECT ON TABLE "auth"."identities" TO "postgres" WITH GRANT OPTION;
GRANT ALL ON TABLE "auth"."identities" TO "dashboard_user";
SET SESSION AUTHORIZATION "postgres";
GRANT SELECT ON TABLE "auth"."identities" TO "postgres" WITH GRANT OPTION;
RESET SESSION AUTHORIZATION;
GRANT ALL ON TABLE "auth"."identities" TO "anon";
GRANT ALL ON TABLE "auth"."identities" TO "authenticated";
GRANT ALL ON TABLE "auth"."identities" TO "service_role";
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "auth"."identities" TO PUBLIC;



GRANT ALL ON TABLE "auth"."instances" TO "dashboard_user";
GRANT INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE "auth"."instances" TO "postgres";
GRANT SELECT ON TABLE "auth"."instances" TO "postgres" WITH GRANT OPTION;
SET SESSION AUTHORIZATION "postgres";
GRANT SELECT ON TABLE "auth"."instances" TO "postgres" WITH GRANT OPTION;
RESET SESSION AUTHORIZATION;
GRANT ALL ON TABLE "auth"."instances" TO "anon";
GRANT ALL ON TABLE "auth"."instances" TO "authenticated";
GRANT ALL ON TABLE "auth"."instances" TO "service_role";
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "auth"."instances" TO PUBLIC;



GRANT INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE "auth"."mfa_amr_claims" TO "postgres";
GRANT SELECT ON TABLE "auth"."mfa_amr_claims" TO "postgres" WITH GRANT OPTION;
GRANT ALL ON TABLE "auth"."mfa_amr_claims" TO "dashboard_user";
SET SESSION AUTHORIZATION "postgres";
GRANT SELECT ON TABLE "auth"."mfa_amr_claims" TO "postgres" WITH GRANT OPTION;
RESET SESSION AUTHORIZATION;
GRANT ALL ON TABLE "auth"."mfa_amr_claims" TO "anon";
GRANT ALL ON TABLE "auth"."mfa_amr_claims" TO "authenticated";
GRANT ALL ON TABLE "auth"."mfa_amr_claims" TO "service_role";
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "auth"."mfa_amr_claims" TO PUBLIC;



GRANT INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE "auth"."mfa_challenges" TO "postgres";
GRANT SELECT ON TABLE "auth"."mfa_challenges" TO "postgres" WITH GRANT OPTION;
GRANT ALL ON TABLE "auth"."mfa_challenges" TO "dashboard_user";
SET SESSION AUTHORIZATION "postgres";
GRANT SELECT ON TABLE "auth"."mfa_challenges" TO "postgres" WITH GRANT OPTION;
RESET SESSION AUTHORIZATION;
GRANT ALL ON TABLE "auth"."mfa_challenges" TO "anon";
GRANT ALL ON TABLE "auth"."mfa_challenges" TO "authenticated";
GRANT ALL ON TABLE "auth"."mfa_challenges" TO "service_role";
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "auth"."mfa_challenges" TO PUBLIC;



GRANT INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE "auth"."mfa_factors" TO "postgres";
GRANT SELECT ON TABLE "auth"."mfa_factors" TO "postgres" WITH GRANT OPTION;
GRANT ALL ON TABLE "auth"."mfa_factors" TO "dashboard_user";
SET SESSION AUTHORIZATION "postgres";
GRANT SELECT ON TABLE "auth"."mfa_factors" TO "postgres" WITH GRANT OPTION;
RESET SESSION AUTHORIZATION;
GRANT ALL ON TABLE "auth"."mfa_factors" TO "anon";
GRANT ALL ON TABLE "auth"."mfa_factors" TO "authenticated";
GRANT ALL ON TABLE "auth"."mfa_factors" TO "service_role";
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "auth"."mfa_factors" TO PUBLIC;



GRANT INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE "auth"."one_time_tokens" TO "postgres";
GRANT SELECT ON TABLE "auth"."one_time_tokens" TO "postgres" WITH GRANT OPTION;
GRANT ALL ON TABLE "auth"."one_time_tokens" TO "dashboard_user";
SET SESSION AUTHORIZATION "postgres";
GRANT SELECT ON TABLE "auth"."one_time_tokens" TO "postgres" WITH GRANT OPTION;
RESET SESSION AUTHORIZATION;
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "auth"."one_time_tokens" TO PUBLIC;



GRANT ALL ON TABLE "auth"."refresh_tokens" TO "dashboard_user";
GRANT INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE "auth"."refresh_tokens" TO "postgres";
GRANT SELECT ON TABLE "auth"."refresh_tokens" TO "postgres" WITH GRANT OPTION;
SET SESSION AUTHORIZATION "postgres";
GRANT SELECT ON TABLE "auth"."refresh_tokens" TO "postgres" WITH GRANT OPTION;
RESET SESSION AUTHORIZATION;
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "auth"."refresh_tokens" TO PUBLIC;



GRANT ALL ON SEQUENCE "auth"."refresh_tokens_id_seq" TO "dashboard_user";
GRANT ALL ON SEQUENCE "auth"."refresh_tokens_id_seq" TO "postgres";
GRANT ALL ON SEQUENCE "auth"."refresh_tokens_id_seq" TO PUBLIC;



GRANT INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE "auth"."saml_providers" TO "postgres";
GRANT SELECT ON TABLE "auth"."saml_providers" TO "postgres" WITH GRANT OPTION;
GRANT ALL ON TABLE "auth"."saml_providers" TO "dashboard_user";
SET SESSION AUTHORIZATION "postgres";
GRANT SELECT ON TABLE "auth"."saml_providers" TO "postgres" WITH GRANT OPTION;
RESET SESSION AUTHORIZATION;
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "auth"."saml_providers" TO PUBLIC;



GRANT INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE "auth"."saml_relay_states" TO "postgres";
GRANT SELECT ON TABLE "auth"."saml_relay_states" TO "postgres" WITH GRANT OPTION;
GRANT ALL ON TABLE "auth"."saml_relay_states" TO "dashboard_user";
SET SESSION AUTHORIZATION "postgres";
GRANT SELECT ON TABLE "auth"."saml_relay_states" TO "postgres" WITH GRANT OPTION;
RESET SESSION AUTHORIZATION;
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "auth"."saml_relay_states" TO PUBLIC;



GRANT ALL ON TABLE "auth"."schema_migrations" TO "dashboard_user";
GRANT INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE "auth"."schema_migrations" TO "postgres";
GRANT SELECT ON TABLE "auth"."schema_migrations" TO "postgres" WITH GRANT OPTION;
SET SESSION AUTHORIZATION "postgres";
GRANT SELECT ON TABLE "auth"."schema_migrations" TO "postgres" WITH GRANT OPTION;
RESET SESSION AUTHORIZATION;
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "auth"."schema_migrations" TO PUBLIC;



GRANT INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE "auth"."sessions" TO "postgres";
GRANT SELECT ON TABLE "auth"."sessions" TO "postgres" WITH GRANT OPTION;
GRANT ALL ON TABLE "auth"."sessions" TO "dashboard_user";
SET SESSION AUTHORIZATION "postgres";
GRANT SELECT ON TABLE "auth"."sessions" TO "postgres" WITH GRANT OPTION;
RESET SESSION AUTHORIZATION;
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "auth"."sessions" TO PUBLIC;



GRANT INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE "auth"."sso_domains" TO "postgres";
GRANT SELECT ON TABLE "auth"."sso_domains" TO "postgres" WITH GRANT OPTION;
GRANT ALL ON TABLE "auth"."sso_domains" TO "dashboard_user";
SET SESSION AUTHORIZATION "postgres";
GRANT SELECT ON TABLE "auth"."sso_domains" TO "postgres" WITH GRANT OPTION;
RESET SESSION AUTHORIZATION;
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "auth"."sso_domains" TO PUBLIC;



GRANT INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE "auth"."sso_providers" TO "postgres";
GRANT SELECT ON TABLE "auth"."sso_providers" TO "postgres" WITH GRANT OPTION;
GRANT ALL ON TABLE "auth"."sso_providers" TO "dashboard_user";
SET SESSION AUTHORIZATION "postgres";
GRANT SELECT ON TABLE "auth"."sso_providers" TO "postgres" WITH GRANT OPTION;
RESET SESSION AUTHORIZATION;
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "auth"."sso_providers" TO PUBLIC;



GRANT ALL ON TABLE "auth"."users" TO "dashboard_user";
GRANT INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE "auth"."users" TO "postgres";
GRANT SELECT ON TABLE "auth"."users" TO "postgres" WITH GRANT OPTION;
SET SESSION AUTHORIZATION "postgres";
GRANT SELECT ON TABLE "auth"."users" TO "postgres" WITH GRANT OPTION;
RESET SESSION AUTHORIZATION;
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "auth"."users" TO PUBLIC;
GRANT INSERT,DELETE,UPDATE ON TABLE "auth"."users" TO "anon";



GRANT ALL ON TABLE "public"."_prisma_migrations" TO "anon";
GRANT ALL ON TABLE "public"."_prisma_migrations" TO "authenticated";
GRANT ALL ON TABLE "public"."_prisma_migrations" TO "service_role";
GRANT ALL ON TABLE "public"."_prisma_migrations" TO "supabase_admin";
GRANT SELECT ON TABLE "public"."_prisma_migrations" TO "supabase_replication_admin";
GRANT SELECT ON TABLE "public"."_prisma_migrations" TO "supabase_read_only_user";
GRANT ALL ON TABLE "public"."_prisma_migrations" TO "dashboard_user";
GRANT SELECT ON TABLE "public"."_prisma_migrations" TO "authenticator";
GRANT SELECT ON TABLE "public"."_prisma_migrations" TO "supabase_auth_admin";
GRANT SELECT ON TABLE "public"."_prisma_migrations" TO "supabase_functions_admin";
GRANT ALL ON TABLE "public"."_prisma_migrations" TO "supabase_storage_admin";



GRANT ALL ON TABLE "public"."activity_logs" TO "anon";
GRANT ALL ON TABLE "public"."activity_logs" TO "authenticated";
GRANT ALL ON TABLE "public"."activity_logs" TO "service_role";
GRANT SELECT ON TABLE "public"."activity_logs" TO "supabase_replication_admin";
GRANT SELECT ON TABLE "public"."activity_logs" TO "supabase_read_only_user";
GRANT ALL ON TABLE "public"."activity_logs" TO "dashboard_user";
GRANT SELECT ON TABLE "public"."activity_logs" TO "authenticator";
GRANT SELECT ON TABLE "public"."activity_logs" TO "supabase_auth_admin";
GRANT SELECT ON TABLE "public"."activity_logs" TO "supabase_functions_admin";
GRANT ALL ON TABLE "public"."activity_logs" TO "supabase_storage_admin";
GRANT ALL ON TABLE "public"."activity_logs" TO "supabase_admin";



GRANT ALL ON TABLE "public"."availability_times" TO "anon";
GRANT ALL ON TABLE "public"."availability_times" TO "authenticated";
GRANT ALL ON TABLE "public"."availability_times" TO "service_role";
GRANT ALL ON TABLE "public"."availability_times" TO "supabase_admin";
GRANT SELECT ON TABLE "public"."availability_times" TO "supabase_replication_admin";
GRANT SELECT ON TABLE "public"."availability_times" TO "supabase_read_only_user";
GRANT ALL ON TABLE "public"."availability_times" TO "dashboard_user";
GRANT SELECT ON TABLE "public"."availability_times" TO "authenticator";
GRANT SELECT ON TABLE "public"."availability_times" TO "supabase_auth_admin";
GRANT SELECT ON TABLE "public"."availability_times" TO "supabase_functions_admin";
GRANT ALL ON TABLE "public"."availability_times" TO "supabase_storage_admin";



GRANT ALL ON TABLE "public"."function_logs" TO "anon";
GRANT ALL ON TABLE "public"."function_logs" TO "authenticated";
GRANT ALL ON TABLE "public"."function_logs" TO "service_role";



GRANT ALL ON TABLE "public"."inmate_availability_settings" TO "anon";
GRANT ALL ON TABLE "public"."inmate_availability_settings" TO "authenticated";
GRANT ALL ON TABLE "public"."inmate_availability_settings" TO "service_role";
GRANT ALL ON TABLE "public"."inmate_availability_settings" TO "supabase_admin";
GRANT SELECT ON TABLE "public"."inmate_availability_settings" TO "supabase_replication_admin";
GRANT SELECT ON TABLE "public"."inmate_availability_settings" TO "supabase_read_only_user";
GRANT ALL ON TABLE "public"."inmate_availability_settings" TO "dashboard_user";
GRANT SELECT ON TABLE "public"."inmate_availability_settings" TO "authenticator";
GRANT SELECT ON TABLE "public"."inmate_availability_settings" TO "supabase_auth_admin";
GRANT SELECT ON TABLE "public"."inmate_availability_settings" TO "supabase_functions_admin";
GRANT ALL ON TABLE "public"."inmate_availability_settings" TO "supabase_storage_admin";



GRANT ALL ON TABLE "public"."inmate_settings" TO "anon";
GRANT ALL ON TABLE "public"."inmate_settings" TO "authenticated";
GRANT ALL ON TABLE "public"."inmate_settings" TO "service_role";
GRANT ALL ON TABLE "public"."inmate_settings" TO "supabase_admin";
GRANT SELECT ON TABLE "public"."inmate_settings" TO "supabase_replication_admin";
GRANT SELECT ON TABLE "public"."inmate_settings" TO "supabase_read_only_user";
GRANT ALL ON TABLE "public"."inmate_settings" TO "dashboard_user";
GRANT SELECT ON TABLE "public"."inmate_settings" TO "authenticator";
GRANT SELECT ON TABLE "public"."inmate_settings" TO "supabase_auth_admin";
GRANT SELECT ON TABLE "public"."inmate_settings" TO "supabase_functions_admin";
GRANT ALL ON TABLE "public"."inmate_settings" TO "supabase_storage_admin";



GRANT ALL ON TABLE "public"."inmate_visitor_documents" TO "anon";
GRANT ALL ON TABLE "public"."inmate_visitor_documents" TO "authenticated";
GRANT ALL ON TABLE "public"."inmate_visitor_documents" TO "service_role";
GRANT ALL ON TABLE "public"."inmate_visitor_documents" TO "supabase_admin";
GRANT SELECT ON TABLE "public"."inmate_visitor_documents" TO "supabase_replication_admin";
GRANT SELECT ON TABLE "public"."inmate_visitor_documents" TO "supabase_read_only_user";
GRANT ALL ON TABLE "public"."inmate_visitor_documents" TO "dashboard_user";
GRANT SELECT ON TABLE "public"."inmate_visitor_documents" TO "authenticator";
GRANT SELECT ON TABLE "public"."inmate_visitor_documents" TO "supabase_auth_admin";
GRANT SELECT ON TABLE "public"."inmate_visitor_documents" TO "supabase_functions_admin";
GRANT ALL ON TABLE "public"."inmate_visitor_documents" TO "supabase_storage_admin";



GRANT ALL ON TABLE "public"."inmate_visitors" TO "anon";
GRANT ALL ON TABLE "public"."inmate_visitors" TO "authenticated";
GRANT ALL ON TABLE "public"."inmate_visitors" TO "service_role";
GRANT ALL ON TABLE "public"."inmate_visitors" TO "supabase_admin";
GRANT SELECT ON TABLE "public"."inmate_visitors" TO "supabase_replication_admin";
GRANT SELECT ON TABLE "public"."inmate_visitors" TO "supabase_read_only_user";
GRANT ALL ON TABLE "public"."inmate_visitors" TO "dashboard_user";
GRANT SELECT ON TABLE "public"."inmate_visitors" TO "authenticator";
GRANT SELECT ON TABLE "public"."inmate_visitors" TO "supabase_auth_admin";
GRANT SELECT ON TABLE "public"."inmate_visitors" TO "supabase_functions_admin";
GRANT ALL ON TABLE "public"."inmate_visitors" TO "supabase_storage_admin";



GRANT ALL ON TABLE "public"."inmates" TO "anon";
GRANT ALL ON TABLE "public"."inmates" TO "authenticated";
GRANT ALL ON TABLE "public"."inmates" TO "service_role";
GRANT ALL ON TABLE "public"."inmates" TO "supabase_admin";
GRANT SELECT ON TABLE "public"."inmates" TO "supabase_replication_admin";
GRANT SELECT ON TABLE "public"."inmates" TO "supabase_read_only_user";
GRANT ALL ON TABLE "public"."inmates" TO "dashboard_user";
GRANT SELECT ON TABLE "public"."inmates" TO "authenticator";
GRANT SELECT ON TABLE "public"."inmates" TO "supabase_auth_admin";
GRANT SELECT ON TABLE "public"."inmates" TO "supabase_functions_admin";
GRANT ALL ON TABLE "public"."inmates" TO "supabase_storage_admin";



GRANT ALL ON TABLE "public"."inmates_status" TO "anon";
GRANT ALL ON TABLE "public"."inmates_status" TO "authenticated";
GRANT ALL ON TABLE "public"."inmates_status" TO "service_role";
GRANT ALL ON TABLE "public"."inmates_status" TO "supabase_admin";
GRANT SELECT ON TABLE "public"."inmates_status" TO "supabase_replication_admin";
GRANT SELECT ON TABLE "public"."inmates_status" TO "supabase_read_only_user";
GRANT ALL ON TABLE "public"."inmates_status" TO "dashboard_user";
GRANT SELECT ON TABLE "public"."inmates_status" TO "authenticator";
GRANT SELECT ON TABLE "public"."inmates_status" TO "supabase_auth_admin";
GRANT SELECT ON TABLE "public"."inmates_status" TO "supabase_functions_admin";
GRANT ALL ON TABLE "public"."inmates_status" TO "supabase_storage_admin";



GRANT ALL ON SEQUENCE "public"."inmates_status_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."inmates_status_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."inmates_status_id_seq" TO "service_role";
GRANT SELECT,USAGE ON SEQUENCE "public"."inmates_status_id_seq" TO "supabase_admin";



GRANT ALL ON TABLE "public"."meeting_feedbacks" TO "anon";
GRANT ALL ON TABLE "public"."meeting_feedbacks" TO "authenticated";
GRANT ALL ON TABLE "public"."meeting_feedbacks" TO "service_role";
GRANT ALL ON TABLE "public"."meeting_feedbacks" TO "supabase_admin";
GRANT SELECT ON TABLE "public"."meeting_feedbacks" TO "supabase_replication_admin";
GRANT SELECT ON TABLE "public"."meeting_feedbacks" TO "supabase_read_only_user";
GRANT ALL ON TABLE "public"."meeting_feedbacks" TO "dashboard_user";
GRANT SELECT ON TABLE "public"."meeting_feedbacks" TO "authenticator";
GRANT SELECT ON TABLE "public"."meeting_feedbacks" TO "supabase_auth_admin";
GRANT SELECT ON TABLE "public"."meeting_feedbacks" TO "supabase_functions_admin";
GRANT ALL ON TABLE "public"."meeting_feedbacks" TO "supabase_storage_admin";



GRANT ALL ON TABLE "public"."meeting_records" TO "anon";
GRANT ALL ON TABLE "public"."meeting_records" TO "authenticated";
GRANT ALL ON TABLE "public"."meeting_records" TO "service_role";
GRANT ALL ON TABLE "public"."meeting_records" TO "supabase_admin";
GRANT SELECT ON TABLE "public"."meeting_records" TO "supabase_replication_admin";
GRANT SELECT ON TABLE "public"."meeting_records" TO "supabase_read_only_user";
GRANT ALL ON TABLE "public"."meeting_records" TO "dashboard_user";
GRANT SELECT ON TABLE "public"."meeting_records" TO "authenticator";
GRANT SELECT ON TABLE "public"."meeting_records" TO "supabase_auth_admin";
GRANT SELECT ON TABLE "public"."meeting_records" TO "supabase_functions_admin";
GRANT ALL ON TABLE "public"."meeting_records" TO "supabase_storage_admin";



GRANT ALL ON TABLE "public"."notification_titles" TO "anon";
GRANT ALL ON TABLE "public"."notification_titles" TO "authenticated";
GRANT ALL ON TABLE "public"."notification_titles" TO "service_role";
GRANT SELECT ON TABLE "public"."notification_titles" TO "supabase_replication_admin";
GRANT SELECT ON TABLE "public"."notification_titles" TO "supabase_read_only_user";
GRANT ALL ON TABLE "public"."notification_titles" TO "dashboard_user";
GRANT SELECT ON TABLE "public"."notification_titles" TO "authenticator";
GRANT SELECT ON TABLE "public"."notification_titles" TO "supabase_auth_admin";
GRANT SELECT ON TABLE "public"."notification_titles" TO "supabase_functions_admin";
GRANT ALL ON TABLE "public"."notification_titles" TO "supabase_storage_admin";
GRANT ALL ON TABLE "public"."notification_titles" TO "supabase_admin";



GRANT ALL ON TABLE "public"."notifications" TO "anon";
GRANT ALL ON TABLE "public"."notifications" TO "authenticated";
GRANT ALL ON TABLE "public"."notifications" TO "service_role";
GRANT ALL ON TABLE "public"."notifications" TO "supabase_admin";
GRANT SELECT ON TABLE "public"."notifications" TO "supabase_replication_admin";
GRANT SELECT ON TABLE "public"."notifications" TO "supabase_read_only_user";
GRANT ALL ON TABLE "public"."notifications" TO "dashboard_user";
GRANT SELECT ON TABLE "public"."notifications" TO "authenticator";
GRANT SELECT ON TABLE "public"."notifications" TO "supabase_auth_admin";
GRANT SELECT ON TABLE "public"."notifications" TO "supabase_functions_admin";
GRANT ALL ON TABLE "public"."notifications" TO "supabase_storage_admin";



GRANT ALL ON TABLE "public"."overrides" TO "postgres";
GRANT ALL ON TABLE "public"."overrides" TO "anon";
GRANT ALL ON TABLE "public"."overrides" TO "authenticated";
GRANT ALL ON TABLE "public"."overrides" TO "service_role";



GRANT ALL ON TABLE "public"."overrides_timeslots" TO "postgres";
GRANT ALL ON TABLE "public"."overrides_timeslots" TO "anon";
GRANT ALL ON TABLE "public"."overrides_timeslots" TO "authenticated";
GRANT ALL ON TABLE "public"."overrides_timeslots" TO "service_role";



GRANT ALL ON TABLE "public"."permissions" TO "anon";
GRANT ALL ON TABLE "public"."permissions" TO "authenticated";
GRANT ALL ON TABLE "public"."permissions" TO "service_role";
GRANT ALL ON TABLE "public"."permissions" TO "supabase_admin";
GRANT SELECT ON TABLE "public"."permissions" TO "supabase_replication_admin";
GRANT SELECT ON TABLE "public"."permissions" TO "supabase_read_only_user";
GRANT ALL ON TABLE "public"."permissions" TO "dashboard_user";
GRANT SELECT ON TABLE "public"."permissions" TO "authenticator";
GRANT SELECT ON TABLE "public"."permissions" TO "supabase_auth_admin";
GRANT SELECT ON TABLE "public"."permissions" TO "supabase_functions_admin";
GRANT ALL ON TABLE "public"."permissions" TO "supabase_storage_admin";



GRANT ALL ON TABLE "public"."permissions_categories" TO "anon";
GRANT ALL ON TABLE "public"."permissions_categories" TO "authenticated";
GRANT ALL ON TABLE "public"."permissions_categories" TO "service_role";
GRANT SELECT ON TABLE "public"."permissions_categories" TO "supabase_replication_admin";
GRANT SELECT ON TABLE "public"."permissions_categories" TO "supabase_read_only_user";
GRANT ALL ON TABLE "public"."permissions_categories" TO "dashboard_user";
GRANT SELECT ON TABLE "public"."permissions_categories" TO "authenticator";
GRANT SELECT ON TABLE "public"."permissions_categories" TO "supabase_auth_admin";
GRANT SELECT ON TABLE "public"."permissions_categories" TO "supabase_functions_admin";
GRANT ALL ON TABLE "public"."permissions_categories" TO "supabase_storage_admin";
GRANT ALL ON TABLE "public"."permissions_categories" TO "supabase_admin";



GRANT ALL ON SEQUENCE "public"."permissions_categories_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."permissions_categories_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."permissions_categories_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."role_permissions" TO "anon";
GRANT ALL ON TABLE "public"."role_permissions" TO "authenticated";
GRANT ALL ON TABLE "public"."role_permissions" TO "service_role";
GRANT ALL ON TABLE "public"."role_permissions" TO "supabase_admin";
GRANT SELECT ON TABLE "public"."role_permissions" TO "supabase_replication_admin";
GRANT SELECT ON TABLE "public"."role_permissions" TO "supabase_read_only_user";
GRANT ALL ON TABLE "public"."role_permissions" TO "dashboard_user";
GRANT SELECT ON TABLE "public"."role_permissions" TO "authenticator";
GRANT SELECT ON TABLE "public"."role_permissions" TO "supabase_auth_admin";
GRANT SELECT ON TABLE "public"."role_permissions" TO "supabase_functions_admin";
GRANT ALL ON TABLE "public"."role_permissions" TO "supabase_storage_admin";



GRANT ALL ON TABLE "public"."roles" TO "anon";
GRANT ALL ON TABLE "public"."roles" TO "authenticated";
GRANT ALL ON TABLE "public"."roles" TO "service_role";
GRANT ALL ON TABLE "public"."roles" TO "supabase_admin";
GRANT SELECT ON TABLE "public"."roles" TO "supabase_replication_admin";
GRANT SELECT ON TABLE "public"."roles" TO "supabase_read_only_user";
GRANT ALL ON TABLE "public"."roles" TO "dashboard_user";
GRANT SELECT ON TABLE "public"."roles" TO "authenticator";
GRANT SELECT ON TABLE "public"."roles" TO "supabase_auth_admin";
GRANT SELECT ON TABLE "public"."roles" TO "supabase_functions_admin";
GRANT ALL ON TABLE "public"."roles" TO "supabase_storage_admin";



GRANT ALL ON TABLE "public"."role_permissions_view" TO "anon";
GRANT ALL ON TABLE "public"."role_permissions_view" TO "authenticated";
GRANT ALL ON TABLE "public"."role_permissions_view" TO "service_role";



GRANT ALL ON TABLE "public"."room_availability_settings" TO "anon";
GRANT ALL ON TABLE "public"."room_availability_settings" TO "authenticated";
GRANT ALL ON TABLE "public"."room_availability_settings" TO "service_role";
GRANT ALL ON TABLE "public"."room_availability_settings" TO "supabase_admin";
GRANT SELECT ON TABLE "public"."room_availability_settings" TO "supabase_replication_admin";
GRANT SELECT ON TABLE "public"."room_availability_settings" TO "supabase_read_only_user";
GRANT ALL ON TABLE "public"."room_availability_settings" TO "dashboard_user";
GRANT SELECT ON TABLE "public"."room_availability_settings" TO "authenticator";
GRANT SELECT ON TABLE "public"."room_availability_settings" TO "supabase_auth_admin";
GRANT SELECT ON TABLE "public"."room_availability_settings" TO "supabase_functions_admin";
GRANT ALL ON TABLE "public"."room_availability_settings" TO "supabase_storage_admin";



GRANT ALL ON TABLE "public"."room_slots" TO "anon";
GRANT ALL ON TABLE "public"."room_slots" TO "authenticated";
GRANT ALL ON TABLE "public"."room_slots" TO "service_role";
GRANT ALL ON TABLE "public"."room_slots" TO "supabase_admin";
GRANT SELECT ON TABLE "public"."room_slots" TO "supabase_replication_admin";
GRANT SELECT ON TABLE "public"."room_slots" TO "supabase_read_only_user";
GRANT ALL ON TABLE "public"."room_slots" TO "dashboard_user";
GRANT SELECT ON TABLE "public"."room_slots" TO "authenticator";
GRANT SELECT ON TABLE "public"."room_slots" TO "supabase_auth_admin";
GRANT SELECT ON TABLE "public"."room_slots" TO "supabase_functions_admin";
GRANT ALL ON TABLE "public"."room_slots" TO "supabase_storage_admin";



GRANT ALL ON TABLE "public"."room_status" TO "anon";
GRANT ALL ON TABLE "public"."room_status" TO "authenticated";
GRANT ALL ON TABLE "public"."room_status" TO "service_role";
GRANT ALL ON TABLE "public"."room_status" TO "supabase_admin";
GRANT SELECT ON TABLE "public"."room_status" TO "supabase_replication_admin";
GRANT SELECT ON TABLE "public"."room_status" TO "supabase_read_only_user";
GRANT ALL ON TABLE "public"."room_status" TO "dashboard_user";
GRANT SELECT ON TABLE "public"."room_status" TO "authenticator";
GRANT SELECT ON TABLE "public"."room_status" TO "supabase_auth_admin";
GRANT SELECT ON TABLE "public"."room_status" TO "supabase_functions_admin";
GRANT ALL ON TABLE "public"."room_status" TO "supabase_storage_admin";



GRANT ALL ON SEQUENCE "public"."room_status_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."room_status_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."room_status_id_seq" TO "service_role";
GRANT SELECT,USAGE ON SEQUENCE "public"."room_status_id_seq" TO "supabase_admin";



GRANT ALL ON TABLE "public"."rooms" TO "anon";
GRANT ALL ON TABLE "public"."rooms" TO "authenticated";
GRANT ALL ON TABLE "public"."rooms" TO "service_role";
GRANT ALL ON TABLE "public"."rooms" TO "supabase_admin";
GRANT SELECT ON TABLE "public"."rooms" TO "supabase_replication_admin";
GRANT SELECT ON TABLE "public"."rooms" TO "supabase_read_only_user";
GRANT ALL ON TABLE "public"."rooms" TO "dashboard_user";
GRANT SELECT ON TABLE "public"."rooms" TO "authenticator";
GRANT SELECT ON TABLE "public"."rooms" TO "supabase_auth_admin";
GRANT SELECT ON TABLE "public"."rooms" TO "supabase_functions_admin";
GRANT ALL ON TABLE "public"."rooms" TO "supabase_storage_admin";



GRANT ALL ON TABLE "public"."schema_migrations" TO "anon";
GRANT ALL ON TABLE "public"."schema_migrations" TO "authenticated";
GRANT ALL ON TABLE "public"."schema_migrations" TO "service_role";



GRANT ALL ON TABLE "public"."section_availability_settings" TO "anon";
GRANT ALL ON TABLE "public"."section_availability_settings" TO "authenticated";
GRANT ALL ON TABLE "public"."section_availability_settings" TO "service_role";
GRANT ALL ON TABLE "public"."section_availability_settings" TO "supabase_admin";
GRANT SELECT ON TABLE "public"."section_availability_settings" TO "supabase_replication_admin";
GRANT SELECT ON TABLE "public"."section_availability_settings" TO "supabase_read_only_user";
GRANT ALL ON TABLE "public"."section_availability_settings" TO "dashboard_user";
GRANT SELECT ON TABLE "public"."section_availability_settings" TO "authenticator";
GRANT SELECT ON TABLE "public"."section_availability_settings" TO "supabase_auth_admin";
GRANT SELECT ON TABLE "public"."section_availability_settings" TO "supabase_functions_admin";
GRANT ALL ON TABLE "public"."section_availability_settings" TO "supabase_storage_admin";



GRANT ALL ON TABLE "public"."section_overrides" TO "postgres";
GRANT ALL ON TABLE "public"."section_overrides" TO "anon";
GRANT ALL ON TABLE "public"."section_overrides" TO "authenticated";
GRANT ALL ON TABLE "public"."section_overrides" TO "service_role";



GRANT ALL ON TABLE "public"."section_overrides_timeslots" TO "postgres";
GRANT ALL ON TABLE "public"."section_overrides_timeslots" TO "anon";
GRANT ALL ON TABLE "public"."section_overrides_timeslots" TO "authenticated";
GRANT ALL ON TABLE "public"."section_overrides_timeslots" TO "service_role";



GRANT ALL ON TABLE "public"."sections" TO "anon";
GRANT ALL ON TABLE "public"."sections" TO "authenticated";
GRANT ALL ON TABLE "public"."sections" TO "service_role";
GRANT ALL ON TABLE "public"."sections" TO "supabase_admin";
GRANT SELECT ON TABLE "public"."sections" TO "supabase_replication_admin";
GRANT SELECT ON TABLE "public"."sections" TO "supabase_read_only_user";
GRANT ALL ON TABLE "public"."sections" TO "dashboard_user";
GRANT SELECT ON TABLE "public"."sections" TO "authenticator";
GRANT SELECT ON TABLE "public"."sections" TO "supabase_auth_admin";
GRANT SELECT ON TABLE "public"."sections" TO "supabase_functions_admin";
GRANT ALL ON TABLE "public"."sections" TO "supabase_storage_admin";



GRANT ALL ON TABLE "public"."servers" TO "anon";
GRANT ALL ON TABLE "public"."servers" TO "authenticated";
GRANT ALL ON TABLE "public"."servers" TO "service_role";
GRANT SELECT ON TABLE "public"."servers" TO "supabase_replication_admin";
GRANT SELECT ON TABLE "public"."servers" TO "supabase_read_only_user";
GRANT ALL ON TABLE "public"."servers" TO "dashboard_user";
GRANT SELECT ON TABLE "public"."servers" TO "authenticator";
GRANT SELECT ON TABLE "public"."servers" TO "supabase_auth_admin";
GRANT SELECT ON TABLE "public"."servers" TO "supabase_functions_admin";
GRANT ALL ON TABLE "public"."servers" TO "supabase_storage_admin";
GRANT ALL ON TABLE "public"."servers" TO "supabase_admin";



GRANT ALL ON TABLE "public"."settings" TO "anon";
GRANT ALL ON TABLE "public"."settings" TO "authenticated";
GRANT ALL ON TABLE "public"."settings" TO "service_role";
GRANT ALL ON TABLE "public"."settings" TO "supabase_admin";
GRANT SELECT ON TABLE "public"."settings" TO "supabase_replication_admin";
GRANT SELECT ON TABLE "public"."settings" TO "supabase_read_only_user";
GRANT ALL ON TABLE "public"."settings" TO "dashboard_user";
GRANT SELECT ON TABLE "public"."settings" TO "authenticator";
GRANT SELECT ON TABLE "public"."settings" TO "supabase_auth_admin";
GRANT SELECT ON TABLE "public"."settings" TO "supabase_functions_admin";
GRANT ALL ON TABLE "public"."settings" TO "supabase_storage_admin";



GRANT ALL ON TABLE "public"."tenants" TO "anon";
GRANT ALL ON TABLE "public"."tenants" TO "authenticated";
GRANT ALL ON TABLE "public"."tenants" TO "service_role";
GRANT SELECT ON TABLE "public"."tenants" TO PUBLIC;



GRANT ALL ON TABLE "public"."user_permissions" TO "anon";
GRANT ALL ON TABLE "public"."user_permissions" TO "authenticated";
GRANT ALL ON TABLE "public"."user_permissions" TO "service_role";
GRANT ALL ON TABLE "public"."user_permissions" TO "supabase_admin";
GRANT SELECT ON TABLE "public"."user_permissions" TO "supabase_replication_admin";
GRANT SELECT ON TABLE "public"."user_permissions" TO "supabase_read_only_user";
GRANT ALL ON TABLE "public"."user_permissions" TO "dashboard_user";
GRANT SELECT ON TABLE "public"."user_permissions" TO "authenticator";
GRANT SELECT ON TABLE "public"."user_permissions" TO "supabase_auth_admin";
GRANT SELECT ON TABLE "public"."user_permissions" TO "supabase_functions_admin";
GRANT ALL ON TABLE "public"."user_permissions" TO "supabase_storage_admin";



GRANT ALL ON TABLE "public"."users" TO "anon";
GRANT ALL ON TABLE "public"."users" TO "authenticated";
GRANT ALL ON TABLE "public"."users" TO "service_role";
GRANT SELECT,DELETE ON TABLE "public"."users" TO "authenticator";
GRANT ALL ON TABLE "public"."users" TO "supabase_admin";
GRANT SELECT ON TABLE "public"."users" TO "supabase_replication_admin";
GRANT SELECT ON TABLE "public"."users" TO "supabase_read_only_user";
GRANT ALL ON TABLE "public"."users" TO "dashboard_user";
GRANT SELECT ON TABLE "public"."users" TO "supabase_auth_admin";
GRANT SELECT ON TABLE "public"."users" TO "supabase_functions_admin";
GRANT ALL ON TABLE "public"."users" TO "supabase_storage_admin";



GRANT ALL ON TABLE "public"."users_notifications" TO "anon";
GRANT ALL ON TABLE "public"."users_notifications" TO "authenticated";
GRANT ALL ON TABLE "public"."users_notifications" TO "service_role";
GRANT ALL ON TABLE "public"."users_notifications" TO "supabase_admin";
GRANT SELECT ON TABLE "public"."users_notifications" TO "supabase_replication_admin";
GRANT SELECT ON TABLE "public"."users_notifications" TO "supabase_read_only_user";
GRANT ALL ON TABLE "public"."users_notifications" TO "dashboard_user";
GRANT SELECT ON TABLE "public"."users_notifications" TO "authenticator";
GRANT SELECT ON TABLE "public"."users_notifications" TO "supabase_auth_admin";
GRANT SELECT ON TABLE "public"."users_notifications" TO "supabase_functions_admin";
GRANT ALL ON TABLE "public"."users_notifications" TO "supabase_storage_admin";



GRANT ALL ON TABLE "public"."visit_requests" TO "anon";
GRANT ALL ON TABLE "public"."visit_requests" TO "authenticated";
GRANT ALL ON TABLE "public"."visit_requests" TO "service_role";
GRANT ALL ON TABLE "public"."visit_requests" TO "supabase_admin";
GRANT SELECT ON TABLE "public"."visit_requests" TO "supabase_replication_admin";
GRANT SELECT ON TABLE "public"."visit_requests" TO "supabase_read_only_user";
GRANT ALL ON TABLE "public"."visit_requests" TO "dashboard_user";
GRANT SELECT ON TABLE "public"."visit_requests" TO "authenticator";
GRANT SELECT ON TABLE "public"."visit_requests" TO "supabase_auth_admin";
GRANT SELECT ON TABLE "public"."visit_requests" TO "supabase_functions_admin";
GRANT ALL ON TABLE "public"."visit_requests" TO "supabase_storage_admin";



GRANT ALL ON TABLE "public"."visit_requests_status" TO "anon";
GRANT ALL ON TABLE "public"."visit_requests_status" TO "authenticated";
GRANT ALL ON TABLE "public"."visit_requests_status" TO "service_role";
GRANT ALL ON TABLE "public"."visit_requests_status" TO "supabase_admin";
GRANT SELECT ON TABLE "public"."visit_requests_status" TO "supabase_replication_admin";
GRANT SELECT ON TABLE "public"."visit_requests_status" TO "supabase_read_only_user";
GRANT ALL ON TABLE "public"."visit_requests_status" TO "dashboard_user";
GRANT SELECT ON TABLE "public"."visit_requests_status" TO "authenticator";
GRANT SELECT ON TABLE "public"."visit_requests_status" TO "supabase_auth_admin";
GRANT SELECT ON TABLE "public"."visit_requests_status" TO "supabase_functions_admin";
GRANT ALL ON TABLE "public"."visit_requests_status" TO "supabase_storage_admin";



GRANT ALL ON SEQUENCE "public"."visit_requests_status_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."visit_requests_status_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."visit_requests_status_id_seq" TO "service_role";
GRANT SELECT,USAGE ON SEQUENCE "public"."visit_requests_status_id_seq" TO "supabase_admin";



GRANT ALL ON TABLE "public"."visitors" TO "anon";
GRANT ALL ON TABLE "public"."visitors" TO "authenticated";
GRANT ALL ON TABLE "public"."visitors" TO "service_role";
GRANT ALL ON TABLE "public"."visitors" TO "supabase_admin";
GRANT SELECT ON TABLE "public"."visitors" TO "supabase_replication_admin";
GRANT SELECT ON TABLE "public"."visitors" TO "supabase_read_only_user";
GRANT ALL ON TABLE "public"."visitors" TO "dashboard_user";
GRANT SELECT ON TABLE "public"."visitors" TO "authenticator";
GRANT SELECT ON TABLE "public"."visitors" TO "supabase_auth_admin";
GRANT SELECT ON TABLE "public"."visitors" TO "supabase_functions_admin";
GRANT ALL ON TABLE "public"."visitors" TO "supabase_storage_admin";



GRANT ALL ON TABLE "public"."visit_requests_view" TO "postgres";
GRANT ALL ON TABLE "public"."visit_requests_view" TO "anon";
GRANT ALL ON TABLE "public"."visit_requests_view" TO "authenticated";
GRANT ALL ON TABLE "public"."visit_requests_view" TO "service_role";



GRANT ALL ON SEQUENCE "public"."visit_requests_visit_number_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."visit_requests_visit_number_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."visit_requests_visit_number_seq" TO "service_role";
GRANT SELECT,USAGE ON SEQUENCE "public"."visit_requests_visit_number_seq" TO "supabase_admin";



ALTER DEFAULT PRIVILEGES FOR ROLE "supabase_auth_admin" IN SCHEMA "auth" GRANT ALL ON SEQUENCES  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "supabase_auth_admin" IN SCHEMA "auth" GRANT ALL ON SEQUENCES  TO "dashboard_user";



ALTER DEFAULT PRIVILEGES FOR ROLE "supabase_auth_admin" IN SCHEMA "auth" GRANT ALL ON FUNCTIONS  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "supabase_auth_admin" IN SCHEMA "auth" GRANT ALL ON FUNCTIONS  TO "dashboard_user";



ALTER DEFAULT PRIVILEGES FOR ROLE "supabase_auth_admin" IN SCHEMA "auth" GRANT ALL ON TABLES  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "supabase_auth_admin" IN SCHEMA "auth" GRANT ALL ON TABLES  TO "dashboard_user";












ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT SELECT ON SEQUENCES  TO PUBLIC;
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "service_role";






ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO PUBLIC;
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "service_role";






ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT SELECT ON TABLES  TO PUBLIC;
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "service_role";






RESET ALL;
